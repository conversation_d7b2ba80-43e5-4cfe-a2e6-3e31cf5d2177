// tslint:disable:indent array-type
// tslint:disable:no-use-before-declare
// tslint:disable:no-namespace
// @ts-ignore
  import * as ReactJssModule from 'Foundation/ReactJss/client';
// @ts-ignore

  // The _BaseIcon template.
  // Short description:
  // Path: /sitecore/templates/PMI/Base/_BaseIcon
  // ID: a220c0f8-274a-4fc5-9896-6fa249935468
  export interface BaseIconDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Icon field.
    // Short description:
    // Field Type: Image
    // Field ID: 734b447e-44ec-4739-8acc-b89fd51e1fec
    // Custom Data:
    icon: ReactJssModule.ImageField;
    // The IconClass field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 4796a76c-5867-4640-9dca-8bb7a17fb185
    // Custom Data:
    iconClass: ReactJssModule.Item<ReactJssModule.BaseDataSourceItem>;
  }
  // The _BaseTitle template.
  // Short description:
  // Path: /sitecore/templates/PMI/Base/BaseContent/_BaseTitle
  // ID: ab8b7fa5-83b6-4713-8a1e-ff29f10192df
  export interface BaseTitleDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Title field.
    // Short description: Please enter title of the item here.
    // Field Type: Single-Line Text
    // Field ID: 56d15ac6-4a9b-4e75-830c-a30e63384011
    // Custom Data:
    title: ReactJssModule.TextField;
  }
  // The Book template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Products/Product Specific Templates/Book
  // ID: 9bb56de8-091b-440a-856c-cb71437914c3
  export interface BookDataSource extends CommerceProductBaseDataSource {
    // The author_name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 1c6bd3f5-fc36-4d79-af71-a20023887706
    // Custom Data:
    authorName: ReactJssModule.TextField;
    // The book_format field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 55b0d64f-c2b8-4e11-a675-8a19fea7d25b
    // Custom Data:
    bookFormat: ReactJssModule.TextField;
    // The date_published field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: de5684ee-a60b-4c37-9658-d49846ee87ec
    // Custom Data:
    datePublished: ReactJssModule.TextField;
    // The isbn13_number field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: cbccc2bf-cdae-468c-8735-192e5e1a7144
    // Custom Data:
    isbn13Number: ReactJssModule.TextField;
    // The language_format field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 0ea6d4eb-e7da-4c3e-9865-f455700e3d44
    // Custom Data: type=IProductAttribute
    languageFormat: ReactJssModule.Item<ReactJssModule.BaseDataSourceItem>;
    // The number_pages field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 8b2a09e5-b2f2-4df5-899c-d17ebab22f6f
    // Custom Data:
    numberPages: ReactJssModule.TextField;
    // The preorderable field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 4d3b0adb-4bc8-4b8c-9a1a-f8c4c6bcb42f
    // Custom Data:
    preorderable: ReactJssModule.Field<boolean>;
    // The publisher_name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 13e0af40-3c77-42fb-bbba-acfae6ea629f
    // Custom Data:
    publisherName: ReactJssModule.TextField;
    // The subject field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: c7bc10cd-061c-4cef-8293-7b2465712c97
    // Custom Data:
    subject: ReactJssModule.TextField;
    // The tax_class_id field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 74e6405a-e1ea-427d-b2bb-2b2ee111c879
    // Custom Data:
    taxClassId: ReactJssModule.Item<ReactJssModule.BaseDataSourceItem>;
  }
  // The CardType template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Global Settings/CardType
  // ID: ce02bea5-5b2f-43ba-baa0-9abe508a9951
  export interface CardTypeDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Card Description field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: faf342aa-e5dc-4bff-bc77-ff30bf0436a8
    // Custom Data:
    cardDescription: ReactJssModule.TextField;
    // The Card ID field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: edb01ed2-fa72-4fd3-bb0a-48c0ca30947b
    // Custom Data:
    cardID: ReactJssModule.TextField;
  }
  // The CardTypes template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Global Settings/CardTypes
  // ID: 1e2dbf82-addc-4b08-ace9-00771c27818a
  export interface CardTypesDataSource extends ReactJssModule.BaseDataSourceItem {
  }
  // The Certification template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Products/Product Specific Templates/Certification
  // ID: 3fcc6991-b4a3-4221-90e6-202862e4ffc5
  export interface CertificationDataSource extends CommerceProductBaseDataSource {
    // The credential field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 0e324b88-5ccc-4a27-8b9f-d69064a01a7b
    // Custom Data:
    credential: ReactJssModule.Item<ReactJssModule.BaseDataSourceItem>;
    // The examtype field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 312aa28f-5139-48f0-b07d-48169506883e
    // Custom Data:
    examtype: ReactJssModule.Item<ReactJssModule.BaseDataSourceItem>;
    // The ordertype field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 33f47df4-5427-4e88-8441-2f487c66f290
    // Custom Data:
    ordertype: ReactJssModule.Item<ReactJssModule.BaseDataSourceItem>;
    // The retake field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 69dc5886-0d92-4083-88bc-bf3f1164bc1c
    // Custom Data:
    retake: ReactJssModule.Field<boolean>;
    // The studentbundle field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 98598266-c002-4063-8a45-df29bc7fe40e
    // Custom Data:
    studentbundle: ReactJssModule.Field<boolean>;
  }
  // The Chapter template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Products/Product Specific Templates/Chapter
  // ID: 96f5afa4-8b80-483c-98e8-259d646c9eec
  export interface ChapterDataSource extends CommerceProductBaseDataSource {
    // The chapter_city field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: f198a68e-ebcc-4a08-848b-e0f4f8989cbe
    // Custom Data:
    chapterCity: ReactJssModule.TextField;
    // The chapter_code field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: c2e05a55-e899-4a37-b186-91c9c81085f1
    // Custom Data:
    chapterCode: ReactJssModule.TextField;
    // The chapter_email field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 51261ff3-a674-4e10-a34f-5862f78f1d47
    // Custom Data:
    chapterEmail: ReactJssModule.TextField;
    // The chapter_phone field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 25307bf5-cc6d-4d24-a434-348906df7420
    // Custom Data:
    chapterPhone: ReactJssModule.TextField;
    // The chapter_state field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 2343e963-cd03-49b4-bcf0-1e25bc55b102
    // Custom Data:
    chapterState: ReactJssModule.TextField;
    // The charter_status field.
    // Short description:
    // Field Type: Droplink
    // Field ID: e32c2c67-2af2-42ad-ac14-db876b8d93dd
    // Custom Data:
    charterStatus: ReactJssModule.Item<ReactJssModule.BaseDataSourceItem>;
    // The charter_year field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: cd12b58d-91a8-4536-8f3e-2ddb0682783b
    // Custom Data:
    charterYear: ReactJssModule.TextField;
    // The country field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 117d48ee-b23f-49a2-b77d-7b15d5ce3a53
    // Custom Data:
    country: ReactJssModule.Item<ReactJssModule.BaseDataSourceItem>;
    // The region field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 4375df1f-6874-4e61-9709-1c5b065b71ab
    // Custom Data:
    region: ReactJssModule.Item<ReactJssModule.BaseDataSourceItem>;
    // The url field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 3291a8f9-4bcb-42c8-94dc-cc965800854e
    // Custom Data:
    url: ReactJssModule.TextField;
  }
  // The Commerce Category Base template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Categories/Commerce Category Base
  // ID: c68778ab-89f5-42bb-85f6-1ff12dcc7d6c
  export interface CommerceCategoryBaseDataSource extends ProductClassificationDataSource, SearchSettingsDataSource, WithCategoryDataSource {
    // The Display Mode field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: a4367de0-87af-4b95-8390-ca462eae5624
    // Custom Data:
    displayMode: ReactJssModule.TextField;
    // The Include In Menu field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 7f9e9e7f-c2c3-4dbe-b908-147b284ebb5a
    // Custom Data:
    includeInMenu: ReactJssModule.Field<boolean>;
    // The Is Active field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 80a6c060-0bf3-47cd-a964-693e0b4ac075
    // Custom Data:
    isActive: ReactJssModule.Field<boolean>;
    // The Is Deleted field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 4ae86e22-af29-46a5-b026-e147bbd2c4ed
    // Custom Data:
    isDeleted: ReactJssModule.Field<boolean>;
    // The Position field.
    // Short description:
    // Field Type: Integer
    // Field ID: a2eb381f-9240-476c-a9bd-81940c43f14f
    // Custom Data:
    position: ReactJssModule.Field<number>;
  }
  // The Commerce Product Base template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Products/Commerce Product Base
  // ID: f9aec12e-65a5-4e11-82ef-a50e9f200257
  export interface CommerceProductBaseDataSource extends ProductDataSource, WithProductDataSource, ContentPageDataSource, BaseIconDataSource, ProductHierarchyBaseDataSource {
    // The AB Prices field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: 97749ffe-4bd0-4190-abe1-c92c68de1397
    // Custom Data:
    abPrices: ReactJssModule.TextField;
    // The B2b Full Description field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: e919cee7-a090-4848-b062-626a9e3c2ea7
    // Custom Data:
    b2bFullDescription: ReactJssModule.TextField;
    // The B2b image field.
    // Short description:
    // Field Type: Image
    // Field ID: a88b0bd5-dc5c-4049-b3ae-5e48fa881c93
    // Custom Data:
    b2bImage: ReactJssModule.ImageField;
    // The B2b Short Description field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 540b1fca-a499-4ebd-80a0-cf252801e994
    // Custom Data:
    b2bShortDescription: ReactJssModule.TextField;
    // The B2b Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 15389578-4623-4398-901c-e9a7b5533a45
    // Custom Data:
    b2bTitle: ReactJssModule.TextField;
    // The Backorders field.
    // Short description:
    // Field Type: Number
    // Field ID: 6d849509-14d3-492a-b49a-550291a14caa
    // Custom Data:
    backorders: ReactJssModule.Field<number>;
    // The BundleProductOptions field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: 60f860f0-69cb-4834-95a7-1391ba665a59
    // Custom Data:
    bundleProductOptions: ReactJssModule.TextField;
    // The Country Prices field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: 202e0c1e-5500-46be-bb5d-14a400eb6130
    // Custom Data:
    countryPrices: ReactJssModule.TextField;
    // The course_kit_type field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 1e58883a-96ca-4e89-9b68-953a9678734c
    // Custom Data:
    courseKitType: ReactJssModule.Item<ReactJssModule.BaseDataSourceItem>;
    // The course_type field.
    // Short description:
    // Field Type: Multilist
    // Field ID: 5fc60777-de7e-4055-945e-6445f00f41a3
    // Custom Data:
    courseTypes: ReactJssModule.ItemList<ReactJssModule.BaseDataSourceItem>;
    // The custom_cart field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: b8fa0117-1511-40bb-95ec-7b265962cec0
    // Custom Data:
    customCart: ReactJssModule.TextField;
    // The disabled_Product_URL field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 0807e662-73c5-41c4-a975-96fd514e0ff9
    // Custom Data:
    disabledProductURL: ReactJssModule.Field<boolean>;
    // The fulfillment_provider field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 5ca91282-238c-4179-a84b-d7a777753a16
    // Custom Data:
    fulfillmentProvider: ReactJssModule.Item<ReactJssModule.BaseDataSourceItem>;
    // The Global Price field.
    // Short description:
    // Field Type: Number
    // Field ID: 3a732fb5-16ff-448d-932a-143810b5f682
    // Custom Data:
    globalPrice: ReactJssModule.Field<number>;
    // The Group Prices field.
    // Short description:
    // Field Type: Lookup Name Value List
    // Field ID: da043375-9353-44e1-b45c-d4ed88459ebf
    // Custom Data: type=IDictionary<ICustomerGroup, float>
    groupPrices: any;
    // The HeroBackgroundImage field.
    // Short description:
    // Field Type: Image
    // Field ID: 6d95f762-44dd-4123-8b31-36a03113d48a
    // Custom Data:
    heroBackgroundImage: ReactJssModule.ImageField;
    // The HeroBackgroundImageMobile field.
    // Short description:
    // Field Type: Image
    // Field ID: daf9a28d-b84c-4a67-8595-a297c050909f
    // Custom Data:
    heroBackgroundImageMobile: ReactJssModule.ImageField;
    // The HeroImage field.
    // Short description:
    // Field Type: Image
    // Field ID: 5a0d0f52-211e-4e3c-ab2f-71726cbd9057
    // Custom Data:
    heroImage: ReactJssModule.ImageField;
    // The Image field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: c89f0ba8-67f4-44f6-a7f1-f68a46cdbcb6
    // Custom Data:
    image: ReactJssModule.TextField;
    // The included_in_membership field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: eb7fc950-cb4c-41e4-82d1-747429338465
    // Custom Data:
    includedInMembership: ReactJssModule.Field<boolean>;
    // The Is Deleted field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 2aaa5d2e-4301-40ad-b6f9-d752c5fad3e3
    // Custom Data:
    isDeleted: ReactJssModule.Field<boolean>;
    // The Is Featured field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 3bfaed00-9aab-42c8-9fb7-8e8d1f0027a6
    // Custom Data:
    isFeatured: ReactJssModule.Field<boolean>;
    // The Is In Stock field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: bb29b6b4-3456-4d3a-94db-b02656ca47a3
    // Custom Data:
    isInStock: ReactJssModule.Field<boolean>;
    // The is_membership field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 47e537b2-9d39-47db-81f7-295157ed880e
    // Custom Data:
    isMembership: ReactJssModule.Field<boolean>;
    // The Is Returnable field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: cae45dfb-0c62-4857-a91a-b72b50354c7d
    // Custom Data:
    isReturnable: ReactJssModule.TextField;
    // The is_subscription_product field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 4b7c5ef9-08c0-41c5-aa41-278641707375
    // Custom Data:
    isSubscriptionProduct: ReactJssModule.Field<boolean>;
    // The language field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 595359da-ec9f-4c4c-a6a2-7546c7ef1240
    // Custom Data:
    language: ReactJssModule.Item<ReactJssModule.BaseDataSourceItem>;
    // The membershipTextHighlightColor field.
    // Short description:
    // Field Type: Droplink
    // Field ID: b18d1660-c856-4d2f-8e3d-c676d4a7421e
    // Custom Data:
    membershipTextHighlightColor: ReactJssModule.Item<ReactJssModule.BaseDataSourceItem>;
    // The OverviewDescription field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: df536977-1ee5-47e9-b7f6-11f25a9d9330
    // Custom Data:
    overviewDescription: ReactJssModule.TextField;
    // The pmi_catalog field.
    // Short description:
    // Field Type: Multilist
    // Field ID: 8e050945-712f-407c-8c38-8835b5488376
    // Custom Data:
    pmiCatalogs: ReactJssModule.ItemList<ReactJssModule.BaseDataSourceItem>;
    // The PMI Membership field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 50845396-2667-4e2a-a548-93c81fc48043
    // Custom Data:
    pmiMembership: ReactJssModule.Item<ReactJssModule.BaseDataSourceItem>;
    // The pmi_product_category field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 3ce1903b-97fa-4f44-9395-4871dd6b91e0
    // Custom Data:
    pmiProductCategory: ReactJssModule.Item<ReactJssModule.BaseDataSourceItem>;
    // The pmi_product_family field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 8a3f0338-265e-445d-8d06-47fb117c7bc4
    // Custom Data:
    pmiProductFamily: ReactJssModule.Item<ReactJssModule.BaseDataSourceItem>;
    // The pmi_product_format field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 8f191f3e-4c52-4091-a6dd-78915bc7af3a
    // Custom Data:
    pmiProductFormat: ReactJssModule.Item<ReactJssModule.BaseDataSourceItem>;
    // The pmi_product_id field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: aa683096-5550-4fee-bf74-ffa9a08957bc
    // Custom Data:
    pmiProductId: ReactJssModule.TextField;
    // The pmi_product_type field.
    // Short description:
    // Field Type: Droplink
    // Field ID: fdb83207-3c1c-4566-ba47-d28568bbdeba
    // Custom Data:
    pmiProductType: ReactJssModule.Item<ReactJssModule.BaseDataSourceItem>;
    // The PMI Segment field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 3b0804fc-df69-4424-9d09-892341852d72
    // Custom Data:
    pmiSegment: ReactJssModule.Item<ReactJssModule.BaseDataSourceItem>;
    // The ProductCustomOptions field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: 3350a005-c47e-461c-b6ce-17e223960c71
    // Custom Data:
    productCustomOptions: ReactJssModule.TextField;
    // The Product Kind field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 3d5ca766-9602-4a5b-aff6-8fcb50fca321
    // Custom Data: nullable=true
    productKind: ReactJssModule.Item<ReactJssModule.BaseDataSourceItem>;
    // The Product Store field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 8e4499d7-9df2-4e4a-a170-3342dc989426
    // Custom Data: nullable=true&type=IProductStore
    productStore: ReactJssModule.Item<ReactJssModule.BaseDataSourceItem>;
    // The PromoBadges field.
    // Short description:
    // Field Type: Multilist
    // Field ID: 23ee2ae2-1d4d-4e51-8e2a-b251e55e8a0d
    // Custom Data:
    promoBadges: ReactJssModule.ItemList<ReactJssModule.BaseDataSourceItem>;
    // The Quantity field.
    // Short description:
    // Field Type: Number
    // Field ID: 549f9561-22be-4070-84f1-c82639c08c6d
    // Custom Data:
    quantity: ReactJssModule.Field<number>;
    // The RecommendedAltText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 3568a091-d725-4a13-9b45-fb21802c9ca3
    // Custom Data:
    recommendedAltText: ReactJssModule.TextField;
    // The Renewal Price field.
    // Short description:
    // Field Type: Number
    // Field ID: b3f78b0d-bf55-4b73-b003-dbafdb00ccb3
    // Custom Data:
    renewalPrice: ReactJssModule.Field<number>;
    // The SeoFriendlyTitle field.
    // Short description: This will be used in the URL path
    // Field Type: Single-Line Text
    // Field ID: 260a336f-7541-4b8c-a023-3198ca421c86
    // Custom Data:
    seoFriendlyTitle: ReactJssModule.TextField;
    // The showCTA field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 402d02c0-c629-4592-b316-2d1cfd687197
    // Custom Data:
    showCTA: ReactJssModule.Field<boolean>;
    // The showPrices field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 8318d912-a453-4e22-8ca2-6ddbc96c7646
    // Custom Data:
    showPrices: ReactJssModule.Field<boolean>;
    // The single_membership field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 45cef470-41fe-48e2-835d-f7cc180fa1a5
    // Custom Data:
    singleMembership: ReactJssModule.Field<boolean>;
    // The Small Image field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: f482cd3c-6b97-48c3-ab80-a898ca3f874b
    // Custom Data:
    smallImage: ReactJssModule.TextField;
    // The sold_as_bundle field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 42c17217-ecc2-4d07-8d35-c5913a94d788
    // Custom Data:
    soldAsBundle: ReactJssModule.Field<boolean>;
    // The subscription_frequency field.
    // Short description:
    // Field Type: Droplink
    // Field ID: c2ddffb8-5490-4ebf-ad2e-1b220a466c5a
    // Custom Data: nullable=true&type=IProductAttributeValue
    subscriptionFrequency: ReactJssModule.Item<ReactJssModule.BaseDataSourceItem>;
    // The Thumbnail field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 2e2436e4-e5c4-4a6c-909e-1eb7931cb2d9
    // Custom Data:
    thumbnail: ReactJssModule.TextField;
    // The UseDarkThemeForHero field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 7bdf557d-6804-49e5-acc8-a1835731babc
    // Custom Data:
    useDarkThemeForHero: ReactJssModule.Field<boolean>;
    // The Weight field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: f3ef8df0-e281-4d4e-bfba-7d950d517170
    // Custom Data:
    weight: ReactJssModule.TextField;
    // The Wordmark field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 1fc37bfb-e2f6-4337-b793-b12477c305bd
    // Custom Data:
    wordmark: ReactJssModule.Item<ReactJssModule.BaseDataSourceItem>;
    // The Zone Prices field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: babb6e53-3777-4916-a764-01aad64cce60
    // Custom Data:
    zonePrices: ReactJssModule.TextField;
  }
  // The Commerce Product Type Base template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Product Types/Commerce Product Type Base
  // ID: d46f0f09-ee8e-44e6-a425-98d246f9a0c9
  export interface CommerceProductTypeBaseDataSource extends ProductTypeDataSource {
  }
  // The Connect Settings template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Settings/Connect Settings
  // ID: adf5c892-b08b-4294-9e28-da612b5fae41
  export interface ConnectSettingsDataSource extends ReactJssModule.BaseDataSourceItem {
  }
  // The Content Page template.
  // Short description:
  // Path: /sitecore/templates/Foundation/BaseModels/Content Page
  // ID: 7e181119-46dd-489c-9fdf-0c9dc89141c2
  export interface ContentPageDataSource extends BaseTitleDataSource {
    // The Short Title field.
    // Short description: Please enter text of the item here.
    // Field Type: Single-Line Text
    // Field ID: e27e197c-ad78-4352-bb04-cb7da291a88e
    // Custom Data:
    shortTitle: ReactJssModule.TextField;
  }
  // The Countries template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Global Settings/Countries
  // ID: d7640afe-63c0-4cdf-876f-c17c5d3fa5c4
  export interface CountriesDataSource extends ReactJssModule.BaseDataSourceItem {
  }
  // The Country template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Global Settings/Country
  // ID: 45537cd4-5252-4436-8b1a-82c1c7c8fd90
  export interface CountryDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Code field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: b9203627-da88-48d6-b617-082458ac0e7c
    // Custom Data:
    code: ReactJssModule.TextField;
    // The Code3 field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 61fe24c7-974b-493e-a16d-e1dddbbca3ee
    // Custom Data:
    code3: ReactJssModule.TextField;
    // The Name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: bf30b4a3-0f21-4946-87c0-de453d9aa6fa
    // Custom Data:
    name: ReactJssModule.TextField;
  }
  // The Currencies template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Global Settings/Currencies
  // ID: eba94d27-11ce-4bc2-a537-a1855aa0c20f
  export interface CurrenciesDataSource extends ReactJssModule.BaseDataSourceItem {
  }
  // The Currency template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Global Settings/Currency
  // ID: b5571a56-037b-48ff-b317-68e10a631079
  export interface CurrencyDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Allowed Cards field.
    // Short description:
    // Field Type: Multilist
    // Field ID: d9dc15f3-ae68-4ee8-82f7-d54e63cb6215
    // Custom Data: type=Pmi.Spx.Foundation.Connect.TemplateModels.ICardType
    allowedCards: ReactJssModule.ItemList<ReactJssModule.BaseDataSourceItem>;
    // The Country field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 6a85eea1-6911-4661-bb68-e297057568d4
    // Custom Data: type=Pmi.Spx.Foundation.Connect.TemplateModels.ICountry
    country: ReactJssModule.Item<ReactJssModule.BaseDataSourceItem>;
    // The Currency Code field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 6fe1ebdc-6340-4c66-bfda-4eb65f155180
    // Custom Data:
    currencyCode: ReactJssModule.TextField;
    // The CurrencyDecimalPlaces field.
    // Short description:
    // Field Type: Integer
    // Field ID: c16357b6-0ea4-4067-bff6-045685442ec5
    // Custom Data:
    currencyDecimalPlaces: ReactJssModule.Field<number>;
    // The Currency Name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: bea10233-e1b8-4651-b45f-90e9a4f4171a
    // Custom Data:
    currencyName: ReactJssModule.TextField;
    // The Currency Symbol field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: c0163f1f-9646-4d2f-82da-059d13b67b71
    // Custom Data:
    currencySymbol: ReactJssModule.TextField;
  }
  // The Customer Group template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Customer Groups/Customer Group
  // ID: 12a53e3f-4e34-4a89-b308-4c0592139498
  export interface CustomerGroupDataSource extends SynchronizationDataSource {
    // The Code field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: e4b8bb8a-b700-4b11-b4b5-37e7c0dea2eb
    // Custom Data:
    code: ReactJssModule.TextField;
  }
  // The Customer Groups template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Customer Groups/Customer Groups
  // ID: 4483c063-f8b4-42f9-8575-96413c8556b9
  export interface CustomerGroupsDataSource extends ReactJssModule.BaseDataSourceItem {
  }
  // The Digital Product Base template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Products/Digital Product Base
  // ID: 559d3674-f2c5-40ba-9db5-da037785e2b6
  export interface DigitalProductBaseDataSource extends ProductHierarchyBaseDataSource {
    // The AssociatedCertifications field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: f3c0f88e-5f66-499b-b927-a75e44f4f991
    // Custom Data:
    associatedCertifications: ReactJssModule.TextField;
    // The Byline field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 94b3b86b-1e81-4469-aa6f-19b529c84599
    // Custom Data:
    byline: ReactJssModule.TextField;
    // The ceu_credit_value field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 79e27f62-fe68-4e2c-a6aa-e2c1fca27235
    // Custom Data:
    ceuCreditValue: ReactJssModule.TextField;
    // The level_of_course field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 812400f2-0248-4b8e-8b38-bc822624009a
    // Custom Data: type=Pmi.Spx.Foundation.Connect.TemplateModels.IProductAttributeValue&name=CourseLevel
    courseLevel: ReactJssModule.Item<ReactJssModule.BaseDataSourceItem>;
    // The language_format field.
    // Short description:
    // Field Type: Multilist
    // Field ID: 75c31d86-c4e7-4f60-b89a-446095adfbe9
    // Custom Data: type=Pmi.Spx.Foundation.Connect.TemplateModels.IProductAttributeValue
    languageFormats: ReactJssModule.ItemList<ReactJssModule.BaseDataSourceItem>;
    // The leadership_pdus field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 76fc17bc-5a80-4ab5-bab3-d351b135e8e2
    // Custom Data:
    leadershipPdus: ReactJssModule.TextField;
    // The length_of_course field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: bff354fa-9d7b-40a9-8f1d-6e82f2e691de
    // Custom Data:
    lengthOfCourse: ReactJssModule.TextField;
    // The pdus field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: b888b428-2677-4008-9d1d-9667886a97a6
    // Custom Data:
    pdus: ReactJssModule.TextField;
    // The strategic_business_pdus field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: eb492c8b-c4d1-44b3-a223-789c1432ca41
    // Custom Data:
    strategicBusinessPdus: ReactJssModule.TextField;
    // The technical_pdus field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 77d189fe-5ede-4a12-8da1-a9fc11c5ec41
    // Custom Data:
    technicalPdus: ReactJssModule.TextField;
  }
  // The Digital Product template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Products/Product Specific Templates/Digital Product
  // ID: a7ef4f24-c310-436e-968b-831eacf63ad3
  export interface DigitalProductDataSource extends CommerceProductBaseDataSource, DigitalProductBaseDataSource {
    // The details field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: fdcff921-b60c-44c5-9d68-86ff124083c1
    // Custom Data:
    details: ReactJssModule.TextField;
  }
  // The Division template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Division
  // ID: c715d20b-2e73-4046-9b87-749a11c27c5c
  export interface DivisionDataSource extends SynchronizationDataSource {
    // The Description field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: dd8ff6da-9e67-4b19-8409-58f4b271c14a
    // Custom Data:
    description: ReactJssModule.TextField;
    // The Name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: b6c749f8-d9d7-477a-8c02-58914020fa6f
    // Custom Data:
    name: ReactJssModule.TextField;
  }
  // The Divisions template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Divisions
  // ID: bf890455-a0ae-4502-8619-c952594605cb
  export interface DivisionsDataSource extends ReactJssModule.BaseDataSourceItem {
  }
  // The Donation template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Products/Product Specific Templates/Donation
  // ID: d97497b8-fffe-4226-9613-35bca3737373
  export interface DonationDataSource extends CommerceProductBaseDataSource {
    // The donation_fixed_amount field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 95ceafa0-7690-4aea-a9c6-e8eddfb3e047
    // Custom Data:
    donationFixedAmount: ReactJssModule.TextField;
    // The donation_max_amount field.
    // Short description:
    // Field Type: Number
    // Field ID: 9708ab3d-47e5-4a2e-894d-af91d7f90060
    // Custom Data:
    donationMaxAmount: ReactJssModule.Field<number>;
    // The experius_donation_min_amount field.
    // Short description:
    // Field Type: Number
    // Field ID: a9083ea4-d44d-4fd1-9de1-75fe0c136705
    // Custom Data:
    experiusDonationMinAmount: ReactJssModule.Field<number>;
  }
  // The eBook template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Products/Product Specific Templates/eBook
  // ID: b4ba8998-578d-4854-9044-8f4105fa3e79
  export interface EBookDataSource extends CommerceProductBaseDataSource {
    // The author_name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: cf4dc6b5-5c66-4207-8768-55cb951e75ff
    // Custom Data:
    authorName: ReactJssModule.TextField;
    // The book_format field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: f923e5e9-85f8-4eba-b7e4-3378aea4e1cc
    // Custom Data:
    bookFormat: ReactJssModule.TextField;
    // The date_published field.
    // Short description:
    // Field Type: Date
    // Field ID: 108d89d0-1622-4eb1-b832-726a61252f7b
    // Custom Data:
    datePublished: ReactJssModule.Field<Date>;
    // The eisbn13_number field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 2b264b1a-0bfc-466a-9d21-303a0685995a
    // Custom Data:
    eisbn13Number: ReactJssModule.TextField;
    // The language_format field.
    // Short description:
    // Field Type: Droplink
    // Field ID: b06f57ae-6094-4b6d-9869-7f0cd6eef0a5
    // Custom Data: type=IProductAttribute
    languageFormat: ReactJssModule.Item<ReactJssModule.BaseDataSourceItem>;
    // The preorderable field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 21fc67d8-861c-4c18-953a-699cb1364729
    // Custom Data:
    preorderable: ReactJssModule.Field<boolean>;
    // The publisher_name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: fa54c689-092a-44ed-89f2-5c1593948268
    // Custom Data:
    publisherName: ReactJssModule.TextField;
    // The subject field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 3882e089-90d3-426e-ad76-5174c4f733fa
    // Custom Data:
    subject: ReactJssModule.TextField;
    // The tax_class_id field.
    // Short description:
    // Field Type: Droplink
    // Field ID: fe5d416d-bc66-4814-b17c-9313a141091d
    // Custom Data:
    taxClassId: ReactJssModule.Item<ReactJssModule.BaseDataSourceItem>;
  }
  // The eLearning template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Products/Product Specific Templates/eLearning
  // ID: c4a2ccf8-a341-40bc-9170-ed121809fda4
  export interface ELearningDataSource extends CommerceProductBaseDataSource, DigitalProductBaseDataSource {
    // The format field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 2cd04308-3313-487e-b75a-082856ba27ff
    // Custom Data:
    format: ReactJssModule.TextField;
    // The lms_code field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 2bc3c5d2-d436-4f8f-a549-57865dca4858
    // Custom Data:
    lmsCode: ReactJssModule.TextField;
    // The OverviewLanguages field.
    // Short description: Language / SKU Mapping
    // Field Type: Lookup Name Value List
    // Field ID: 91df15c9-cdb4-4eda-8429-3ee46a99aeb6
    // Custom Data:
    overviewLanguages: any;
    // The pmi_elearning_provider field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 0c4a8b9e-f2d9-4075-8fd6-6fb7d1cde018
    // Custom Data: type=Pmi.Spx.Foundation.Connect.TemplateModels.IProductAttribute
    pmiElearningProvider: ReactJssModule.Item<ReactJssModule.BaseDataSourceItem>;
    // The who_should_attend field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: fe9e9252-e629-496f-805d-5cb1a309c2bd
    // Custom Data:
    whoShouldAttend: ReactJssModule.TextField;
  }
  // The Event template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Products/Product Specific Templates/Event
  // ID: 0c5fe585-60fc-4da3-80d8-e56c86e89611
  export interface EventDataSource extends CommerceProductBaseDataSource {
    // The event_dates field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 5803bf63-979a-43c8-8e6e-d4dafb2d9bd7
    // Custom Data:
    eventDates: ReactJssModule.TextField;
    // The event_email field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 76063bd5-e959-426a-9cd1-6ea09a0f1d66
    // Custom Data:
    eventEmail: ReactJssModule.TextField;
    // The event_location field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 651a8e42-3a06-421f-9e65-10cf228b1228
    // Custom Data:
    eventLocation: ReactJssModule.TextField;
    // The event_name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: c27a0fd2-544b-44c9-9450-2f499b187db1
    // Custom Data:
    eventName: ReactJssModule.TextField;
    // The event_policy field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 35e3b570-175d-4505-a941-7f390f1a400f
    // Custom Data:
    eventPolicy: ReactJssModule.TextField;
    // The event_tickets field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 0cced7d8-1cef-4270-a88c-52f3f0f4e639
    // Custom Data:
    eventTickets: ReactJssModule.TextField;
    // The target_audience field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: e4019318-a45c-4dc5-931e-c879be4cd2ea
    // Custom Data:
    targetAudience: ReactJssModule.TextField;
  }
  // The Facet template.
  // Short description:
  // Path: /sitecore/templates/System/Item Buckets/Facet
  // ID: 5c125b6d-c481-4c24-b5b9-9a23fe396bf0
  export interface FacetDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Client Side Handle field.
    // Short description: 保留以供将来使用
    // Field Type: Multi-Line Text
    // Field ID: 7f42ed80-33f0-4c98-a1e7-b15be7551c36
    // Custom Data:
    clientSideHandle: ReactJssModule.TextField;
    // The DisplayName field.
    // Short description: 应使用更加用户友好的名称或进行本地化
    // Field Type: Single-Line Text
    // Field ID: 5de17054-0141-4e58-935b-fec623a60806
    // Custom Data:
    displayName: ReactJssModule.TextField;
    // The Enabled field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 3d0fe806-d0e3-4254-96be-a60899c7d2e6
    // Custom Data:
    enabled: ReactJssModule.Field<boolean>;
    // The Global field.
    // Short description: 是否要在每一搜索的每一节点中搜索此 Facet
    // Field Type: Checkbox
    // Field ID: 182bd0f4-c902-4a19-a1ba-d189caa18e1b
    // Custom Data:
    global: ReactJssModule.Field<boolean>;
    // The Minimum Number of Items field.
    // Short description: Facet 出现在搜索结果中之前必须显示的最少节点数
    // Field Type: Number
    // Field ID: 8a3ac1b8-8cc1-4dd4-8766-9803f1601741
    // Custom Data:
    minimumNumberOfItems: ReactJssModule.Field<number>;
    // The Name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 31ca077f-ef38-4980-8a24-05408031b2bc
    // Custom Data:
    name: ReactJssModule.TextField;
    // The Parameters field.
    // Short description: 索引中使用的字段小写名称，facet 以此为基础。您可以输入用逗号分隔的多个字段名称 (title,author)
    // Field Type: Single-Line Text
    // Field ID: 0e23bb51-c071-41b5-9d83-ac410b89b85a
    // Custom Data:
    parameters: ReactJssModule.TextField;
    // The Type field.
    // Short description: 实施 ISimpleFacet 的类
    // Field Type: Single-Line Text
    // Field ID: fae92c1e-9e2c-4209-89eb-c256d55e3d7f
    // Custom Data:
    type: ReactJssModule.TextField;
  }
  // The Global Connect Settings template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Global Settings/Global Connect Settings
  // ID: 62d3fd7b-1c19-4428-8ad6-b107cca460ee
  export interface GlobalConnectSettingsDataSource extends ReactJssModule.BaseDataSourceItem {
  }
  // The Identification Type template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Identification Type
  // ID: 0a5feaa1-1b35-441a-9cc0-8fc259a15213
  export interface IdentificationTypeDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Description field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: dada97b4-46a4-4f5f-a96f-64fde1e9d837
    // Custom Data:
    description: ReactJssModule.TextField;
    // The Name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 6cbb88aa-3a9f-4769-9996-6ed459bed324
    // Custom Data:
    name: ReactJssModule.TextField;
  }
  // The Identification Types template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Identification Types
  // ID: 29fe9b4e-bda0-420f-97ca-ce1421cd9cc8
  export interface IdentificationTypesDataSource extends ReactJssModule.BaseDataSourceItem {
  }
  // The Lookup Value template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Lookup Value
  // ID: b95d069c-7867-4140-816d-6b204949b522
  export interface LookupValueDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Description field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 7a3b32c4-ceaa-4759-9e6c-f2012fc40c91
    // Custom Data:
    description: ReactJssModule.TextField;
    // The Short field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: d8034d94-f87f-47e2-a884-f11abee21138
    // Custom Data:
    short: ReactJssModule.TextField;
  }
  // The Lookup Values template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Lookup Values
  // ID: ab6feece-afa4-49b8-b96d-f15e28eaefcd
  export interface LookupValuesDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Description field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 545f0965-e7a6-4231-9a13-4f7ff48c006f
    // Custom Data:
    description: ReactJssModule.TextField;
  }
  // The Manufacturer template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Manufacturer
  // ID: 8ecdc0a6-3a85-4f89-8f49-8a53aa75595e
  export interface ManufacturerDataSource extends SynchronizationDataSource {
    // The Description field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 465f8abe-79fb-4c68-8dfa-5890bb7f2a60
    // Custom Data:
    description: ReactJssModule.TextField;
    // The Name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: eca748ca-62b5-4f86-a213-796b668a0ad8
    // Custom Data:
    name: ReactJssModule.TextField;
    // The ProductURLMacro field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 991e7d30-115b-444a-bfd5-3831727e78e1
    // Custom Data:
    productURLMacro: ReactJssModule.TextField;
    // The WebsiteUrl field.
    // Short description:
    // Field Type: General Link
    // Field ID: 3a6ff55f-4354-46f2-bf70-d6550d834922
    // Custom Data:
    websiteUrl: ReactJssModule.LinkField;
  }
  // The Manufacturers template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Manufacturers
  // ID: 6addbd3c-6a82-4361-a361-6c89b4cb05b2
  export interface ManufacturersDataSource extends ReactJssModule.BaseDataSourceItem {
  }
  // The Membership template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Products/Product Specific Templates/Membership
  // ID: 1193007d-5ebf-4b23-abb5-cbd5585c58e5
  export interface MembershipDataSource extends CommerceProductBaseDataSource {
    // The is_studentmembership field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 3aa37d98-25e5-4a3c-bc22-0e2d00279046
    // Custom Data:
    isStudentmembership: ReactJssModule.Field<boolean>;
    // The opt_in_for_auto_renew field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 43df5139-0631-403f-aae2-569ac7354496
    // Custom Data:
    optInForAutoRenew: ReactJssModule.Field<boolean>;
    // The validation field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: e81e4e50-0cf3-4042-95f3-644f3fe01ee1
    // Custom Data:
    validation: ReactJssModule.Field<boolean>;
  }
  // The Product Attribute template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Product Attributes/Product Attribute
  // ID: a876e3f3-e9c2-43ac-9d05-3068ccbf9a3c
  export interface ProductAttributeDataSource extends SynchronizationDataSource {
    // The Code field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: fc03e427-2d6e-4bc2-baee-a7bd00f743d3
    // Custom Data:
    code: ReactJssModule.TextField;
  }
  // The Product Attributes template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Product Attributes/Product Attributes
  // ID: 472bc7d1-3730-4cf6-acab-65b015c9184c
  export interface ProductAttributesDataSource extends ReactJssModule.BaseDataSourceItem {
  }
  // The Product Attribute Value template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Product Attributes/Product Attribute Value
  // ID: 5fa8a8cc-aaa9-4fb2-898a-7d0b79dcd4c1
  export interface ProductAttributeValueDataSource extends SynchronizationDataSource {
    // The Label field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 0ac1ed9b-d768-4c4a-90e1-2d766a20df67
    // Custom Data:
    label: ReactJssModule.TextField;
  }
  // The Product Classification template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Product Classification
  // ID: d5b1659a-83e7-485f-ad9a-555eecb564bf
  export interface ProductClassificationDataSource extends SynchronizationDataSource {
    // The Description field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 3e873f0e-4a03-4169-91af-fae982f9cbe3
    // Custom Data:
    description: ReactJssModule.TextField;
    // The ExternalParentID field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 008f1da3-097e-4fec-8322-9303308c63ea
    // Custom Data:
    externalParentID: ReactJssModule.TextField;
    // The Name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 6a65dfc6-6a35-4e8d-93ea-d1d4f40711c2
    // Custom Data:
    name: ReactJssModule.TextField;
  }
  // The Product ClassificationGroup template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Product ClassificationGroup
  // ID: cf8fec2c-96fc-46c8-8a49-7bf908482f4b
  export interface ProductClassificationGroupDataSource extends SynchronizationDataSource {
    // The Description field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: d918b5f7-75e9-4345-913c-b79e1171cc42
    // Custom Data:
    description: ReactJssModule.TextField;
    // The Name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 44c7d49f-0c37-4a56-b135-aa831e7f1eb9
    // Custom Data:
    name: ReactJssModule.TextField;
  }
  // The Product Classifications template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Product Classifications
  // ID: 89c74115-3e03-4d1d-bd99-29aafcf9a649
  export interface ProductClassificationsDataSource extends SynchronizationDataSource {
    // The Description field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 645563ed-597b-4095-acc8-eda71049c3d3
    // Custom Data:
    description: ReactJssModule.TextField;
    // The Name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 3308641b-eb85-4fe7-8d0d-543e5b7ea4b6
    // Custom Data:
    name: ReactJssModule.TextField;
  }
  // The Product Classifications Specification template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Product Classifications Specification
  // ID: 9ca86513-c986-4bf3-9f9c-9791aa7ea352
  export interface ProductClassificationsSpecificationDataSource extends ProductSpecificationDataSource {
  }
  // The Product template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Product
  // ID: 47d1a39e-3b4b-4428-a9f8-b446256c9581
  export interface ProductDataSource extends SynchronizationDataSource {
    // The BrandName field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 8f6025b8-1039-46ab-a606-849bfe0c6292
    // Custom Data:
    brandName: ReactJssModule.TextField;
    // The Divisions field.
    // Short description:
    // Field Type: Treelist
    // Field ID: 0f949b65-ee6c-4d70-b692-54d801ba812c
    // Custom Data:
    divisions: ReactJssModule.ItemList<ReactJssModule.BaseDataSourceItem>;
    // The Full Description field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 25fe6930-d1f7-4924-95f9-770aa320a1f9
    // Custom Data:
    fullDescription: ReactJssModule.TextField;
    // The Identification field.
    // Short description:
    // Field Type: Name Lookup Value List
    // Field ID: 89d22f6f-409f-4463-bb09-0a79f29b9707
    // Custom Data:
    identification: { } /* UNKNOWN TYPE: name lookup value list */;
    // The Manufacturer field.
    // Short description:
    // Field Type: Treelist with Search
    // Field ID: ea45edf7-cd8b-433f-99a6-ebfe3c7ce3a5
    // Custom Data:
    manufacturer: ReactJssModule.ItemList<ReactJssModule.BaseDataSourceItem>;
    // The ModelName field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 7b3fad93-dcb0-4d8d-8181-13738502bea5
    // Custom Data:
    modelName: ReactJssModule.TextField;
    // The Name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: e2cea8d5-65f4-4789-a778-b1b400b56928
    // Custom Data:
    name: ReactJssModule.TextField;
    // The ProductClasses field.
    // Short description:
    // Field Type: Treelist
    // Field ID: 891039c9-fe24-46b6-a5de-ba958a6925cf
    // Custom Data:
    productClasses: ReactJssModule.ItemList<ReactJssModule.BaseDataSourceItem>;
    // The ProductType field.
    // Short description:
    // Field Type: Treelist with Search
    // Field ID: d5806f22-e082-4c82-87bd-439a62ef92c6
    // Custom Data: type=IProductType
    productType: ReactJssModule.ItemList<ReactJssModule.BaseDataSourceItem>;
    // The Short Description field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 87cf462a-a37a-4731-bf0b-370d3d7a9873
    // Custom Data:
    shortDescription: ReactJssModule.TextField;
  }
  // The Product Global Specification template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Product Global Specification
  // ID: 157c6369-db59-4299-8238-365d964c0965
  export interface ProductGlobalSpecificationDataSource extends ProductSpecificationDataSource {
  }
  // The Product Hierarchy Base template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Products/Product Hierarchy Base
  // ID: 8dd26570-494d-4867-9ff6-c15e56e8feca
  export interface ProductHierarchyBaseDataSource extends ReactJssModule.BaseDataSourceItem {
    // The level_2_hierarchy field.
    // Short description:
    // Field Type: Multilist
    // Field ID: 691c85ee-9f50-4dfd-b3af-a6c0d1d676c9
    // Custom Data: type=Pmi.Spx.Foundation.Connect.TemplateModels.IProductAttributeValue
    level2Hierarchies: ReactJssModule.ItemList<ReactJssModule.BaseDataSourceItem>;
    // The level_3_hierarchy field.
    // Short description:
    // Field Type: Multilist
    // Field ID: d3a4b7de-eafd-4b13-8df7-67c7220dbdda
    // Custom Data: type=Pmi.Spx.Foundation.Connect.TemplateModels.IProductAttributeValue
    level3Hierarchies: ReactJssModule.ItemList<ReactJssModule.BaseDataSourceItem>;
    // The level_4_hierarchy field.
    // Short description:
    // Field Type: Multilist
    // Field ID: b695c62a-0edd-4951-bd55-024699c1b8ad
    // Custom Data: type=Pmi.Spx.Foundation.Connect.TemplateModels.IProductAttributeValue
    level4Hierarchies: ReactJssModule.ItemList<ReactJssModule.BaseDataSourceItem>;
  }
  // The Product Kind template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Product Kinds/Product Kind
  // ID: 4442ed16-6ad0-483f-8fb1-6e43de7a9ee7
  export interface ProductKindDataSource extends SynchronizationDataSource {
    // The Name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 3fd3f21f-6e3c-4c34-a7c6-71f20fb71550
    // Custom Data:
    name: ReactJssModule.TextField;
  }
  // The Product Kinds template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Product Kinds/Product Kinds
  // ID: f106457b-dc94-4da8-a5fa-4944f1d56017
  export interface ProductKindsDataSource extends ReactJssModule.BaseDataSourceItem {
  }
  // The Product Matching template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Products/Product Matching
  // ID: d185e14a-37bc-4cad-97af-2d34829ee819
  export interface ProductMatchingDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Product Kind field.
    // Short description:
    // Field Type: Multilist
    // Field ID: d2e0628f-f47f-479c-ad48-5fc7ce2699f0
    // Custom Data: name=ProductKind
    productKind: ReactJssModule.ItemList<ReactJssModule.BaseDataSourceItem>;
    // The Product Type field.
    // Short description:
    // Field Type: Multilist
    // Field ID: b360298f-10b6-406e-a8ee-e3c6dcc2b8ab
    // Custom Data: name=ProductType
    productType: ReactJssModule.ItemList<ReactJssModule.BaseDataSourceItem>;
  }
  // The Product Page template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Product Pages/Product Page
  // ID: 2838844b-92ab-4bd4-a349-25a521a16f99
  export interface ProductPageDataSource extends WithCategoryDataSource, WithProductDataSource, ProductMatchingDataSource {
  }
  // The Product Relation template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Product Relation
  // ID: 93703013-77b6-48b5-8338-c29a5d15a1a6
  export interface ProductRelationDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Description field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 609c7279-6e83-4ed2-9f7e-7e9bef1397f0
    // Custom Data:
    description: ReactJssModule.TextField;
    // The Name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 1ac1bfa4-4113-4c66-afe2-f3f3c0266c73
    // Custom Data:
    name: ReactJssModule.TextField;
    // The RelatedProducts field.
    // Short description:
    // Field Type: Multilist with Search
    // Field ID: b039e503-5eaf-4688-80a1-b9f32de6d800
    // Custom Data:
    relatedProducts: ReactJssModule.ItemList<ReactJssModule.BaseDataSourceItem>;
    // The Type field.
    // Short description:
    // Field Type: Droplist
    // Field ID: 9d507af0-463a-4151-aaa2-8c5ad6632e36
    // Custom Data:
    type: ReactJssModule.TextField;
  }
  // The Product Relations template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Product Relations
  // ID: 6e5fbb20-2d51-4b63-8563-65e6e8c61dfd
  export interface ProductRelationsDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Description field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 62c925b2-b720-465b-8047-e776be22bd79
    // Custom Data:
    description: ReactJssModule.TextField;
  }
  // The Product Repository template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Product Repository
  // ID: f599bf48-d6fe-40dc-9f78-cf2d56bfb657
  export interface ProductRepositoryDataSource extends ReactJssModule.BaseDataSourceItem {
  }
  // The Product Resource template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Product Resource
  // ID: 0d52eb5c-a153-4688-a217-151a17de321e
  export interface ProductResourceDataSource extends SynchronizationDataSource {
    // The Resource field.
    // Short description:
    // Field Type: Image
    // Field ID: fef7847e-3ac3-4c79-be15-bf407c461de6
    // Custom Data:
    resource: ReactJssModule.ImageField;
    // The Type field.
    // Short description:
    // Field Type: Droplist
    // Field ID: 10870bdb-03dc-4eb0-922e-c891a3b96f37
    // Custom Data:
    type: ReactJssModule.TextField;
    // The URI field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: d2a79cb7-a015-4ba4-b63d-beb127bf5f0e
    // Custom Data:
    uri: ReactJssModule.TextField;
  }
  // The Product Resources template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Product Resources
  // ID: fbca6069-d6c5-493e-83d7-c4a079e2717f
  export interface ProductResourcesDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Main image field.
    // Short description:
    // Field Type: Droptree
    // Field ID: bb61c579-50c7-40e7-b32e-a66bdc5d2dc3
    // Custom Data:
    mainImage: ReactJssModule.Item<ReactJssModule.BaseDataSourceItem>;
  }
  // The Products template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Products
  // ID: 4d21ad2a-ba49-4dfc-9db9-c299c7fb7f6e
  export interface ProductsDataSource extends ReactJssModule.BaseDataSourceItem {
  }
  // The Product Specification template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Product Specification
  // ID: 687ebd28-8c90-4203-8527-a809d9b99680
  export interface ProductSpecificationDataSource extends SynchronizationDataSource {
    // The Group field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 6486cbd0-3e73-4293-9da9-52249bba163d
    // Custom Data:
    group: ReactJssModule.TextField;
    // The Key field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 88892c6a-f865-493e-82b7-5c21e67c2fa4
    // Custom Data:
    key: ReactJssModule.TextField;
    // The LookupValue field.
    // Short description:
    // Field Type: Treelist with Search
    // Field ID: 8377b000-0ee9-4f77-9eba-2b0822669f84
    // Custom Data:
    lookupValue: ReactJssModule.ItemList<ReactJssModule.BaseDataSourceItem>;
    // The Value field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 59b79baf-4f30-4eca-961d-4afab14d157c
    // Custom Data:
    value: ReactJssModule.TextField;
  }
  // The Product Specifications template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Product Specifications
  // ID: a1441a78-21ac-483c-89a6-d46521818b94
  export interface ProductSpecificationsDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Description field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 92630a4a-369d-47ce-9ff1-a72a7dd67969
    // Custom Data:
    description: ReactJssModule.TextField;
    // The VariantSpecifications field.
    // Short description:
    // Field Type: Multilist
    // Field ID: 95cf928f-0ca7-4daf-b6b1-8dfe31ac9523
    // Custom Data:
    variantSpecifications: ReactJssModule.ItemList<ReactJssModule.BaseDataSourceItem>;
  }
  // The Product Store template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Product Stores/Product Store
  // ID: 661d783a-fb8e-4d98-bbb4-8796e25bb2c7
  export interface ProductStoreDataSource extends SynchronizationDataSource {
    // The AvailableAddressCountries field.
    // Short description: Leave empty to allow all
    // Field Type: Multilist
    // Field ID: c8151ea4-ea10-421a-b44b-bcfc881e388a
    // Custom Data:
    availableAddressCountries: ReactJssModule.ItemList<ReactJssModule.BaseDataSourceItem>;
    // The CustomerCareRegionalEmailAddress field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 70ab3290-122a-4b3c-83d5-dd80db4567d3
    // Custom Data:
    customerCareRegionalEmailAddress: ReactJssModule.TextField;
    // The CustomerCareRegionalPhoneNumber field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 24e07817-b39e-4e4e-9d21-6e901c20d3f3
    // Custom Data:
    customerCareRegionalPhoneNumber: ReactJssModule.TextField;
    // The DefaultCartLink field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 8c845038-eccb-4f69-a8e5-7da952cee494
    // Custom Data:
    defaultCartLink: ReactJssModule.TextField;
    // The EnableRemoveChapterLink field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 0b5e7902-3a05-4b20-b1d3-9b03aac17021
    // Custom Data:
    enableRemoveChapterLink: ReactJssModule.Field<boolean>;
    // The Is Default Store field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 272b3899-dd90-43be-933a-637cfbeb4872
    // Custom Data:
    isDefaultStore: ReactJssModule.Field<boolean>;
    // The Is Master Store field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: a3f214ec-d7af-4590-818b-01c9ef57d7cf
    // Custom Data:
    isMasterStore: ReactJssModule.Field<boolean>;
    // The Is Store Enabled field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 81f71b88-46cc-4d33-b28f-eb85d3b92221
    // Custom Data:
    isStoreEnabled: ReactJssModule.Field<boolean>;
    // The Is Tiered Store field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: b040c3bb-12ee-44ce-abfa-8034017d009c
    // Custom Data:
    isTieredStore: ReactJssModule.Field<boolean>;
    // The LocalCurrencies field.
    // Short description:
    // Field Type: Multilist
    // Field ID: 49ba8681-8640-4dcb-adb8-682fa369bfdb
    // Custom Data: type=Pmi.Spx.Foundation.Connect.TemplateModels.ICurrency
    localCurrencies: ReactJssModule.ItemList<ReactJssModule.BaseDataSourceItem>;
    // The Store Countries field.
    // Short description:
    // Field Type: Multilist
    // Field ID: 97f9cbba-e6dd-4c3a-8657-b2d13f35bd5e
    // Custom Data: type=Pmi.Spx.Foundation.Connect.TemplateModels.ICountry
    storeCountries: ReactJssModule.ItemList<ReactJssModule.BaseDataSourceItem>;
    // The Store Currency field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 68411739-4d6c-4b23-a18c-91958b64a220
    // Custom Data: type=Pmi.Spx.Foundation.Connect.TemplateModels.ICurrency
    storeCurrency: ReactJssModule.Item<ReactJssModule.BaseDataSourceItem>;
    // The Store Id field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: fefd99ae-9387-4dcd-bb60-5e986b533054
    // Custom Data:
    storeId: ReactJssModule.TextField;
    // The Store Identifier field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 8241cef9-8088-4c7b-a667-7f9c303cc5a4
    // Custom Data:
    storeIdentifier: ReactJssModule.TextField;
    // The StoreLanguage field.
    // Short description:
    // Field Type: Droplist
    // Field ID: 72a58959-671e-4611-b99e-b89e69397f8d
    // Custom Data:
    storeLanguage: ReactJssModule.TextField;
    // The Store Name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 00184368-779d-485f-9942-3f6570158ca0
    // Custom Data:
    storeName: ReactJssModule.TextField;
    // The SyncCountries field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 0a28ff27-3c94-46a3-8550-21342e82c466
    // Custom Data:
    syncCountries: ReactJssModule.Field<boolean>;
    // The Tier field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 87002ed3-365e-4a58-a467-768941b0d0dc
    // Custom Data: nullable=true
    tier: ReactJssModule.Item<ReactJssModule.BaseDataSourceItem>;
    // The WebsiteId field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 58af6183-73bd-4775-aeb6-ee4feeda40f9
    // Custom Data:
    websiteId: ReactJssModule.TextField;
  }
  // The Product Stores template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Product Stores/Product Stores
  // ID: f1f86d29-d324-4d86-8da4-c56770ae9283
  export interface ProductStoresDataSource extends ReactJssModule.BaseDataSourceItem {
  }
  // The Product Template Mapping template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Settings/Product Template Mapping
  // ID: 48856c4b-f788-4b33-bb42-10ca051a94cd
  export interface ProductTemplateMappingDataSource extends ProductMatchingDataSource {
    // The Product Template field.
    // Short description:
    // Field Type: Droplink
    // Field ID: d78d8a82-643f-417c-bd1d-3abd6cb46bad
    // Custom Data: nullable=true
    productTemplate: ReactJssModule.Item<ReactJssModule.BaseDataSourceItem>;
    // The Product Template Headless field.
    // Short description:
    // Field Type: Droplink
    // Field ID: deb5fd81-4317-4ad7-a237-316446b2b345
    // Custom Data: nullable=true
    productTemplateHeadless: ReactJssModule.Item<ReactJssModule.BaseDataSourceItem>;
  }
  // The Product Template Mappings template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Settings/Product Template Mappings
  // ID: 679384ac-a080-4a48-b3c2-0794fb65e4e4
  export interface ProductTemplateMappingsDataSource extends ReactJssModule.BaseDataSourceItem {
  }
  // The Product Type template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Product Type
  // ID: 2fc31cad-946f-4944-9a98-3da167bebc34
  export interface ProductTypeDataSource extends SynchronizationDataSource {
    // The Description field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: ec7d4ac3-83b6-4653-944a-097aafdfff07
    // Custom Data:
    description: ReactJssModule.TextField;
    // The ExternalParentID field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 5d89d406-b223-426e-a029-ce7138a9bab2
    // Custom Data:
    externalParentID: ReactJssModule.TextField;
    // The Name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 93e7751e-aec7-4ea7-b5d8-b6d6653b3e8b
    // Custom Data:
    name: ReactJssModule.TextField;
  }
  // The Product Types template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Product Types
  // ID: 26d1e614-5ae9-4e58-90ab-6cf9b9b6f938
  export interface ProductTypesDataSource extends ReactJssModule.BaseDataSourceItem {
  }
  // The Product Type Specification template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Product Type Specification
  // ID: c696bec3-0414-4a3d-a4fe-3583a7ae051d
  export interface ProductTypeSpecificationDataSource extends ProductSpecificationDataSource {
  }
  // The Region template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Global Settings/Region
  // ID: c3a3d7c2-002d-4be9-a6d3-564481175f92
  export interface RegionDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Code field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: da530c00-2c3d-44fa-adbe-4555b94effcd
    // Custom Data:
    code: ReactJssModule.TextField;
    // The Name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 399c7c33-21a2-49c2-be65-d0d7ae956f0d
    // Custom Data:
    name: ReactJssModule.TextField;
  }
  // The REP Course template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Products/Product Specific Templates/REP Course
  // ID: ce4fd7d0-e6fa-400e-b09f-b29ddf7f2d0d
  export interface REPCourseDataSource extends CommerceProductBaseDataSource {
    // The course_level field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 3203d3ac-9b02-416a-a436-f5133c265e23
    // Custom Data:
    courseLevel: ReactJssModule.TextField;
    // The format field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 603747cb-0bc8-4a3d-9ccc-fd3dabcd365d
    // Custom Data:
    format: ReactJssModule.TextField;
    // The pdus field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: bb810404-13cd-4f78-a62d-7c2294674950
    // Custom Data:
    pdus: ReactJssModule.TextField;
    // The rep_provider_id field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: a437255c-8fdf-44e5-93a2-1b64deacfc5f
    // Custom Data:
    repProviderId: ReactJssModule.TextField;
    // The rep_provider_name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: f6da501a-305f-4467-8b50-588b8f2c336f
    // Custom Data:
    repProviderName: ReactJssModule.TextField;
  }
  // The REP Member template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Products/Product Specific Templates/REP Member
  // ID: 63ed873e-e791-46c5-9932-cfb9bfd66e85
  export interface REPMemberDataSource extends CommerceProductBaseDataSource {
  }
  // The Resource Folder template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Resource Folder
  // ID: a0a1342a-6339-445c-94a1-31cb7756aa0b
  export interface ResourceFolderDataSource extends ReactJssModule.BaseDataSourceItem {
  }
  // The Search Settings template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Global Settings/Search Settings
  // ID: 5901d826-15c7-4219-a985-bcdc46736d70
  export interface SearchSettingsDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Items Per Page field.
    // Short description:
    // Field Type: Integer
    // Field ID: 35fc96a7-bf12-4c02-a66a-1ae68d3b4432
    // Custom Data:
    itemsPerPage: ReactJssModule.Field<number>;
    // The Search Facets field.
    // Short description:
    // Field Type: Multilist
    // Field ID: a3683321-18c4-424a-94aa-fc1ce9b03b92
    // Custom Data:
    searchFacets: ReactJssModule.ItemList<ReactJssModule.BaseDataSourceItem>;
    // The Sort Fields field.
    // Short description:
    // Field Type: Multilist
    // Field ID: 91c3811d-b760-475c-87e7-0aee5d4080c9
    // Custom Data:
    sortFields: ReactJssModule.ItemList<ReactJssModule.BaseDataSourceItem>;
  }
  // The Specification template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Specification
  // ID: 284f5f48-7d2e-48d3-b3f5-c8f45549a2a0
  export interface SpecificationDataSource extends ReactJssModule.BaseDataSourceItem {
    // The Name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 02eaa1d4-a90a-4124-b2ba-3453794daf10
    // Custom Data:
    name: ReactJssModule.TextField;
  }
  // The Specification Lookup template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Specification Lookup
  // ID: 64ee9778-10d3-492a-a52e-d229ce8b9fad
  export interface SpecificationLookupDataSource extends SynchronizationDataSource {
    // The Name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: f3f37114-38b0-43df-b9c7-512b62081bf1
    // Custom Data:
    name: ReactJssModule.TextField;
  }
  // The Specification Lookups template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Specification Lookups
  // ID: a8a3d56b-bca0-4877-b137-86992f341d81
  export interface SpecificationLookupsDataSource extends ReactJssModule.BaseDataSourceItem {
  }
  // The Synchronization template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Synchronization
  // ID: 240334a5-cfe8-4450-bb85-253d620cba02
  export interface SynchronizationDataSource extends ReactJssModule.BaseDataSourceItem {
    // The ExternalID field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: f2f908f1-806e-4706-911b-6794b73576d0
    // Custom Data:
    externalID: ReactJssModule.TextField;
  }
  // The Webinar template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Products/Product Specific Templates/Webinar
  // ID: 805fa044-5fd1-45c2-acf2-b199d9dd2366
  export interface WebinarDataSource extends CommerceProductBaseDataSource {
    // The course_level field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 10662483-c0e9-4e7c-81b0-58f7db29f0b0
    // Custom Data:
    courseLevel: ReactJssModule.TextField;
    // The format field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 67596356-2d5c-4213-8a6f-e55536ab098a
    // Custom Data:
    format: ReactJssModule.TextField;
    // The pdus field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: c48a932b-6fa1-472a-b7ae-1b02c6d59853
    // Custom Data:
    pdus: ReactJssModule.TextField;
  }
  // The _With Category template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Tokens/_With Category
  // ID: ebd414c1-b36d-4efe-b41f-6ef2643ad09b
  export interface WithCategoryDataSource extends ReactJssModule.BaseDataSourceItem {
  }
  // The _With Product template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Tokens/_With Product
  // ID: 818c94a3-4023-4e2e-b93e-7a2be09eab9a
  export interface WithProductDataSource extends ReactJssModule.BaseDataSourceItem {
  }

  // The _BaseIcon template.
  // Short description:
  // Path: /sitecore/templates/PMI/Base/_BaseIcon
  // ID: a220c0f8-274a-4fc5-9896-6fa249935468
  export interface BaseIconRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Icon field.
    // Short description:
    // Field Type: Image
    // Field ID: 734b447e-44ec-4739-8acc-b89fd51e1fec
    // Custom Data:
    icon: string;
    // The IconClass field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 4796a76c-5867-4640-9dca-8bb7a17fb185
    // Custom Data:
    iconClass: string;
  }
  // The _BaseTitle template.
  // Short description:
  // Path: /sitecore/templates/PMI/Base/BaseContent/_BaseTitle
  // ID: ab8b7fa5-83b6-4713-8a1e-ff29f10192df
  export interface BaseTitleRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Title field.
    // Short description: Please enter title of the item here.
    // Field Type: Single-Line Text
    // Field ID: 56d15ac6-4a9b-4e75-830c-a30e63384011
    // Custom Data:
    title: string;
  }
  // The Book template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Products/Product Specific Templates/Book
  // ID: 9bb56de8-091b-440a-856c-cb71437914c3
  export interface BookRenderingParams extends CommerceProductBaseRenderingParams {
    // The author_name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 1c6bd3f5-fc36-4d79-af71-a20023887706
    // Custom Data:
    authorName: string;
    // The book_format field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 55b0d64f-c2b8-4e11-a675-8a19fea7d25b
    // Custom Data:
    bookFormat: string;
    // The date_published field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: de5684ee-a60b-4c37-9658-d49846ee87ec
    // Custom Data:
    datePublished: string;
    // The isbn13_number field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: cbccc2bf-cdae-468c-8735-192e5e1a7144
    // Custom Data:
    isbn13Number: string;
    // The language_format field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 0ea6d4eb-e7da-4c3e-9865-f455700e3d44
    // Custom Data: type=IProductAttribute
    languageFormat: string;
    // The number_pages field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 8b2a09e5-b2f2-4df5-899c-d17ebab22f6f
    // Custom Data:
    numberPages: string;
    // The preorderable field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 4d3b0adb-4bc8-4b8c-9a1a-f8c4c6bcb42f
    // Custom Data:
    preorderable: boolean;
    // The publisher_name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 13e0af40-3c77-42fb-bbba-acfae6ea629f
    // Custom Data:
    publisherName: string;
    // The subject field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: c7bc10cd-061c-4cef-8293-7b2465712c97
    // Custom Data:
    subject: string;
    // The tax_class_id field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 74e6405a-e1ea-427d-b2bb-2b2ee111c879
    // Custom Data:
    taxClassId: string;
  }
  // The CardType template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Global Settings/CardType
  // ID: ce02bea5-5b2f-43ba-baa0-9abe508a9951
  export interface CardTypeRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Card Description field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: faf342aa-e5dc-4bff-bc77-ff30bf0436a8
    // Custom Data:
    cardDescription: string;
    // The Card ID field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: edb01ed2-fa72-4fd3-bb0a-48c0ca30947b
    // Custom Data:
    cardID: string;
  }
  // The CardTypes template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Global Settings/CardTypes
  // ID: 1e2dbf82-addc-4b08-ace9-00771c27818a
  export interface CardTypesRenderingParams extends ReactJssModule.BaseRenderingParam {
  }
  // The Certification template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Products/Product Specific Templates/Certification
  // ID: 3fcc6991-b4a3-4221-90e6-202862e4ffc5
  export interface CertificationRenderingParams extends CommerceProductBaseRenderingParams {
    // The credential field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 0e324b88-5ccc-4a27-8b9f-d69064a01a7b
    // Custom Data:
    credential: string;
    // The examtype field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 312aa28f-5139-48f0-b07d-48169506883e
    // Custom Data:
    examtype: string;
    // The ordertype field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 33f47df4-5427-4e88-8441-2f487c66f290
    // Custom Data:
    ordertype: string;
    // The retake field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 69dc5886-0d92-4083-88bc-bf3f1164bc1c
    // Custom Data:
    retake: boolean;
    // The studentbundle field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 98598266-c002-4063-8a45-df29bc7fe40e
    // Custom Data:
    studentbundle: boolean;
  }
  // The Chapter template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Products/Product Specific Templates/Chapter
  // ID: 96f5afa4-8b80-483c-98e8-259d646c9eec
  export interface ChapterRenderingParams extends CommerceProductBaseRenderingParams {
    // The chapter_city field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: f198a68e-ebcc-4a08-848b-e0f4f8989cbe
    // Custom Data:
    chapterCity: string;
    // The chapter_code field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: c2e05a55-e899-4a37-b186-91c9c81085f1
    // Custom Data:
    chapterCode: string;
    // The chapter_email field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 51261ff3-a674-4e10-a34f-5862f78f1d47
    // Custom Data:
    chapterEmail: string;
    // The chapter_phone field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 25307bf5-cc6d-4d24-a434-348906df7420
    // Custom Data:
    chapterPhone: string;
    // The chapter_state field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 2343e963-cd03-49b4-bcf0-1e25bc55b102
    // Custom Data:
    chapterState: string;
    // The charter_status field.
    // Short description:
    // Field Type: Droplink
    // Field ID: e32c2c67-2af2-42ad-ac14-db876b8d93dd
    // Custom Data:
    charterStatus: string;
    // The charter_year field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: cd12b58d-91a8-4536-8f3e-2ddb0682783b
    // Custom Data:
    charterYear: string;
    // The country field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 117d48ee-b23f-49a2-b77d-7b15d5ce3a53
    // Custom Data:
    country: string;
    // The region field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 4375df1f-6874-4e61-9709-1c5b065b71ab
    // Custom Data:
    region: string;
    // The url field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 3291a8f9-4bcb-42c8-94dc-cc965800854e
    // Custom Data:
    url: string;
  }
  // The Commerce Category Base template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Categories/Commerce Category Base
  // ID: c68778ab-89f5-42bb-85f6-1ff12dcc7d6c
  export interface CommerceCategoryBaseRenderingParams extends ProductClassificationRenderingParams, SearchSettingsRenderingParams, WithCategoryRenderingParams {
    // The Display Mode field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: a4367de0-87af-4b95-8390-ca462eae5624
    // Custom Data:
    displayMode: string;
    // The Include In Menu field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 7f9e9e7f-c2c3-4dbe-b908-147b284ebb5a
    // Custom Data:
    includeInMenu: boolean;
    // The Is Active field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 80a6c060-0bf3-47cd-a964-693e0b4ac075
    // Custom Data:
    isActive: boolean;
    // The Is Deleted field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 4ae86e22-af29-46a5-b026-e147bbd2c4ed
    // Custom Data:
    isDeleted: boolean;
    // The Position field.
    // Short description:
    // Field Type: Integer
    // Field ID: a2eb381f-9240-476c-a9bd-81940c43f14f
    // Custom Data:
    position: number;
  }
  // The Commerce Product Base template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Products/Commerce Product Base
  // ID: f9aec12e-65a5-4e11-82ef-a50e9f200257
  export interface CommerceProductBaseRenderingParams extends ProductRenderingParams, WithProductRenderingParams, ContentPageRenderingParams, BaseIconRenderingParams, ProductHierarchyBaseRenderingParams {
    // The AB Prices field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: 97749ffe-4bd0-4190-abe1-c92c68de1397
    // Custom Data:
    abPrices: string;
    // The B2b Full Description field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: e919cee7-a090-4848-b062-626a9e3c2ea7
    // Custom Data:
    b2bFullDescription: string;
    // The B2b image field.
    // Short description:
    // Field Type: Image
    // Field ID: a88b0bd5-dc5c-4049-b3ae-5e48fa881c93
    // Custom Data:
    b2bImage: string;
    // The B2b Short Description field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 540b1fca-a499-4ebd-80a0-cf252801e994
    // Custom Data:
    b2bShortDescription: string;
    // The B2b Title field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 15389578-4623-4398-901c-e9a7b5533a45
    // Custom Data:
    b2bTitle: string;
    // The Backorders field.
    // Short description:
    // Field Type: Number
    // Field ID: 6d849509-14d3-492a-b49a-550291a14caa
    // Custom Data:
    backorders: number;
    // The BundleProductOptions field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: 60f860f0-69cb-4834-95a7-1391ba665a59
    // Custom Data:
    bundleProductOptions: string;
    // The Country Prices field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: 202e0c1e-5500-46be-bb5d-14a400eb6130
    // Custom Data:
    countryPrices: string;
    // The course_kit_type field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 1e58883a-96ca-4e89-9b68-953a9678734c
    // Custom Data:
    courseKitType: string;
    // The course_type field.
    // Short description:
    // Field Type: Multilist
    // Field ID: 5fc60777-de7e-4055-945e-6445f00f41a3
    // Custom Data:
    courseTypes: string;
    // The custom_cart field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: b8fa0117-1511-40bb-95ec-7b265962cec0
    // Custom Data:
    customCart: string;
    // The disabled_Product_URL field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 0807e662-73c5-41c4-a975-96fd514e0ff9
    // Custom Data:
    disabledProductURL: boolean;
    // The fulfillment_provider field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 5ca91282-238c-4179-a84b-d7a777753a16
    // Custom Data:
    fulfillmentProvider: string;
    // The Global Price field.
    // Short description:
    // Field Type: Number
    // Field ID: 3a732fb5-16ff-448d-932a-143810b5f682
    // Custom Data:
    globalPrice: number;
    // The Group Prices field.
    // Short description:
    // Field Type: Lookup Name Value List
    // Field ID: da043375-9353-44e1-b45c-d4ed88459ebf
    // Custom Data: type=IDictionary<ICustomerGroup, float>
    groupPrices: any;
    // The HeroBackgroundImage field.
    // Short description:
    // Field Type: Image
    // Field ID: 6d95f762-44dd-4123-8b31-36a03113d48a
    // Custom Data:
    heroBackgroundImage: string;
    // The HeroBackgroundImageMobile field.
    // Short description:
    // Field Type: Image
    // Field ID: daf9a28d-b84c-4a67-8595-a297c050909f
    // Custom Data:
    heroBackgroundImageMobile: string;
    // The HeroImage field.
    // Short description:
    // Field Type: Image
    // Field ID: 5a0d0f52-211e-4e3c-ab2f-71726cbd9057
    // Custom Data:
    heroImage: string;
    // The Image field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: c89f0ba8-67f4-44f6-a7f1-f68a46cdbcb6
    // Custom Data:
    image: string;
    // The included_in_membership field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: eb7fc950-cb4c-41e4-82d1-747429338465
    // Custom Data:
    includedInMembership: boolean;
    // The Is Deleted field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 2aaa5d2e-4301-40ad-b6f9-d752c5fad3e3
    // Custom Data:
    isDeleted: boolean;
    // The Is Featured field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 3bfaed00-9aab-42c8-9fb7-8e8d1f0027a6
    // Custom Data:
    isFeatured: boolean;
    // The Is In Stock field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: bb29b6b4-3456-4d3a-94db-b02656ca47a3
    // Custom Data:
    isInStock: boolean;
    // The is_membership field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 47e537b2-9d39-47db-81f7-295157ed880e
    // Custom Data:
    isMembership: boolean;
    // The Is Returnable field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: cae45dfb-0c62-4857-a91a-b72b50354c7d
    // Custom Data:
    isReturnable: string;
    // The is_subscription_product field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 4b7c5ef9-08c0-41c5-aa41-278641707375
    // Custom Data:
    isSubscriptionProduct: boolean;
    // The language field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 595359da-ec9f-4c4c-a6a2-7546c7ef1240
    // Custom Data:
    language: string;
    // The membershipTextHighlightColor field.
    // Short description:
    // Field Type: Droplink
    // Field ID: b18d1660-c856-4d2f-8e3d-c676d4a7421e
    // Custom Data:
    membershipTextHighlightColor: string;
    // The OverviewDescription field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: df536977-1ee5-47e9-b7f6-11f25a9d9330
    // Custom Data:
    overviewDescription: string;
    // The pmi_catalog field.
    // Short description:
    // Field Type: Multilist
    // Field ID: 8e050945-712f-407c-8c38-8835b5488376
    // Custom Data:
    pmiCatalogs: string;
    // The PMI Membership field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 50845396-2667-4e2a-a548-93c81fc48043
    // Custom Data:
    pmiMembership: string;
    // The pmi_product_category field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 3ce1903b-97fa-4f44-9395-4871dd6b91e0
    // Custom Data:
    pmiProductCategory: string;
    // The pmi_product_family field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 8a3f0338-265e-445d-8d06-47fb117c7bc4
    // Custom Data:
    pmiProductFamily: string;
    // The pmi_product_format field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 8f191f3e-4c52-4091-a6dd-78915bc7af3a
    // Custom Data:
    pmiProductFormat: string;
    // The pmi_product_id field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: aa683096-5550-4fee-bf74-ffa9a08957bc
    // Custom Data:
    pmiProductId: string;
    // The pmi_product_type field.
    // Short description:
    // Field Type: Droplink
    // Field ID: fdb83207-3c1c-4566-ba47-d28568bbdeba
    // Custom Data:
    pmiProductType: string;
    // The PMI Segment field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 3b0804fc-df69-4424-9d09-892341852d72
    // Custom Data:
    pmiSegment: string;
    // The ProductCustomOptions field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: 3350a005-c47e-461c-b6ce-17e223960c71
    // Custom Data:
    productCustomOptions: string;
    // The Product Kind field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 3d5ca766-9602-4a5b-aff6-8fcb50fca321
    // Custom Data: nullable=true
    productKind: string;
    // The Product Store field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 8e4499d7-9df2-4e4a-a170-3342dc989426
    // Custom Data: nullable=true&type=IProductStore
    productStore: string;
    // The PromoBadges field.
    // Short description:
    // Field Type: Multilist
    // Field ID: 23ee2ae2-1d4d-4e51-8e2a-b251e55e8a0d
    // Custom Data:
    promoBadges: string;
    // The Quantity field.
    // Short description:
    // Field Type: Number
    // Field ID: 549f9561-22be-4070-84f1-c82639c08c6d
    // Custom Data:
    quantity: number;
    // The RecommendedAltText field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 3568a091-d725-4a13-9b45-fb21802c9ca3
    // Custom Data:
    recommendedAltText: string;
    // The Renewal Price field.
    // Short description:
    // Field Type: Number
    // Field ID: b3f78b0d-bf55-4b73-b003-dbafdb00ccb3
    // Custom Data:
    renewalPrice: number;
    // The SeoFriendlyTitle field.
    // Short description: This will be used in the URL path
    // Field Type: Single-Line Text
    // Field ID: 260a336f-7541-4b8c-a023-3198ca421c86
    // Custom Data:
    seoFriendlyTitle: string;
    // The showCTA field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 402d02c0-c629-4592-b316-2d1cfd687197
    // Custom Data:
    showCTA: boolean;
    // The showPrices field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 8318d912-a453-4e22-8ca2-6ddbc96c7646
    // Custom Data:
    showPrices: boolean;
    // The single_membership field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 45cef470-41fe-48e2-835d-f7cc180fa1a5
    // Custom Data:
    singleMembership: boolean;
    // The Small Image field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: f482cd3c-6b97-48c3-ab80-a898ca3f874b
    // Custom Data:
    smallImage: string;
    // The sold_as_bundle field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 42c17217-ecc2-4d07-8d35-c5913a94d788
    // Custom Data:
    soldAsBundle: boolean;
    // The subscription_frequency field.
    // Short description:
    // Field Type: Droplink
    // Field ID: c2ddffb8-5490-4ebf-ad2e-1b220a466c5a
    // Custom Data: nullable=true&type=IProductAttributeValue
    subscriptionFrequency: string;
    // The Thumbnail field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 2e2436e4-e5c4-4a6c-909e-1eb7931cb2d9
    // Custom Data:
    thumbnail: string;
    // The UseDarkThemeForHero field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 7bdf557d-6804-49e5-acc8-a1835731babc
    // Custom Data:
    useDarkThemeForHero: boolean;
    // The Weight field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: f3ef8df0-e281-4d4e-bfba-7d950d517170
    // Custom Data:
    weight: string;
    // The Wordmark field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 1fc37bfb-e2f6-4337-b793-b12477c305bd
    // Custom Data:
    wordmark: string;
    // The Zone Prices field.
    // Short description:
    // Field Type: Multi-Line Text
    // Field ID: babb6e53-3777-4916-a764-01aad64cce60
    // Custom Data:
    zonePrices: string;
  }
  // The Commerce Product Type Base template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Product Types/Commerce Product Type Base
  // ID: d46f0f09-ee8e-44e6-a425-98d246f9a0c9
  export interface CommerceProductTypeBaseRenderingParams extends ProductTypeRenderingParams {
  }
  // The Connect Settings template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Settings/Connect Settings
  // ID: adf5c892-b08b-4294-9e28-da612b5fae41
  export interface ConnectSettingsRenderingParams extends ReactJssModule.BaseRenderingParam {
  }
  // The Content Page template.
  // Short description:
  // Path: /sitecore/templates/Foundation/BaseModels/Content Page
  // ID: 7e181119-46dd-489c-9fdf-0c9dc89141c2
  export interface ContentPageRenderingParams extends BaseTitleRenderingParams {
    // The Short Title field.
    // Short description: Please enter text of the item here.
    // Field Type: Single-Line Text
    // Field ID: e27e197c-ad78-4352-bb04-cb7da291a88e
    // Custom Data:
    shortTitle: string;
  }
  // The Countries template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Global Settings/Countries
  // ID: d7640afe-63c0-4cdf-876f-c17c5d3fa5c4
  export interface CountriesRenderingParams extends ReactJssModule.BaseRenderingParam {
  }
  // The Country template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Global Settings/Country
  // ID: 45537cd4-5252-4436-8b1a-82c1c7c8fd90
  export interface CountryRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Code field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: b9203627-da88-48d6-b617-082458ac0e7c
    // Custom Data:
    code: string;
    // The Code3 field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 61fe24c7-974b-493e-a16d-e1dddbbca3ee
    // Custom Data:
    code3: string;
    // The Name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: bf30b4a3-0f21-4946-87c0-de453d9aa6fa
    // Custom Data:
    name: string;
  }
  // The Currencies template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Global Settings/Currencies
  // ID: eba94d27-11ce-4bc2-a537-a1855aa0c20f
  export interface CurrenciesRenderingParams extends ReactJssModule.BaseRenderingParam {
  }
  // The Currency template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Global Settings/Currency
  // ID: b5571a56-037b-48ff-b317-68e10a631079
  export interface CurrencyRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Allowed Cards field.
    // Short description:
    // Field Type: Multilist
    // Field ID: d9dc15f3-ae68-4ee8-82f7-d54e63cb6215
    // Custom Data: type=Pmi.Spx.Foundation.Connect.TemplateModels.ICardType
    allowedCards: string;
    // The Country field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 6a85eea1-6911-4661-bb68-e297057568d4
    // Custom Data: type=Pmi.Spx.Foundation.Connect.TemplateModels.ICountry
    country: string;
    // The Currency Code field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 6fe1ebdc-6340-4c66-bfda-4eb65f155180
    // Custom Data:
    currencyCode: string;
    // The CurrencyDecimalPlaces field.
    // Short description:
    // Field Type: Integer
    // Field ID: c16357b6-0ea4-4067-bff6-045685442ec5
    // Custom Data:
    currencyDecimalPlaces: number;
    // The Currency Name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: bea10233-e1b8-4651-b45f-90e9a4f4171a
    // Custom Data:
    currencyName: string;
    // The Currency Symbol field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: c0163f1f-9646-4d2f-82da-059d13b67b71
    // Custom Data:
    currencySymbol: string;
  }
  // The Customer Group template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Customer Groups/Customer Group
  // ID: 12a53e3f-4e34-4a89-b308-4c0592139498
  export interface CustomerGroupRenderingParams extends SynchronizationRenderingParams {
    // The Code field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: e4b8bb8a-b700-4b11-b4b5-37e7c0dea2eb
    // Custom Data:
    code: string;
  }
  // The Customer Groups template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Customer Groups/Customer Groups
  // ID: 4483c063-f8b4-42f9-8575-96413c8556b9
  export interface CustomerGroupsRenderingParams extends ReactJssModule.BaseRenderingParam {
  }
  // The Digital Product Base template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Products/Digital Product Base
  // ID: 559d3674-f2c5-40ba-9db5-da037785e2b6
  export interface DigitalProductBaseRenderingParams extends ProductHierarchyBaseRenderingParams {
    // The AssociatedCertifications field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: f3c0f88e-5f66-499b-b927-a75e44f4f991
    // Custom Data:
    associatedCertifications: string;
    // The Byline field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 94b3b86b-1e81-4469-aa6f-19b529c84599
    // Custom Data:
    byline: string;
    // The ceu_credit_value field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 79e27f62-fe68-4e2c-a6aa-e2c1fca27235
    // Custom Data:
    ceuCreditValue: string;
    // The level_of_course field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 812400f2-0248-4b8e-8b38-bc822624009a
    // Custom Data: type=Pmi.Spx.Foundation.Connect.TemplateModels.IProductAttributeValue&name=CourseLevel
    courseLevel: string;
    // The language_format field.
    // Short description:
    // Field Type: Multilist
    // Field ID: 75c31d86-c4e7-4f60-b89a-446095adfbe9
    // Custom Data: type=Pmi.Spx.Foundation.Connect.TemplateModels.IProductAttributeValue
    languageFormats: string;
    // The leadership_pdus field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 76fc17bc-5a80-4ab5-bab3-d351b135e8e2
    // Custom Data:
    leadershipPdus: string;
    // The length_of_course field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: bff354fa-9d7b-40a9-8f1d-6e82f2e691de
    // Custom Data:
    lengthOfCourse: string;
    // The pdus field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: b888b428-2677-4008-9d1d-9667886a97a6
    // Custom Data:
    pdus: string;
    // The strategic_business_pdus field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: eb492c8b-c4d1-44b3-a223-789c1432ca41
    // Custom Data:
    strategicBusinessPdus: string;
    // The technical_pdus field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 77d189fe-5ede-4a12-8da1-a9fc11c5ec41
    // Custom Data:
    technicalPdus: string;
  }
  // The Digital Product template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Products/Product Specific Templates/Digital Product
  // ID: a7ef4f24-c310-436e-968b-831eacf63ad3
  export interface DigitalProductRenderingParams extends CommerceProductBaseRenderingParams, DigitalProductBaseRenderingParams {
    // The details field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: fdcff921-b60c-44c5-9d68-86ff124083c1
    // Custom Data:
    details: string;
  }
  // The Division template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Division
  // ID: c715d20b-2e73-4046-9b87-749a11c27c5c
  export interface DivisionRenderingParams extends SynchronizationRenderingParams {
    // The Description field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: dd8ff6da-9e67-4b19-8409-58f4b271c14a
    // Custom Data:
    description: string;
    // The Name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: b6c749f8-d9d7-477a-8c02-58914020fa6f
    // Custom Data:
    name: string;
  }
  // The Divisions template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Divisions
  // ID: bf890455-a0ae-4502-8619-c952594605cb
  export interface DivisionsRenderingParams extends ReactJssModule.BaseRenderingParam {
  }
  // The Donation template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Products/Product Specific Templates/Donation
  // ID: d97497b8-fffe-4226-9613-35bca3737373
  export interface DonationRenderingParams extends CommerceProductBaseRenderingParams {
    // The donation_fixed_amount field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 95ceafa0-7690-4aea-a9c6-e8eddfb3e047
    // Custom Data:
    donationFixedAmount: string;
    // The donation_max_amount field.
    // Short description:
    // Field Type: Number
    // Field ID: 9708ab3d-47e5-4a2e-894d-af91d7f90060
    // Custom Data:
    donationMaxAmount: number;
    // The experius_donation_min_amount field.
    // Short description:
    // Field Type: Number
    // Field ID: a9083ea4-d44d-4fd1-9de1-75fe0c136705
    // Custom Data:
    experiusDonationMinAmount: number;
  }
  // The eBook template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Products/Product Specific Templates/eBook
  // ID: b4ba8998-578d-4854-9044-8f4105fa3e79
  export interface EBookRenderingParams extends CommerceProductBaseRenderingParams {
    // The author_name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: cf4dc6b5-5c66-4207-8768-55cb951e75ff
    // Custom Data:
    authorName: string;
    // The book_format field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: f923e5e9-85f8-4eba-b7e4-3378aea4e1cc
    // Custom Data:
    bookFormat: string;
    // The date_published field.
    // Short description:
    // Field Type: Date
    // Field ID: 108d89d0-1622-4eb1-b832-726a61252f7b
    // Custom Data:
    datePublished: string;
    // The eisbn13_number field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 2b264b1a-0bfc-466a-9d21-303a0685995a
    // Custom Data:
    eisbn13Number: string;
    // The language_format field.
    // Short description:
    // Field Type: Droplink
    // Field ID: b06f57ae-6094-4b6d-9869-7f0cd6eef0a5
    // Custom Data: type=IProductAttribute
    languageFormat: string;
    // The preorderable field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 21fc67d8-861c-4c18-953a-699cb1364729
    // Custom Data:
    preorderable: boolean;
    // The publisher_name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: fa54c689-092a-44ed-89f2-5c1593948268
    // Custom Data:
    publisherName: string;
    // The subject field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 3882e089-90d3-426e-ad76-5174c4f733fa
    // Custom Data:
    subject: string;
    // The tax_class_id field.
    // Short description:
    // Field Type: Droplink
    // Field ID: fe5d416d-bc66-4814-b17c-9313a141091d
    // Custom Data:
    taxClassId: string;
  }
  // The eLearning template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Products/Product Specific Templates/eLearning
  // ID: c4a2ccf8-a341-40bc-9170-ed121809fda4
  export interface ELearningRenderingParams extends CommerceProductBaseRenderingParams, DigitalProductBaseRenderingParams {
    // The format field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 2cd04308-3313-487e-b75a-082856ba27ff
    // Custom Data:
    format: string;
    // The lms_code field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 2bc3c5d2-d436-4f8f-a549-57865dca4858
    // Custom Data:
    lmsCode: string;
    // The OverviewLanguages field.
    // Short description: Language / SKU Mapping
    // Field Type: Lookup Name Value List
    // Field ID: 91df15c9-cdb4-4eda-8429-3ee46a99aeb6
    // Custom Data:
    overviewLanguages: any;
    // The pmi_elearning_provider field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 0c4a8b9e-f2d9-4075-8fd6-6fb7d1cde018
    // Custom Data: type=Pmi.Spx.Foundation.Connect.TemplateModels.IProductAttribute
    pmiElearningProvider: string;
    // The who_should_attend field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: fe9e9252-e629-496f-805d-5cb1a309c2bd
    // Custom Data:
    whoShouldAttend: string;
  }
  // The Event template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Products/Product Specific Templates/Event
  // ID: 0c5fe585-60fc-4da3-80d8-e56c86e89611
  export interface EventRenderingParams extends CommerceProductBaseRenderingParams {
    // The event_dates field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 5803bf63-979a-43c8-8e6e-d4dafb2d9bd7
    // Custom Data:
    eventDates: string;
    // The event_email field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 76063bd5-e959-426a-9cd1-6ea09a0f1d66
    // Custom Data:
    eventEmail: string;
    // The event_location field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 651a8e42-3a06-421f-9e65-10cf228b1228
    // Custom Data:
    eventLocation: string;
    // The event_name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: c27a0fd2-544b-44c9-9450-2f499b187db1
    // Custom Data:
    eventName: string;
    // The event_policy field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 35e3b570-175d-4505-a941-7f390f1a400f
    // Custom Data:
    eventPolicy: string;
    // The event_tickets field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 0cced7d8-1cef-4270-a88c-52f3f0f4e639
    // Custom Data:
    eventTickets: string;
    // The target_audience field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: e4019318-a45c-4dc5-931e-c879be4cd2ea
    // Custom Data:
    targetAudience: string;
  }
  // The Facet template.
  // Short description:
  // Path: /sitecore/templates/System/Item Buckets/Facet
  // ID: 5c125b6d-c481-4c24-b5b9-9a23fe396bf0
  export interface FacetRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Client Side Handle field.
    // Short description: 保留以供将来使用
    // Field Type: Multi-Line Text
    // Field ID: 7f42ed80-33f0-4c98-a1e7-b15be7551c36
    // Custom Data:
    clientSideHandle: string;
    // The DisplayName field.
    // Short description: 应使用更加用户友好的名称或进行本地化
    // Field Type: Single-Line Text
    // Field ID: 5de17054-0141-4e58-935b-fec623a60806
    // Custom Data:
    displayName: string;
    // The Enabled field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 3d0fe806-d0e3-4254-96be-a60899c7d2e6
    // Custom Data:
    enabled: boolean;
    // The Global field.
    // Short description: 是否要在每一搜索的每一节点中搜索此 Facet
    // Field Type: Checkbox
    // Field ID: 182bd0f4-c902-4a19-a1ba-d189caa18e1b
    // Custom Data:
    global: boolean;
    // The Minimum Number of Items field.
    // Short description: Facet 出现在搜索结果中之前必须显示的最少节点数
    // Field Type: Number
    // Field ID: 8a3ac1b8-8cc1-4dd4-8766-9803f1601741
    // Custom Data:
    minimumNumberOfItems: number;
    // The Name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 31ca077f-ef38-4980-8a24-05408031b2bc
    // Custom Data:
    name: string;
    // The Parameters field.
    // Short description: 索引中使用的字段小写名称，facet 以此为基础。您可以输入用逗号分隔的多个字段名称 (title,author)
    // Field Type: Single-Line Text
    // Field ID: 0e23bb51-c071-41b5-9d83-ac410b89b85a
    // Custom Data:
    parameters: string;
    // The Type field.
    // Short description: 实施 ISimpleFacet 的类
    // Field Type: Single-Line Text
    // Field ID: fae92c1e-9e2c-4209-89eb-c256d55e3d7f
    // Custom Data:
    type: string;
  }
  // The Global Connect Settings template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Global Settings/Global Connect Settings
  // ID: 62d3fd7b-1c19-4428-8ad6-b107cca460ee
  export interface GlobalConnectSettingsRenderingParams extends ReactJssModule.BaseRenderingParam {
  }
  // The Identification Type template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Identification Type
  // ID: 0a5feaa1-1b35-441a-9cc0-8fc259a15213
  export interface IdentificationTypeRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Description field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: dada97b4-46a4-4f5f-a96f-64fde1e9d837
    // Custom Data:
    description: string;
    // The Name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 6cbb88aa-3a9f-4769-9996-6ed459bed324
    // Custom Data:
    name: string;
  }
  // The Identification Types template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Identification Types
  // ID: 29fe9b4e-bda0-420f-97ca-ce1421cd9cc8
  export interface IdentificationTypesRenderingParams extends ReactJssModule.BaseRenderingParam {
  }
  // The Lookup Value template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Lookup Value
  // ID: b95d069c-7867-4140-816d-6b204949b522
  export interface LookupValueRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Description field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 7a3b32c4-ceaa-4759-9e6c-f2012fc40c91
    // Custom Data:
    description: string;
    // The Short field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: d8034d94-f87f-47e2-a884-f11abee21138
    // Custom Data:
    short: string;
  }
  // The Lookup Values template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Lookup Values
  // ID: ab6feece-afa4-49b8-b96d-f15e28eaefcd
  export interface LookupValuesRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Description field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 545f0965-e7a6-4231-9a13-4f7ff48c006f
    // Custom Data:
    description: string;
  }
  // The Manufacturer template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Manufacturer
  // ID: 8ecdc0a6-3a85-4f89-8f49-8a53aa75595e
  export interface ManufacturerRenderingParams extends SynchronizationRenderingParams {
    // The Description field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 465f8abe-79fb-4c68-8dfa-5890bb7f2a60
    // Custom Data:
    description: string;
    // The Name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: eca748ca-62b5-4f86-a213-796b668a0ad8
    // Custom Data:
    name: string;
    // The ProductURLMacro field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 991e7d30-115b-444a-bfd5-3831727e78e1
    // Custom Data:
    productURLMacro: string;
    // The WebsiteUrl field.
    // Short description:
    // Field Type: General Link
    // Field ID: 3a6ff55f-4354-46f2-bf70-d6550d834922
    // Custom Data:
    websiteUrl: string;
  }
  // The Manufacturers template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Manufacturers
  // ID: 6addbd3c-6a82-4361-a361-6c89b4cb05b2
  export interface ManufacturersRenderingParams extends ReactJssModule.BaseRenderingParam {
  }
  // The Membership template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Products/Product Specific Templates/Membership
  // ID: 1193007d-5ebf-4b23-abb5-cbd5585c58e5
  export interface MembershipRenderingParams extends CommerceProductBaseRenderingParams {
    // The is_studentmembership field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 3aa37d98-25e5-4a3c-bc22-0e2d00279046
    // Custom Data:
    isStudentmembership: boolean;
    // The opt_in_for_auto_renew field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 43df5139-0631-403f-aae2-569ac7354496
    // Custom Data:
    optInForAutoRenew: boolean;
    // The validation field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: e81e4e50-0cf3-4042-95f3-644f3fe01ee1
    // Custom Data:
    validation: boolean;
  }
  // The Product Attribute template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Product Attributes/Product Attribute
  // ID: a876e3f3-e9c2-43ac-9d05-3068ccbf9a3c
  export interface ProductAttributeRenderingParams extends SynchronizationRenderingParams {
    // The Code field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: fc03e427-2d6e-4bc2-baee-a7bd00f743d3
    // Custom Data:
    code: string;
  }
  // The Product Attributes template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Product Attributes/Product Attributes
  // ID: 472bc7d1-3730-4cf6-acab-65b015c9184c
  export interface ProductAttributesRenderingParams extends ReactJssModule.BaseRenderingParam {
  }
  // The Product Attribute Value template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Product Attributes/Product Attribute Value
  // ID: 5fa8a8cc-aaa9-4fb2-898a-7d0b79dcd4c1
  export interface ProductAttributeValueRenderingParams extends SynchronizationRenderingParams {
    // The Label field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 0ac1ed9b-d768-4c4a-90e1-2d766a20df67
    // Custom Data:
    label: string;
  }
  // The Product ClassificationGroup template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Product ClassificationGroup
  // ID: cf8fec2c-96fc-46c8-8a49-7bf908482f4b
  export interface ProductClassificationGroupRenderingParams extends SynchronizationRenderingParams {
    // The Description field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: d918b5f7-75e9-4345-913c-b79e1171cc42
    // Custom Data:
    description: string;
    // The Name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 44c7d49f-0c37-4a56-b135-aa831e7f1eb9
    // Custom Data:
    name: string;
  }
  // The Product Classification template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Product Classification
  // ID: d5b1659a-83e7-485f-ad9a-555eecb564bf
  export interface ProductClassificationRenderingParams extends SynchronizationRenderingParams {
    // The Description field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 3e873f0e-4a03-4169-91af-fae982f9cbe3
    // Custom Data:
    description: string;
    // The ExternalParentID field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 008f1da3-097e-4fec-8322-9303308c63ea
    // Custom Data:
    externalParentID: string;
    // The Name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 6a65dfc6-6a35-4e8d-93ea-d1d4f40711c2
    // Custom Data:
    name: string;
  }
  // The Product Classifications template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Product Classifications
  // ID: 89c74115-3e03-4d1d-bd99-29aafcf9a649
  export interface ProductClassificationsRenderingParams extends SynchronizationRenderingParams {
    // The Description field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 645563ed-597b-4095-acc8-eda71049c3d3
    // Custom Data:
    description: string;
    // The Name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 3308641b-eb85-4fe7-8d0d-543e5b7ea4b6
    // Custom Data:
    name: string;
  }
  // The Product Classifications Specification template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Product Classifications Specification
  // ID: 9ca86513-c986-4bf3-9f9c-9791aa7ea352
  export interface ProductClassificationsSpecificationRenderingParams extends ProductSpecificationRenderingParams {
  }
  // The Product Global Specification template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Product Global Specification
  // ID: 157c6369-db59-4299-8238-365d964c0965
  export interface ProductGlobalSpecificationRenderingParams extends ProductSpecificationRenderingParams {
  }
  // The Product Hierarchy Base template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Products/Product Hierarchy Base
  // ID: 8dd26570-494d-4867-9ff6-c15e56e8feca
  export interface ProductHierarchyBaseRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The level_2_hierarchy field.
    // Short description:
    // Field Type: Multilist
    // Field ID: 691c85ee-9f50-4dfd-b3af-a6c0d1d676c9
    // Custom Data: type=Pmi.Spx.Foundation.Connect.TemplateModels.IProductAttributeValue
    level2Hierarchies: string;
    // The level_3_hierarchy field.
    // Short description:
    // Field Type: Multilist
    // Field ID: d3a4b7de-eafd-4b13-8df7-67c7220dbdda
    // Custom Data: type=Pmi.Spx.Foundation.Connect.TemplateModels.IProductAttributeValue
    level3Hierarchies: string;
    // The level_4_hierarchy field.
    // Short description:
    // Field Type: Multilist
    // Field ID: b695c62a-0edd-4951-bd55-024699c1b8ad
    // Custom Data: type=Pmi.Spx.Foundation.Connect.TemplateModels.IProductAttributeValue
    level4Hierarchies: string;
  }
  // The Product Kind template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Product Kinds/Product Kind
  // ID: 4442ed16-6ad0-483f-8fb1-6e43de7a9ee7
  export interface ProductKindRenderingParams extends SynchronizationRenderingParams {
    // The Name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 3fd3f21f-6e3c-4c34-a7c6-71f20fb71550
    // Custom Data:
    name: string;
  }
  // The Product Kinds template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Product Kinds/Product Kinds
  // ID: f106457b-dc94-4da8-a5fa-4944f1d56017
  export interface ProductKindsRenderingParams extends ReactJssModule.BaseRenderingParam {
  }
  // The Product Matching template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Products/Product Matching
  // ID: d185e14a-37bc-4cad-97af-2d34829ee819
  export interface ProductMatchingRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Product Kind field.
    // Short description:
    // Field Type: Multilist
    // Field ID: d2e0628f-f47f-479c-ad48-5fc7ce2699f0
    // Custom Data: name=ProductKind
    productKind: string;
    // The Product Type field.
    // Short description:
    // Field Type: Multilist
    // Field ID: b360298f-10b6-406e-a8ee-e3c6dcc2b8ab
    // Custom Data: name=ProductType
    productType: string;
  }
  // The Product Page template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Product Pages/Product Page
  // ID: 2838844b-92ab-4bd4-a349-25a521a16f99
  export interface ProductPageRenderingParams extends WithCategoryRenderingParams, WithProductRenderingParams, ProductMatchingRenderingParams {
  }
  // The Product Relation template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Product Relation
  // ID: 93703013-77b6-48b5-8338-c29a5d15a1a6
  export interface ProductRelationRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Description field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 609c7279-6e83-4ed2-9f7e-7e9bef1397f0
    // Custom Data:
    description: string;
    // The Name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 1ac1bfa4-4113-4c66-afe2-f3f3c0266c73
    // Custom Data:
    name: string;
    // The RelatedProducts field.
    // Short description:
    // Field Type: Multilist with Search
    // Field ID: b039e503-5eaf-4688-80a1-b9f32de6d800
    // Custom Data:
    relatedProducts: string;
    // The Type field.
    // Short description:
    // Field Type: Droplist
    // Field ID: 9d507af0-463a-4151-aaa2-8c5ad6632e36
    // Custom Data:
    type: string;
  }
  // The Product Relations template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Product Relations
  // ID: 6e5fbb20-2d51-4b63-8563-65e6e8c61dfd
  export interface ProductRelationsRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Description field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 62c925b2-b720-465b-8047-e776be22bd79
    // Custom Data:
    description: string;
  }
  // The Product template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Product
  // ID: 47d1a39e-3b4b-4428-a9f8-b446256c9581
  export interface ProductRenderingParams extends SynchronizationRenderingParams {
    // The BrandName field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 8f6025b8-1039-46ab-a606-849bfe0c6292
    // Custom Data:
    brandName: string;
    // The Divisions field.
    // Short description:
    // Field Type: Treelist
    // Field ID: 0f949b65-ee6c-4d70-b692-54d801ba812c
    // Custom Data:
    divisions: string;
    // The Full Description field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: 25fe6930-d1f7-4924-95f9-770aa320a1f9
    // Custom Data:
    fullDescription: string;
    // The Identification field.
    // Short description:
    // Field Type: Name Lookup Value List
    // Field ID: 89d22f6f-409f-4463-bb09-0a79f29b9707
    // Custom Data:
    identification: { } /* UNKNOWN TYPE: name lookup value list */;
    // The Manufacturer field.
    // Short description:
    // Field Type: Treelist with Search
    // Field ID: ea45edf7-cd8b-433f-99a6-ebfe3c7ce3a5
    // Custom Data:
    manufacturer: string;
    // The ModelName field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 7b3fad93-dcb0-4d8d-8181-13738502bea5
    // Custom Data:
    modelName: string;
    // The Name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: e2cea8d5-65f4-4789-a778-b1b400b56928
    // Custom Data:
    name: string;
    // The ProductClasses field.
    // Short description:
    // Field Type: Treelist
    // Field ID: 891039c9-fe24-46b6-a5de-ba958a6925cf
    // Custom Data:
    productClasses: string;
    // The ProductType field.
    // Short description:
    // Field Type: Treelist with Search
    // Field ID: d5806f22-e082-4c82-87bd-439a62ef92c6
    // Custom Data: type=IProductType
    productType: string;
    // The Short Description field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 87cf462a-a37a-4731-bf0b-370d3d7a9873
    // Custom Data:
    shortDescription: string;
  }
  // The Product Repository template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Product Repository
  // ID: f599bf48-d6fe-40dc-9f78-cf2d56bfb657
  export interface ProductRepositoryRenderingParams extends ReactJssModule.BaseRenderingParam {
  }
  // The Product Resource template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Product Resource
  // ID: 0d52eb5c-a153-4688-a217-151a17de321e
  export interface ProductResourceRenderingParams extends SynchronizationRenderingParams {
    // The Resource field.
    // Short description:
    // Field Type: Image
    // Field ID: fef7847e-3ac3-4c79-be15-bf407c461de6
    // Custom Data:
    resource: string;
    // The Type field.
    // Short description:
    // Field Type: Droplist
    // Field ID: 10870bdb-03dc-4eb0-922e-c891a3b96f37
    // Custom Data:
    type: string;
    // The URI field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: d2a79cb7-a015-4ba4-b63d-beb127bf5f0e
    // Custom Data:
    uri: string;
  }
  // The Product Resources template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Product Resources
  // ID: fbca6069-d6c5-493e-83d7-c4a079e2717f
  export interface ProductResourcesRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Main image field.
    // Short description:
    // Field Type: Droptree
    // Field ID: bb61c579-50c7-40e7-b32e-a66bdc5d2dc3
    // Custom Data:
    mainImage: string;
  }
  // The Product Specification template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Product Specification
  // ID: 687ebd28-8c90-4203-8527-a809d9b99680
  export interface ProductSpecificationRenderingParams extends SynchronizationRenderingParams {
    // The Group field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 6486cbd0-3e73-4293-9da9-52249bba163d
    // Custom Data:
    group: string;
    // The Key field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 88892c6a-f865-493e-82b7-5c21e67c2fa4
    // Custom Data:
    key: string;
    // The LookupValue field.
    // Short description:
    // Field Type: Treelist with Search
    // Field ID: 8377b000-0ee9-4f77-9eba-2b0822669f84
    // Custom Data:
    lookupValue: string;
    // The Value field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 59b79baf-4f30-4eca-961d-4afab14d157c
    // Custom Data:
    value: string;
  }
  // The Product Specifications template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Product Specifications
  // ID: a1441a78-21ac-483c-89a6-d46521818b94
  export interface ProductSpecificationsRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Description field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 92630a4a-369d-47ce-9ff1-a72a7dd67969
    // Custom Data:
    description: string;
    // The VariantSpecifications field.
    // Short description:
    // Field Type: Multilist
    // Field ID: 95cf928f-0ca7-4daf-b6b1-8dfe31ac9523
    // Custom Data:
    variantSpecifications: string;
  }
  // The Products template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Products
  // ID: 4d21ad2a-ba49-4dfc-9db9-c299c7fb7f6e
  export interface ProductsRenderingParams extends ReactJssModule.BaseRenderingParam {
  }
  // The Product Store template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Product Stores/Product Store
  // ID: 661d783a-fb8e-4d98-bbb4-8796e25bb2c7
  export interface ProductStoreRenderingParams extends SynchronizationRenderingParams {
    // The AvailableAddressCountries field.
    // Short description: Leave empty to allow all
    // Field Type: Multilist
    // Field ID: c8151ea4-ea10-421a-b44b-bcfc881e388a
    // Custom Data:
    availableAddressCountries: string;
    // The CustomerCareRegionalEmailAddress field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 70ab3290-122a-4b3c-83d5-dd80db4567d3
    // Custom Data:
    customerCareRegionalEmailAddress: string;
    // The CustomerCareRegionalPhoneNumber field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 24e07817-b39e-4e4e-9d21-6e901c20d3f3
    // Custom Data:
    customerCareRegionalPhoneNumber: string;
    // The DefaultCartLink field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 8c845038-eccb-4f69-a8e5-7da952cee494
    // Custom Data:
    defaultCartLink: string;
    // The EnableRemoveChapterLink field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 0b5e7902-3a05-4b20-b1d3-9b03aac17021
    // Custom Data:
    enableRemoveChapterLink: boolean;
    // The Is Default Store field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 272b3899-dd90-43be-933a-637cfbeb4872
    // Custom Data:
    isDefaultStore: boolean;
    // The Is Master Store field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: a3f214ec-d7af-4590-818b-01c9ef57d7cf
    // Custom Data:
    isMasterStore: boolean;
    // The Is Store Enabled field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 81f71b88-46cc-4d33-b28f-eb85d3b92221
    // Custom Data:
    isStoreEnabled: boolean;
    // The Is Tiered Store field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: b040c3bb-12ee-44ce-abfa-8034017d009c
    // Custom Data:
    isTieredStore: boolean;
    // The LocalCurrencies field.
    // Short description:
    // Field Type: Multilist
    // Field ID: 49ba8681-8640-4dcb-adb8-682fa369bfdb
    // Custom Data: type=Pmi.Spx.Foundation.Connect.TemplateModels.ICurrency
    localCurrencies: string;
    // The Store Countries field.
    // Short description:
    // Field Type: Multilist
    // Field ID: 97f9cbba-e6dd-4c3a-8657-b2d13f35bd5e
    // Custom Data: type=Pmi.Spx.Foundation.Connect.TemplateModels.ICountry
    storeCountries: string;
    // The Store Currency field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 68411739-4d6c-4b23-a18c-91958b64a220
    // Custom Data: type=Pmi.Spx.Foundation.Connect.TemplateModels.ICurrency
    storeCurrency: string;
    // The Store Id field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: fefd99ae-9387-4dcd-bb60-5e986b533054
    // Custom Data:
    storeId: string;
    // The Store Identifier field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 8241cef9-8088-4c7b-a667-7f9c303cc5a4
    // Custom Data:
    storeIdentifier: string;
    // The StoreLanguage field.
    // Short description:
    // Field Type: Droplist
    // Field ID: 72a58959-671e-4611-b99e-b89e69397f8d
    // Custom Data:
    storeLanguage: string;
    // The Store Name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 00184368-779d-485f-9942-3f6570158ca0
    // Custom Data:
    storeName: string;
    // The SyncCountries field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 0a28ff27-3c94-46a3-8550-21342e82c466
    // Custom Data:
    syncCountries: boolean;
    // The Tier field.
    // Short description:
    // Field Type: Droplink
    // Field ID: 87002ed3-365e-4a58-a467-768941b0d0dc
    // Custom Data: nullable=true
    tier: string;
    // The WebsiteId field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 58af6183-73bd-4775-aeb6-ee4feeda40f9
    // Custom Data:
    websiteId: string;
  }
  // The Product Stores template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Product Stores/Product Stores
  // ID: f1f86d29-d324-4d86-8da4-c56770ae9283
  export interface ProductStoresRenderingParams extends ReactJssModule.BaseRenderingParam {
  }
  // The Product Template Mapping template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Settings/Product Template Mapping
  // ID: 48856c4b-f788-4b33-bb42-10ca051a94cd
  export interface ProductTemplateMappingRenderingParams extends ProductMatchingRenderingParams {
    // The Product Template field.
    // Short description:
    // Field Type: Droplink
    // Field ID: d78d8a82-643f-417c-bd1d-3abd6cb46bad
    // Custom Data: nullable=true
    productTemplate: string;
    // The Product Template Headless field.
    // Short description:
    // Field Type: Droplink
    // Field ID: deb5fd81-4317-4ad7-a237-316446b2b345
    // Custom Data: nullable=true
    productTemplateHeadless: string;
  }
  // The Product Template Mappings template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Settings/Product Template Mappings
  // ID: 679384ac-a080-4a48-b3c2-0794fb65e4e4
  export interface ProductTemplateMappingsRenderingParams extends ReactJssModule.BaseRenderingParam {
  }
  // The Product Type template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Product Type
  // ID: 2fc31cad-946f-4944-9a98-3da167bebc34
  export interface ProductTypeRenderingParams extends SynchronizationRenderingParams {
    // The Description field.
    // Short description:
    // Field Type: Rich Text
    // Field ID: ec7d4ac3-83b6-4653-944a-097aafdfff07
    // Custom Data:
    description: string;
    // The ExternalParentID field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 5d89d406-b223-426e-a029-ce7138a9bab2
    // Custom Data:
    externalParentID: string;
    // The Name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 93e7751e-aec7-4ea7-b5d8-b6d6653b3e8b
    // Custom Data:
    name: string;
  }
  // The Product Type Specification template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Product Type Specification
  // ID: c696bec3-0414-4a3d-a4fe-3583a7ae051d
  export interface ProductTypeSpecificationRenderingParams extends ProductSpecificationRenderingParams {
  }
  // The Product Types template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Product Types
  // ID: 26d1e614-5ae9-4e58-90ab-6cf9b9b6f938
  export interface ProductTypesRenderingParams extends ReactJssModule.BaseRenderingParam {
  }
  // The Region template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Global Settings/Region
  // ID: c3a3d7c2-002d-4be9-a6d3-564481175f92
  export interface RegionRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Code field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: da530c00-2c3d-44fa-adbe-4555b94effcd
    // Custom Data:
    code: string;
    // The Name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 399c7c33-21a2-49c2-be65-d0d7ae956f0d
    // Custom Data:
    name: string;
  }
  // The REP Course template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Products/Product Specific Templates/REP Course
  // ID: ce4fd7d0-e6fa-400e-b09f-b29ddf7f2d0d
  export interface REPCourseRenderingParams extends CommerceProductBaseRenderingParams {
    // The course_level field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 3203d3ac-9b02-416a-a436-f5133c265e23
    // Custom Data:
    courseLevel: string;
    // The format field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 603747cb-0bc8-4a3d-9ccc-fd3dabcd365d
    // Custom Data:
    format: string;
    // The pdus field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: bb810404-13cd-4f78-a62d-7c2294674950
    // Custom Data:
    pdus: string;
    // The rep_provider_id field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: a437255c-8fdf-44e5-93a2-1b64deacfc5f
    // Custom Data:
    repProviderId: string;
    // The rep_provider_name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: f6da501a-305f-4467-8b50-588b8f2c336f
    // Custom Data:
    repProviderName: string;
  }
  // The REP Member template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Products/Product Specific Templates/REP Member
  // ID: 63ed873e-e791-46c5-9932-cfb9bfd66e85
  export interface REPMemberRenderingParams extends CommerceProductBaseRenderingParams {
  }
  // The Resource Folder template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Resource Folder
  // ID: a0a1342a-6339-445c-94a1-31cb7756aa0b
  export interface ResourceFolderRenderingParams extends ReactJssModule.BaseRenderingParam {
  }
  // The Search Settings template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Global Settings/Search Settings
  // ID: 5901d826-15c7-4219-a985-bcdc46736d70
  export interface SearchSettingsRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Items Per Page field.
    // Short description:
    // Field Type: Integer
    // Field ID: 35fc96a7-bf12-4c02-a66a-1ae68d3b4432
    // Custom Data:
    itemsPerPage: number;
    // The Search Facets field.
    // Short description:
    // Field Type: Multilist
    // Field ID: a3683321-18c4-424a-94aa-fc1ce9b03b92
    // Custom Data:
    searchFacets: string;
    // The Sort Fields field.
    // Short description:
    // Field Type: Multilist
    // Field ID: 91c3811d-b760-475c-87e7-0aee5d4080c9
    // Custom Data:
    sortFields: string;
  }
  // The Specification Lookup template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Specification Lookup
  // ID: 64ee9778-10d3-492a-a52e-d229ce8b9fad
  export interface SpecificationLookupRenderingParams extends SynchronizationRenderingParams {
    // The Name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: f3f37114-38b0-43df-b9c7-512b62081bf1
    // Custom Data:
    name: string;
  }
  // The Specification Lookups template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Specification Lookups
  // ID: a8a3d56b-bca0-4877-b137-86992f341d81
  export interface SpecificationLookupsRenderingParams extends ReactJssModule.BaseRenderingParam {
  }
  // The Specification template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Specification
  // ID: 284f5f48-7d2e-48d3-b3f5-c8f45549a2a0
  export interface SpecificationRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The Name field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 02eaa1d4-a90a-4124-b2ba-3453794daf10
    // Custom Data:
    name: string;
  }
  // The Synchronization template.
  // Short description:
  // Path: /sitecore/templates/CommerceConnect/Products/Synchronization
  // ID: 240334a5-cfe8-4450-bb85-253d620cba02
  export interface SynchronizationRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The ExternalID field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: f2f908f1-806e-4706-911b-6794b73576d0
    // Custom Data:
    externalID: string;
  }
  // The Webinar template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Products/Product Specific Templates/Webinar
  // ID: 805fa044-5fd1-45c2-acf2-b199d9dd2366
  export interface WebinarRenderingParams extends CommerceProductBaseRenderingParams {
    // The course_level field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 10662483-c0e9-4e7c-81b0-58f7db29f0b0
    // Custom Data:
    courseLevel: string;
    // The format field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 67596356-2d5c-4213-8a6f-e55536ab098a
    // Custom Data:
    format: string;
    // The pdus field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: c48a932b-6fa1-472a-b7ae-1b02c6d59853
    // Custom Data:
    pdus: string;
  }
  // The _With Category template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Tokens/_With Category
  // ID: ebd414c1-b36d-4efe-b41f-6ef2643ad09b
  export interface WithCategoryRenderingParams extends ReactJssModule.BaseRenderingParam {
  }
  // The _With Product template.
  // Short description:
  // Path: /sitecore/templates/SPX/Foundation/Connect/Tokens/_With Product
  // ID: 818c94a3-4023-4e2e-b93e-7a2be09eab9a
  export interface WithProductRenderingParams extends ReactJssModule.BaseRenderingParam {
  }
  export namespace BaseIconTemplate {
    export const templateId: string = 'a220c0f8-274a-4fc5-9896-6fa249935468';
    export const templateName: string = '_BaseIcon';
    export const iconFieldId: string = '734b447e-44ec-4739-8acc-b89fd51e1fec';
    export const iconFieldName: string = 'Icon';
    export const iconClassFieldId: string = '4796a76c-5867-4640-9dca-8bb7a17fb185';
    export const iconClassFieldName: string = 'IconClass';
  }
  export namespace BaseTitleTemplate {
    export const templateId: string = 'ab8b7fa5-83b6-4713-8a1e-ff29f10192df';
    export const templateName: string = '_BaseTitle';
    export const titleFieldId: string = '56d15ac6-4a9b-4e75-830c-a30e63384011';
    export const titleFieldName: string = 'Title';
  }
  export namespace BookTemplate {
    export const templateId: string = '9bb56de8-091b-440a-856c-cb71437914c3';
    export const templateName: string = 'Book';
    export const authorNameFieldId: string = '1c6bd3f5-fc36-4d79-af71-a20023887706';
    export const authorNameFieldName: string = 'author_name';
    export const bookFormatFieldId: string = '55b0d64f-c2b8-4e11-a675-8a19fea7d25b';
    export const bookFormatFieldName: string = 'book_format';
    export const datePublishedFieldId: string = 'de5684ee-a60b-4c37-9658-d49846ee87ec';
    export const datePublishedFieldName: string = 'date_published';
    export const isbn13NumberFieldId: string = 'cbccc2bf-cdae-468c-8735-192e5e1a7144';
    export const isbn13NumberFieldName: string = 'isbn13_number';
    export const languageFormatFieldId: string = '0ea6d4eb-e7da-4c3e-9865-f455700e3d44';
    export const languageFormatFieldName: string = 'language_format';
    export const numberPagesFieldId: string = '8b2a09e5-b2f2-4df5-899c-d17ebab22f6f';
    export const numberPagesFieldName: string = 'number_pages';
    export const preorderableFieldId: string = '4d3b0adb-4bc8-4b8c-9a1a-f8c4c6bcb42f';
    export const preorderableFieldName: string = 'preorderable';
    export const publisherNameFieldId: string = '13e0af40-3c77-42fb-bbba-acfae6ea629f';
    export const publisherNameFieldName: string = 'publisher_name';
    export const subjectFieldId: string = 'c7bc10cd-061c-4cef-8293-7b2465712c97';
    export const subjectFieldName: string = 'subject';
    export const taxClassIdFieldId: string = '74e6405a-e1ea-427d-b2bb-2b2ee111c879';
    export const taxClassIdFieldName: string = 'tax_class_id';
    export const b2bFullDescriptionFieldId: string = 'e919cee7-a090-4848-b062-626a9e3c2ea7';
    export const b2bFullDescriptionFieldName: string = 'B2b Full Description';
    export const b2bImageFieldId: string = 'a88b0bd5-dc5c-4049-b3ae-5e48fa881c93';
    export const b2bImageFieldName: string = 'B2b image';
    export const b2bShortDescriptionFieldId: string = '540b1fca-a499-4ebd-80a0-cf252801e994';
    export const b2bShortDescriptionFieldName: string = 'B2b Short Description';
    export const b2bTitleFieldId: string = '15389578-4623-4398-901c-e9a7b5533a45';
    export const b2bTitleFieldName: string = 'B2b Title';
    export const bundleProductOptionsFieldId: string = '60f860f0-69cb-4834-95a7-1391ba665a59';
    export const bundleProductOptionsFieldName: string = 'BundleProductOptions';
    export const productCustomOptionsFieldId: string = '3350a005-c47e-461c-b6ce-17e223960c71';
    export const productCustomOptionsFieldName: string = 'ProductCustomOptions';
    export const backordersFieldId: string = '6d849509-14d3-492a-b49a-550291a14caa';
    export const backordersFieldName: string = 'Backorders';
    export const courseKitTypeFieldId: string = '1e58883a-96ca-4e89-9b68-953a9678734c';
    export const courseKitTypeFieldName: string = 'course_kit_type';
    export const courseTypesFieldId: string = '5fc60777-de7e-4055-945e-6445f00f41a3';
    export const courseTypesFieldName: string = 'course_type';
    export const disabledProductURLFieldId: string = '0807e662-73c5-41c4-a975-96fd514e0ff9';
    export const disabledProductURLFieldName: string = 'disabled_Product_URL';
    export const fulfillmentProviderFieldId: string = '5ca91282-238c-4179-a84b-d7a777753a16';
    export const fulfillmentProviderFieldName: string = 'fulfillment_provider';
    export const includedInMembershipFieldId: string = 'eb7fc950-cb4c-41e4-82d1-747429338465';
    export const includedInMembershipFieldName: string = 'included_in_membership';
    export const isInStockFieldId: string = 'bb29b6b4-3456-4d3a-94db-b02656ca47a3';
    export const isInStockFieldName: string = 'Is In Stock';
    export const isReturnableFieldId: string = 'cae45dfb-0c62-4857-a91a-b72b50354c7d';
    export const isReturnableFieldName: string = 'Is Returnable';
    export const isMembershipFieldId: string = '47e537b2-9d39-47db-81f7-295157ed880e';
    export const isMembershipFieldName: string = 'is_membership';
    export const isSubscriptionProductFieldId: string = '4b7c5ef9-08c0-41c5-aa41-278641707375';
    export const isSubscriptionProductFieldName: string = 'is_subscription_product';
    export const languageFieldId: string = '595359da-ec9f-4c4c-a6a2-7546c7ef1240';
    export const languageFieldName: string = 'language';
    export const overviewDescriptionFieldId: string = 'df536977-1ee5-47e9-b7f6-11f25a9d9330';
    export const overviewDescriptionFieldName: string = 'OverviewDescription';
    export const pmiCatalogsFieldId: string = '8e050945-712f-407c-8c38-8835b5488376';
    export const pmiCatalogsFieldName: string = 'pmi_catalog';
    export const pmiProductCategoryFieldId: string = '3ce1903b-97fa-4f44-9395-4871dd6b91e0';
    export const pmiProductCategoryFieldName: string = 'pmi_product_category';
    export const pmiProductFamilyFieldId: string = '8a3f0338-265e-445d-8d06-47fb117c7bc4';
    export const pmiProductFamilyFieldName: string = 'pmi_product_family';
    export const pmiProductFormatFieldId: string = '8f191f3e-4c52-4091-a6dd-78915bc7af3a';
    export const pmiProductFormatFieldName: string = 'pmi_product_format';
    export const pmiProductTypeFieldId: string = 'fdb83207-3c1c-4566-ba47-d28568bbdeba';
    export const pmiProductTypeFieldName: string = 'pmi_product_type';
    export const productKindFieldId: string = '3d5ca766-9602-4a5b-aff6-8fcb50fca321';
    export const productKindFieldName: string = 'Product Kind';
    export const productStoreFieldId: string = '8e4499d7-9df2-4e4a-a170-3342dc989426';
    export const productStoreFieldName: string = 'Product Store';
    export const quantityFieldId: string = '549f9561-22be-4070-84f1-c82639c08c6d';
    export const quantityFieldName: string = 'Quantity';
    export const singleMembershipFieldId: string = '45cef470-41fe-48e2-835d-f7cc180fa1a5';
    export const singleMembershipFieldName: string = 'single_membership';
    export const soldAsBundleFieldId: string = '42c17217-ecc2-4d07-8d35-c5913a94d788';
    export const soldAsBundleFieldName: string = 'sold_as_bundle';
    export const subscriptionFrequencyFieldId: string = 'c2ddffb8-5490-4ebf-ad2e-1b220a466c5a';
    export const subscriptionFrequencyFieldName: string = 'subscription_frequency';
    export const weightFieldId: string = 'f3ef8df0-e281-4d4e-bfba-7d950d517170';
    export const weightFieldName: string = 'Weight';
    export const pmiProductIdFieldId: string = 'aa683096-5550-4fee-bf74-ffa9a08957bc';
    export const pmiProductIdFieldName: string = 'pmi_product_id';
    export const membershipTextHighlightColorFieldId: string = 'b18d1660-c856-4d2f-8e3d-c676d4a7421e';
    export const membershipTextHighlightColorFieldName: string = 'membershipTextHighlightColor';
    export const recommendedAltTextFieldId: string = '3568a091-d725-4a13-9b45-fb21802c9ca3';
    export const recommendedAltTextFieldName: string = 'RecommendedAltText';
    export const showCTAFieldId: string = '402d02c0-c629-4592-b316-2d1cfd687197';
    export const showCTAFieldName: string = 'showCTA';
    export const showPricesFieldId: string = '8318d912-a453-4e22-8ca2-6ddbc96c7646';
    export const showPricesFieldName: string = 'showPrices';
    export const customCartFieldId: string = 'b8fa0117-1511-40bb-95ec-7b265962cec0';
    export const customCartFieldName: string = 'custom_cart';
    export const heroBackgroundImageFieldId: string = '6d95f762-44dd-4123-8b31-36a03113d48a';
    export const heroBackgroundImageFieldName: string = 'HeroBackgroundImage';
    export const heroBackgroundImageMobileFieldId: string = 'daf9a28d-b84c-4a67-8595-a297c050909f';
    export const heroBackgroundImageMobileFieldName: string = 'HeroBackgroundImageMobile';
    export const heroImageFieldId: string = '5a0d0f52-211e-4e3c-ab2f-71726cbd9057';
    export const heroImageFieldName: string = 'HeroImage';
    export const imageFieldId: string = 'c89f0ba8-67f4-44f6-a7f1-f68a46cdbcb6';
    export const imageFieldName: string = 'Image';
    export const smallImageFieldId: string = 'f482cd3c-6b97-48c3-ab80-a898ca3f874b';
    export const smallImageFieldName: string = 'Small Image';
    export const thumbnailFieldId: string = '2e2436e4-e5c4-4a6c-909e-1eb7931cb2d9';
    export const thumbnailFieldName: string = 'Thumbnail';
    export const useDarkThemeForHeroFieldId: string = '7bdf557d-6804-49e5-acc8-a1835731babc';
    export const useDarkThemeForHeroFieldName: string = 'UseDarkThemeForHero';
    export const isFeaturedFieldId: string = '3bfaed00-9aab-42c8-9fb7-8e8d1f0027a6';
    export const isFeaturedFieldName: string = 'Is Featured';
    export const promoBadgesFieldId: string = '23ee2ae2-1d4d-4e51-8e2a-b251e55e8a0d';
    export const promoBadgesFieldName: string = 'PromoBadges';
    export const seoFriendlyTitleFieldId: string = '260a336f-7541-4b8c-a023-3198ca421c86';
    export const seoFriendlyTitleFieldName: string = 'SeoFriendlyTitle';
    export const wordmarkFieldId: string = '1fc37bfb-e2f6-4337-b793-b12477c305bd';
    export const wordmarkFieldName: string = 'Wordmark';
    export const isDeletedFieldId: string = '2aaa5d2e-4301-40ad-b6f9-d752c5fad3e3';
    export const isDeletedFieldName: string = 'Is Deleted';
    export const pmiMembershipFieldId: string = '50845396-2667-4e2a-a548-93c81fc48043';
    export const pmiMembershipFieldName: string = 'PMI Membership';
    export const pmiSegmentFieldId: string = '3b0804fc-df69-4424-9d09-892341852d72';
    export const pmiSegmentFieldName: string = 'PMI Segment';
    export const abPricesFieldId: string = '97749ffe-4bd0-4190-abe1-c92c68de1397';
    export const abPricesFieldName: string = 'AB Prices';
    export const countryPricesFieldId: string = '202e0c1e-5500-46be-bb5d-14a400eb6130';
    export const countryPricesFieldName: string = 'Country Prices';
    export const globalPriceFieldId: string = '3a732fb5-16ff-448d-932a-143810b5f682';
    export const globalPriceFieldName: string = 'Global Price';
    export const groupPricesFieldId: string = 'da043375-9353-44e1-b45c-d4ed88459ebf';
    export const groupPricesFieldName: string = 'Group Prices';
    export const renewalPriceFieldId: string = 'b3f78b0d-bf55-4b73-b003-dbafdb00ccb3';
    export const renewalPriceFieldName: string = 'Renewal Price';
    export const zonePricesFieldId: string = 'babb6e53-3777-4916-a764-01aad64cce60';
    export const zonePricesFieldName: string = 'Zone Prices';
    export const brandNameFieldId: string = '8f6025b8-1039-46ab-a606-849bfe0c6292';
    export const brandNameFieldName: string = 'BrandName';
    export const divisionsFieldId: string = '0f949b65-ee6c-4d70-b692-54d801ba812c';
    export const divisionsFieldName: string = 'Divisions';
    export const fullDescriptionFieldId: string = '25fe6930-d1f7-4924-95f9-770aa320a1f9';
    export const fullDescriptionFieldName: string = 'Full Description';
    export const identificationFieldId: string = '89d22f6f-409f-4463-bb09-0a79f29b9707';
    export const identificationFieldName: string = 'Identification';
    export const manufacturerFieldId: string = 'ea45edf7-cd8b-433f-99a6-ebfe3c7ce3a5';
    export const manufacturerFieldName: string = 'Manufacturer';
    export const modelNameFieldId: string = '7b3fad93-dcb0-4d8d-8181-13738502bea5';
    export const modelNameFieldName: string = 'ModelName';
    export const nameFieldId: string = 'e2cea8d5-65f4-4789-a778-b1b400b56928';
    export const nameFieldName: string = 'Name';
    export const productClassesFieldId: string = '891039c9-fe24-46b6-a5de-ba958a6925cf';
    export const productClassesFieldName: string = 'ProductClasses';
    export const productTypeFieldId: string = 'd5806f22-e082-4c82-87bd-439a62ef92c6';
    export const productTypeFieldName: string = 'ProductType';
    export const shortDescriptionFieldId: string = '87cf462a-a37a-4731-bf0b-370d3d7a9873';
    export const shortDescriptionFieldName: string = 'Short Description';
    export const externalIDFieldId: string = 'f2f908f1-806e-4706-911b-6794b73576d0';
    export const externalIDFieldName: string = 'ExternalID';
    export const shortTitleFieldId: string = 'e27e197c-ad78-4352-bb04-cb7da291a88e';
    export const shortTitleFieldName: string = 'Short Title';
    export const titleFieldId: string = '56d15ac6-4a9b-4e75-830c-a30e63384011';
    export const titleFieldName: string = 'Title';
    export const iconFieldId: string = '734b447e-44ec-4739-8acc-b89fd51e1fec';
    export const iconFieldName: string = 'Icon';
    export const iconClassFieldId: string = '4796a76c-5867-4640-9dca-8bb7a17fb185';
    export const iconClassFieldName: string = 'IconClass';
    export const level2HierarchiesFieldId: string = '691c85ee-9f50-4dfd-b3af-a6c0d1d676c9';
    export const level2HierarchiesFieldName: string = 'level_2_hierarchy';
    export const level3HierarchiesFieldId: string = 'd3a4b7de-eafd-4b13-8df7-67c7220dbdda';
    export const level3HierarchiesFieldName: string = 'level_3_hierarchy';
    export const level4HierarchiesFieldId: string = 'b695c62a-0edd-4951-bd55-024699c1b8ad';
    export const level4HierarchiesFieldName: string = 'level_4_hierarchy';
  }
  export namespace CardTypesTemplate {
    export const templateId: string = '1e2dbf82-addc-4b08-ace9-00771c27818a';
    export const templateName: string = 'CardTypes';
  }
  export namespace CardTypeTemplate {
    export const templateId: string = 'ce02bea5-5b2f-43ba-baa0-9abe508a9951';
    export const templateName: string = 'CardType';
    export const cardDescriptionFieldId: string = 'faf342aa-e5dc-4bff-bc77-ff30bf0436a8';
    export const cardDescriptionFieldName: string = 'Card Description';
    export const cardIDFieldId: string = 'edb01ed2-fa72-4fd3-bb0a-48c0ca30947b';
    export const cardIDFieldName: string = 'Card ID';
  }
  export namespace CertificationTemplate {
    export const templateId: string = '3fcc6991-b4a3-4221-90e6-202862e4ffc5';
    export const templateName: string = 'Certification';
    export const credentialFieldId: string = '0e324b88-5ccc-4a27-8b9f-d69064a01a7b';
    export const credentialFieldName: string = 'credential';
    export const examtypeFieldId: string = '312aa28f-5139-48f0-b07d-48169506883e';
    export const examtypeFieldName: string = 'examtype';
    export const ordertypeFieldId: string = '33f47df4-5427-4e88-8441-2f487c66f290';
    export const ordertypeFieldName: string = 'ordertype';
    export const retakeFieldId: string = '69dc5886-0d92-4083-88bc-bf3f1164bc1c';
    export const retakeFieldName: string = 'retake';
    export const studentbundleFieldId: string = '98598266-c002-4063-8a45-df29bc7fe40e';
    export const studentbundleFieldName: string = 'studentbundle';
    export const b2bFullDescriptionFieldId: string = 'e919cee7-a090-4848-b062-626a9e3c2ea7';
    export const b2bFullDescriptionFieldName: string = 'B2b Full Description';
    export const b2bImageFieldId: string = 'a88b0bd5-dc5c-4049-b3ae-5e48fa881c93';
    export const b2bImageFieldName: string = 'B2b image';
    export const b2bShortDescriptionFieldId: string = '540b1fca-a499-4ebd-80a0-cf252801e994';
    export const b2bShortDescriptionFieldName: string = 'B2b Short Description';
    export const b2bTitleFieldId: string = '15389578-4623-4398-901c-e9a7b5533a45';
    export const b2bTitleFieldName: string = 'B2b Title';
    export const bundleProductOptionsFieldId: string = '60f860f0-69cb-4834-95a7-1391ba665a59';
    export const bundleProductOptionsFieldName: string = 'BundleProductOptions';
    export const productCustomOptionsFieldId: string = '3350a005-c47e-461c-b6ce-17e223960c71';
    export const productCustomOptionsFieldName: string = 'ProductCustomOptions';
    export const backordersFieldId: string = '6d849509-14d3-492a-b49a-550291a14caa';
    export const backordersFieldName: string = 'Backorders';
    export const courseKitTypeFieldId: string = '1e58883a-96ca-4e89-9b68-953a9678734c';
    export const courseKitTypeFieldName: string = 'course_kit_type';
    export const courseTypesFieldId: string = '5fc60777-de7e-4055-945e-6445f00f41a3';
    export const courseTypesFieldName: string = 'course_type';
    export const disabledProductURLFieldId: string = '0807e662-73c5-41c4-a975-96fd514e0ff9';
    export const disabledProductURLFieldName: string = 'disabled_Product_URL';
    export const fulfillmentProviderFieldId: string = '5ca91282-238c-4179-a84b-d7a777753a16';
    export const fulfillmentProviderFieldName: string = 'fulfillment_provider';
    export const includedInMembershipFieldId: string = 'eb7fc950-cb4c-41e4-82d1-747429338465';
    export const includedInMembershipFieldName: string = 'included_in_membership';
    export const isInStockFieldId: string = 'bb29b6b4-3456-4d3a-94db-b02656ca47a3';
    export const isInStockFieldName: string = 'Is In Stock';
    export const isReturnableFieldId: string = 'cae45dfb-0c62-4857-a91a-b72b50354c7d';
    export const isReturnableFieldName: string = 'Is Returnable';
    export const isMembershipFieldId: string = '47e537b2-9d39-47db-81f7-295157ed880e';
    export const isMembershipFieldName: string = 'is_membership';
    export const isSubscriptionProductFieldId: string = '4b7c5ef9-08c0-41c5-aa41-278641707375';
    export const isSubscriptionProductFieldName: string = 'is_subscription_product';
    export const languageFieldId: string = '595359da-ec9f-4c4c-a6a2-7546c7ef1240';
    export const languageFieldName: string = 'language';
    export const overviewDescriptionFieldId: string = 'df536977-1ee5-47e9-b7f6-11f25a9d9330';
    export const overviewDescriptionFieldName: string = 'OverviewDescription';
    export const pmiCatalogsFieldId: string = '8e050945-712f-407c-8c38-8835b5488376';
    export const pmiCatalogsFieldName: string = 'pmi_catalog';
    export const pmiProductCategoryFieldId: string = '3ce1903b-97fa-4f44-9395-4871dd6b91e0';
    export const pmiProductCategoryFieldName: string = 'pmi_product_category';
    export const pmiProductFamilyFieldId: string = '8a3f0338-265e-445d-8d06-47fb117c7bc4';
    export const pmiProductFamilyFieldName: string = 'pmi_product_family';
    export const pmiProductFormatFieldId: string = '8f191f3e-4c52-4091-a6dd-78915bc7af3a';
    export const pmiProductFormatFieldName: string = 'pmi_product_format';
    export const pmiProductTypeFieldId: string = 'fdb83207-3c1c-4566-ba47-d28568bbdeba';
    export const pmiProductTypeFieldName: string = 'pmi_product_type';
    export const productKindFieldId: string = '3d5ca766-9602-4a5b-aff6-8fcb50fca321';
    export const productKindFieldName: string = 'Product Kind';
    export const productStoreFieldId: string = '8e4499d7-9df2-4e4a-a170-3342dc989426';
    export const productStoreFieldName: string = 'Product Store';
    export const quantityFieldId: string = '549f9561-22be-4070-84f1-c82639c08c6d';
    export const quantityFieldName: string = 'Quantity';
    export const singleMembershipFieldId: string = '45cef470-41fe-48e2-835d-f7cc180fa1a5';
    export const singleMembershipFieldName: string = 'single_membership';
    export const soldAsBundleFieldId: string = '42c17217-ecc2-4d07-8d35-c5913a94d788';
    export const soldAsBundleFieldName: string = 'sold_as_bundle';
    export const subscriptionFrequencyFieldId: string = 'c2ddffb8-5490-4ebf-ad2e-1b220a466c5a';
    export const subscriptionFrequencyFieldName: string = 'subscription_frequency';
    export const weightFieldId: string = 'f3ef8df0-e281-4d4e-bfba-7d950d517170';
    export const weightFieldName: string = 'Weight';
    export const pmiProductIdFieldId: string = 'aa683096-5550-4fee-bf74-ffa9a08957bc';
    export const pmiProductIdFieldName: string = 'pmi_product_id';
    export const membershipTextHighlightColorFieldId: string = 'b18d1660-c856-4d2f-8e3d-c676d4a7421e';
    export const membershipTextHighlightColorFieldName: string = 'membershipTextHighlightColor';
    export const recommendedAltTextFieldId: string = '3568a091-d725-4a13-9b45-fb21802c9ca3';
    export const recommendedAltTextFieldName: string = 'RecommendedAltText';
    export const showCTAFieldId: string = '402d02c0-c629-4592-b316-2d1cfd687197';
    export const showCTAFieldName: string = 'showCTA';
    export const showPricesFieldId: string = '8318d912-a453-4e22-8ca2-6ddbc96c7646';
    export const showPricesFieldName: string = 'showPrices';
    export const customCartFieldId: string = 'b8fa0117-1511-40bb-95ec-7b265962cec0';
    export const customCartFieldName: string = 'custom_cart';
    export const heroBackgroundImageFieldId: string = '6d95f762-44dd-4123-8b31-36a03113d48a';
    export const heroBackgroundImageFieldName: string = 'HeroBackgroundImage';
    export const heroBackgroundImageMobileFieldId: string = 'daf9a28d-b84c-4a67-8595-a297c050909f';
    export const heroBackgroundImageMobileFieldName: string = 'HeroBackgroundImageMobile';
    export const heroImageFieldId: string = '5a0d0f52-211e-4e3c-ab2f-71726cbd9057';
    export const heroImageFieldName: string = 'HeroImage';
    export const imageFieldId: string = 'c89f0ba8-67f4-44f6-a7f1-f68a46cdbcb6';
    export const imageFieldName: string = 'Image';
    export const smallImageFieldId: string = 'f482cd3c-6b97-48c3-ab80-a898ca3f874b';
    export const smallImageFieldName: string = 'Small Image';
    export const thumbnailFieldId: string = '2e2436e4-e5c4-4a6c-909e-1eb7931cb2d9';
    export const thumbnailFieldName: string = 'Thumbnail';
    export const useDarkThemeForHeroFieldId: string = '7bdf557d-6804-49e5-acc8-a1835731babc';
    export const useDarkThemeForHeroFieldName: string = 'UseDarkThemeForHero';
    export const isFeaturedFieldId: string = '3bfaed00-9aab-42c8-9fb7-8e8d1f0027a6';
    export const isFeaturedFieldName: string = 'Is Featured';
    export const promoBadgesFieldId: string = '23ee2ae2-1d4d-4e51-8e2a-b251e55e8a0d';
    export const promoBadgesFieldName: string = 'PromoBadges';
    export const seoFriendlyTitleFieldId: string = '260a336f-7541-4b8c-a023-3198ca421c86';
    export const seoFriendlyTitleFieldName: string = 'SeoFriendlyTitle';
    export const wordmarkFieldId: string = '1fc37bfb-e2f6-4337-b793-b12477c305bd';
    export const wordmarkFieldName: string = 'Wordmark';
    export const isDeletedFieldId: string = '2aaa5d2e-4301-40ad-b6f9-d752c5fad3e3';
    export const isDeletedFieldName: string = 'Is Deleted';
    export const pmiMembershipFieldId: string = '50845396-2667-4e2a-a548-93c81fc48043';
    export const pmiMembershipFieldName: string = 'PMI Membership';
    export const pmiSegmentFieldId: string = '3b0804fc-df69-4424-9d09-892341852d72';
    export const pmiSegmentFieldName: string = 'PMI Segment';
    export const abPricesFieldId: string = '97749ffe-4bd0-4190-abe1-c92c68de1397';
    export const abPricesFieldName: string = 'AB Prices';
    export const countryPricesFieldId: string = '202e0c1e-5500-46be-bb5d-14a400eb6130';
    export const countryPricesFieldName: string = 'Country Prices';
    export const globalPriceFieldId: string = '3a732fb5-16ff-448d-932a-143810b5f682';
    export const globalPriceFieldName: string = 'Global Price';
    export const groupPricesFieldId: string = 'da043375-9353-44e1-b45c-d4ed88459ebf';
    export const groupPricesFieldName: string = 'Group Prices';
    export const renewalPriceFieldId: string = 'b3f78b0d-bf55-4b73-b003-dbafdb00ccb3';
    export const renewalPriceFieldName: string = 'Renewal Price';
    export const zonePricesFieldId: string = 'babb6e53-3777-4916-a764-01aad64cce60';
    export const zonePricesFieldName: string = 'Zone Prices';
    export const brandNameFieldId: string = '8f6025b8-1039-46ab-a606-849bfe0c6292';
    export const brandNameFieldName: string = 'BrandName';
    export const divisionsFieldId: string = '0f949b65-ee6c-4d70-b692-54d801ba812c';
    export const divisionsFieldName: string = 'Divisions';
    export const fullDescriptionFieldId: string = '25fe6930-d1f7-4924-95f9-770aa320a1f9';
    export const fullDescriptionFieldName: string = 'Full Description';
    export const identificationFieldId: string = '89d22f6f-409f-4463-bb09-0a79f29b9707';
    export const identificationFieldName: string = 'Identification';
    export const manufacturerFieldId: string = 'ea45edf7-cd8b-433f-99a6-ebfe3c7ce3a5';
    export const manufacturerFieldName: string = 'Manufacturer';
    export const modelNameFieldId: string = '7b3fad93-dcb0-4d8d-8181-13738502bea5';
    export const modelNameFieldName: string = 'ModelName';
    export const nameFieldId: string = 'e2cea8d5-65f4-4789-a778-b1b400b56928';
    export const nameFieldName: string = 'Name';
    export const productClassesFieldId: string = '891039c9-fe24-46b6-a5de-ba958a6925cf';
    export const productClassesFieldName: string = 'ProductClasses';
    export const productTypeFieldId: string = 'd5806f22-e082-4c82-87bd-439a62ef92c6';
    export const productTypeFieldName: string = 'ProductType';
    export const shortDescriptionFieldId: string = '87cf462a-a37a-4731-bf0b-370d3d7a9873';
    export const shortDescriptionFieldName: string = 'Short Description';
    export const externalIDFieldId: string = 'f2f908f1-806e-4706-911b-6794b73576d0';
    export const externalIDFieldName: string = 'ExternalID';
    export const shortTitleFieldId: string = 'e27e197c-ad78-4352-bb04-cb7da291a88e';
    export const shortTitleFieldName: string = 'Short Title';
    export const titleFieldId: string = '56d15ac6-4a9b-4e75-830c-a30e63384011';
    export const titleFieldName: string = 'Title';
    export const iconFieldId: string = '734b447e-44ec-4739-8acc-b89fd51e1fec';
    export const iconFieldName: string = 'Icon';
    export const iconClassFieldId: string = '4796a76c-5867-4640-9dca-8bb7a17fb185';
    export const iconClassFieldName: string = 'IconClass';
    export const level2HierarchiesFieldId: string = '691c85ee-9f50-4dfd-b3af-a6c0d1d676c9';
    export const level2HierarchiesFieldName: string = 'level_2_hierarchy';
    export const level3HierarchiesFieldId: string = 'd3a4b7de-eafd-4b13-8df7-67c7220dbdda';
    export const level3HierarchiesFieldName: string = 'level_3_hierarchy';
    export const level4HierarchiesFieldId: string = 'b695c62a-0edd-4951-bd55-024699c1b8ad';
    export const level4HierarchiesFieldName: string = 'level_4_hierarchy';
  }
  export namespace ChapterTemplate {
    export const templateId: string = '96f5afa4-8b80-483c-98e8-259d646c9eec';
    export const templateName: string = 'Chapter';
    export const chapterCityFieldId: string = 'f198a68e-ebcc-4a08-848b-e0f4f8989cbe';
    export const chapterCityFieldName: string = 'chapter_city';
    export const chapterCodeFieldId: string = 'c2e05a55-e899-4a37-b186-91c9c81085f1';
    export const chapterCodeFieldName: string = 'chapter_code';
    export const chapterEmailFieldId: string = '51261ff3-a674-4e10-a34f-5862f78f1d47';
    export const chapterEmailFieldName: string = 'chapter_email';
    export const chapterPhoneFieldId: string = '25307bf5-cc6d-4d24-a434-348906df7420';
    export const chapterPhoneFieldName: string = 'chapter_phone';
    export const chapterStateFieldId: string = '2343e963-cd03-49b4-bcf0-1e25bc55b102';
    export const chapterStateFieldName: string = 'chapter_state';
    export const charterStatusFieldId: string = 'e32c2c67-2af2-42ad-ac14-db876b8d93dd';
    export const charterStatusFieldName: string = 'charter_status';
    export const charterYearFieldId: string = 'cd12b58d-91a8-4536-8f3e-2ddb0682783b';
    export const charterYearFieldName: string = 'charter_year';
    export const countryFieldId: string = '117d48ee-b23f-49a2-b77d-7b15d5ce3a53';
    export const countryFieldName: string = 'country';
    export const regionFieldId: string = '4375df1f-6874-4e61-9709-1c5b065b71ab';
    export const regionFieldName: string = 'region';
    export const urlFieldId: string = '3291a8f9-4bcb-42c8-94dc-cc965800854e';
    export const urlFieldName: string = 'url';
    export const b2bFullDescriptionFieldId: string = 'e919cee7-a090-4848-b062-626a9e3c2ea7';
    export const b2bFullDescriptionFieldName: string = 'B2b Full Description';
    export const b2bImageFieldId: string = 'a88b0bd5-dc5c-4049-b3ae-5e48fa881c93';
    export const b2bImageFieldName: string = 'B2b image';
    export const b2bShortDescriptionFieldId: string = '540b1fca-a499-4ebd-80a0-cf252801e994';
    export const b2bShortDescriptionFieldName: string = 'B2b Short Description';
    export const b2bTitleFieldId: string = '15389578-4623-4398-901c-e9a7b5533a45';
    export const b2bTitleFieldName: string = 'B2b Title';
    export const bundleProductOptionsFieldId: string = '60f860f0-69cb-4834-95a7-1391ba665a59';
    export const bundleProductOptionsFieldName: string = 'BundleProductOptions';
    export const productCustomOptionsFieldId: string = '3350a005-c47e-461c-b6ce-17e223960c71';
    export const productCustomOptionsFieldName: string = 'ProductCustomOptions';
    export const backordersFieldId: string = '6d849509-14d3-492a-b49a-550291a14caa';
    export const backordersFieldName: string = 'Backorders';
    export const courseKitTypeFieldId: string = '1e58883a-96ca-4e89-9b68-953a9678734c';
    export const courseKitTypeFieldName: string = 'course_kit_type';
    export const courseTypesFieldId: string = '5fc60777-de7e-4055-945e-6445f00f41a3';
    export const courseTypesFieldName: string = 'course_type';
    export const disabledProductURLFieldId: string = '0807e662-73c5-41c4-a975-96fd514e0ff9';
    export const disabledProductURLFieldName: string = 'disabled_Product_URL';
    export const fulfillmentProviderFieldId: string = '5ca91282-238c-4179-a84b-d7a777753a16';
    export const fulfillmentProviderFieldName: string = 'fulfillment_provider';
    export const includedInMembershipFieldId: string = 'eb7fc950-cb4c-41e4-82d1-747429338465';
    export const includedInMembershipFieldName: string = 'included_in_membership';
    export const isInStockFieldId: string = 'bb29b6b4-3456-4d3a-94db-b02656ca47a3';
    export const isInStockFieldName: string = 'Is In Stock';
    export const isReturnableFieldId: string = 'cae45dfb-0c62-4857-a91a-b72b50354c7d';
    export const isReturnableFieldName: string = 'Is Returnable';
    export const isMembershipFieldId: string = '47e537b2-9d39-47db-81f7-295157ed880e';
    export const isMembershipFieldName: string = 'is_membership';
    export const isSubscriptionProductFieldId: string = '4b7c5ef9-08c0-41c5-aa41-278641707375';
    export const isSubscriptionProductFieldName: string = 'is_subscription_product';
    export const languageFieldId: string = '595359da-ec9f-4c4c-a6a2-7546c7ef1240';
    export const languageFieldName: string = 'language';
    export const overviewDescriptionFieldId: string = 'df536977-1ee5-47e9-b7f6-11f25a9d9330';
    export const overviewDescriptionFieldName: string = 'OverviewDescription';
    export const pmiCatalogsFieldId: string = '8e050945-712f-407c-8c38-8835b5488376';
    export const pmiCatalogsFieldName: string = 'pmi_catalog';
    export const pmiProductCategoryFieldId: string = '3ce1903b-97fa-4f44-9395-4871dd6b91e0';
    export const pmiProductCategoryFieldName: string = 'pmi_product_category';
    export const pmiProductFamilyFieldId: string = '8a3f0338-265e-445d-8d06-47fb117c7bc4';
    export const pmiProductFamilyFieldName: string = 'pmi_product_family';
    export const pmiProductFormatFieldId: string = '8f191f3e-4c52-4091-a6dd-78915bc7af3a';
    export const pmiProductFormatFieldName: string = 'pmi_product_format';
    export const pmiProductTypeFieldId: string = 'fdb83207-3c1c-4566-ba47-d28568bbdeba';
    export const pmiProductTypeFieldName: string = 'pmi_product_type';
    export const productKindFieldId: string = '3d5ca766-9602-4a5b-aff6-8fcb50fca321';
    export const productKindFieldName: string = 'Product Kind';
    export const productStoreFieldId: string = '8e4499d7-9df2-4e4a-a170-3342dc989426';
    export const productStoreFieldName: string = 'Product Store';
    export const quantityFieldId: string = '549f9561-22be-4070-84f1-c82639c08c6d';
    export const quantityFieldName: string = 'Quantity';
    export const singleMembershipFieldId: string = '45cef470-41fe-48e2-835d-f7cc180fa1a5';
    export const singleMembershipFieldName: string = 'single_membership';
    export const soldAsBundleFieldId: string = '42c17217-ecc2-4d07-8d35-c5913a94d788';
    export const soldAsBundleFieldName: string = 'sold_as_bundle';
    export const subscriptionFrequencyFieldId: string = 'c2ddffb8-5490-4ebf-ad2e-1b220a466c5a';
    export const subscriptionFrequencyFieldName: string = 'subscription_frequency';
    export const weightFieldId: string = 'f3ef8df0-e281-4d4e-bfba-7d950d517170';
    export const weightFieldName: string = 'Weight';
    export const pmiProductIdFieldId: string = 'aa683096-5550-4fee-bf74-ffa9a08957bc';
    export const pmiProductIdFieldName: string = 'pmi_product_id';
    export const membershipTextHighlightColorFieldId: string = 'b18d1660-c856-4d2f-8e3d-c676d4a7421e';
    export const membershipTextHighlightColorFieldName: string = 'membershipTextHighlightColor';
    export const recommendedAltTextFieldId: string = '3568a091-d725-4a13-9b45-fb21802c9ca3';
    export const recommendedAltTextFieldName: string = 'RecommendedAltText';
    export const showCTAFieldId: string = '402d02c0-c629-4592-b316-2d1cfd687197';
    export const showCTAFieldName: string = 'showCTA';
    export const showPricesFieldId: string = '8318d912-a453-4e22-8ca2-6ddbc96c7646';
    export const showPricesFieldName: string = 'showPrices';
    export const customCartFieldId: string = 'b8fa0117-1511-40bb-95ec-7b265962cec0';
    export const customCartFieldName: string = 'custom_cart';
    export const heroBackgroundImageFieldId: string = '6d95f762-44dd-4123-8b31-36a03113d48a';
    export const heroBackgroundImageFieldName: string = 'HeroBackgroundImage';
    export const heroBackgroundImageMobileFieldId: string = 'daf9a28d-b84c-4a67-8595-a297c050909f';
    export const heroBackgroundImageMobileFieldName: string = 'HeroBackgroundImageMobile';
    export const heroImageFieldId: string = '5a0d0f52-211e-4e3c-ab2f-71726cbd9057';
    export const heroImageFieldName: string = 'HeroImage';
    export const imageFieldId: string = 'c89f0ba8-67f4-44f6-a7f1-f68a46cdbcb6';
    export const imageFieldName: string = 'Image';
    export const smallImageFieldId: string = 'f482cd3c-6b97-48c3-ab80-a898ca3f874b';
    export const smallImageFieldName: string = 'Small Image';
    export const thumbnailFieldId: string = '2e2436e4-e5c4-4a6c-909e-1eb7931cb2d9';
    export const thumbnailFieldName: string = 'Thumbnail';
    export const useDarkThemeForHeroFieldId: string = '7bdf557d-6804-49e5-acc8-a1835731babc';
    export const useDarkThemeForHeroFieldName: string = 'UseDarkThemeForHero';
    export const isFeaturedFieldId: string = '3bfaed00-9aab-42c8-9fb7-8e8d1f0027a6';
    export const isFeaturedFieldName: string = 'Is Featured';
    export const promoBadgesFieldId: string = '23ee2ae2-1d4d-4e51-8e2a-b251e55e8a0d';
    export const promoBadgesFieldName: string = 'PromoBadges';
    export const seoFriendlyTitleFieldId: string = '260a336f-7541-4b8c-a023-3198ca421c86';
    export const seoFriendlyTitleFieldName: string = 'SeoFriendlyTitle';
    export const wordmarkFieldId: string = '1fc37bfb-e2f6-4337-b793-b12477c305bd';
    export const wordmarkFieldName: string = 'Wordmark';
    export const isDeletedFieldId: string = '2aaa5d2e-4301-40ad-b6f9-d752c5fad3e3';
    export const isDeletedFieldName: string = 'Is Deleted';
    export const pmiMembershipFieldId: string = '50845396-2667-4e2a-a548-93c81fc48043';
    export const pmiMembershipFieldName: string = 'PMI Membership';
    export const pmiSegmentFieldId: string = '3b0804fc-df69-4424-9d09-892341852d72';
    export const pmiSegmentFieldName: string = 'PMI Segment';
    export const abPricesFieldId: string = '97749ffe-4bd0-4190-abe1-c92c68de1397';
    export const abPricesFieldName: string = 'AB Prices';
    export const countryPricesFieldId: string = '202e0c1e-5500-46be-bb5d-14a400eb6130';
    export const countryPricesFieldName: string = 'Country Prices';
    export const globalPriceFieldId: string = '3a732fb5-16ff-448d-932a-143810b5f682';
    export const globalPriceFieldName: string = 'Global Price';
    export const groupPricesFieldId: string = 'da043375-9353-44e1-b45c-d4ed88459ebf';
    export const groupPricesFieldName: string = 'Group Prices';
    export const renewalPriceFieldId: string = 'b3f78b0d-bf55-4b73-b003-dbafdb00ccb3';
    export const renewalPriceFieldName: string = 'Renewal Price';
    export const zonePricesFieldId: string = 'babb6e53-3777-4916-a764-01aad64cce60';
    export const zonePricesFieldName: string = 'Zone Prices';
    export const brandNameFieldId: string = '8f6025b8-1039-46ab-a606-849bfe0c6292';
    export const brandNameFieldName: string = 'BrandName';
    export const divisionsFieldId: string = '0f949b65-ee6c-4d70-b692-54d801ba812c';
    export const divisionsFieldName: string = 'Divisions';
    export const fullDescriptionFieldId: string = '25fe6930-d1f7-4924-95f9-770aa320a1f9';
    export const fullDescriptionFieldName: string = 'Full Description';
    export const identificationFieldId: string = '89d22f6f-409f-4463-bb09-0a79f29b9707';
    export const identificationFieldName: string = 'Identification';
    export const manufacturerFieldId: string = 'ea45edf7-cd8b-433f-99a6-ebfe3c7ce3a5';
    export const manufacturerFieldName: string = 'Manufacturer';
    export const modelNameFieldId: string = '7b3fad93-dcb0-4d8d-8181-13738502bea5';
    export const modelNameFieldName: string = 'ModelName';
    export const nameFieldId: string = 'e2cea8d5-65f4-4789-a778-b1b400b56928';
    export const nameFieldName: string = 'Name';
    export const productClassesFieldId: string = '891039c9-fe24-46b6-a5de-ba958a6925cf';
    export const productClassesFieldName: string = 'ProductClasses';
    export const productTypeFieldId: string = 'd5806f22-e082-4c82-87bd-439a62ef92c6';
    export const productTypeFieldName: string = 'ProductType';
    export const shortDescriptionFieldId: string = '87cf462a-a37a-4731-bf0b-370d3d7a9873';
    export const shortDescriptionFieldName: string = 'Short Description';
    export const externalIDFieldId: string = 'f2f908f1-806e-4706-911b-6794b73576d0';
    export const externalIDFieldName: string = 'ExternalID';
    export const shortTitleFieldId: string = 'e27e197c-ad78-4352-bb04-cb7da291a88e';
    export const shortTitleFieldName: string = 'Short Title';
    export const titleFieldId: string = '56d15ac6-4a9b-4e75-830c-a30e63384011';
    export const titleFieldName: string = 'Title';
    export const iconFieldId: string = '734b447e-44ec-4739-8acc-b89fd51e1fec';
    export const iconFieldName: string = 'Icon';
    export const iconClassFieldId: string = '4796a76c-5867-4640-9dca-8bb7a17fb185';
    export const iconClassFieldName: string = 'IconClass';
    export const level2HierarchiesFieldId: string = '691c85ee-9f50-4dfd-b3af-a6c0d1d676c9';
    export const level2HierarchiesFieldName: string = 'level_2_hierarchy';
    export const level3HierarchiesFieldId: string = 'd3a4b7de-eafd-4b13-8df7-67c7220dbdda';
    export const level3HierarchiesFieldName: string = 'level_3_hierarchy';
    export const level4HierarchiesFieldId: string = 'b695c62a-0edd-4951-bd55-024699c1b8ad';
    export const level4HierarchiesFieldName: string = 'level_4_hierarchy';
  }
  export namespace CommerceCategoryBaseTemplate {
    export const templateId: string = 'c68778ab-89f5-42bb-85f6-1ff12dcc7d6c';
    export const templateName: string = 'Commerce Category Base';
    export const displayModeFieldId: string = 'a4367de0-87af-4b95-8390-ca462eae5624';
    export const displayModeFieldName: string = 'Display Mode';
    export const includeInMenuFieldId: string = '7f9e9e7f-c2c3-4dbe-b908-147b284ebb5a';
    export const includeInMenuFieldName: string = 'Include In Menu';
    export const isActiveFieldId: string = '80a6c060-0bf3-47cd-a964-693e0b4ac075';
    export const isActiveFieldName: string = 'Is Active';
    export const positionFieldId: string = 'a2eb381f-9240-476c-a9bd-81940c43f14f';
    export const positionFieldName: string = 'Position';
    export const isDeletedFieldId: string = '4ae86e22-af29-46a5-b026-e147bbd2c4ed';
    export const isDeletedFieldName: string = 'Is Deleted';
    export const externalParentIDFieldId: string = '008f1da3-097e-4fec-8322-9303308c63ea';
    export const externalParentIDFieldName: string = 'ExternalParentID';
    export const descriptionFieldId: string = '3e873f0e-4a03-4169-91af-fae982f9cbe3';
    export const descriptionFieldName: string = 'Description';
    export const nameFieldId: string = '6a65dfc6-6a35-4e8d-93ea-d1d4f40711c2';
    export const nameFieldName: string = 'Name';
    export const externalIDFieldId: string = 'f2f908f1-806e-4706-911b-6794b73576d0';
    export const externalIDFieldName: string = 'ExternalID';
    export const itemsPerPageFieldId: string = '35fc96a7-bf12-4c02-a66a-1ae68d3b4432';
    export const itemsPerPageFieldName: string = 'Items Per Page';
    export const searchFacetsFieldId: string = 'a3683321-18c4-424a-94aa-fc1ce9b03b92';
    export const searchFacetsFieldName: string = 'Search Facets';
    export const sortFieldsFieldId: string = '91c3811d-b760-475c-87e7-0aee5d4080c9';
    export const sortFieldsFieldName: string = 'Sort Fields';
  }
  export namespace CommerceProductBaseTemplate {
    export const templateId: string = 'f9aec12e-65a5-4e11-82ef-a50e9f200257';
    export const templateName: string = 'Commerce Product Base';
    export const b2bFullDescriptionFieldId: string = 'e919cee7-a090-4848-b062-626a9e3c2ea7';
    export const b2bFullDescriptionFieldName: string = 'B2b Full Description';
    export const b2bImageFieldId: string = 'a88b0bd5-dc5c-4049-b3ae-5e48fa881c93';
    export const b2bImageFieldName: string = 'B2b image';
    export const b2bShortDescriptionFieldId: string = '540b1fca-a499-4ebd-80a0-cf252801e994';
    export const b2bShortDescriptionFieldName: string = 'B2b Short Description';
    export const b2bTitleFieldId: string = '15389578-4623-4398-901c-e9a7b5533a45';
    export const b2bTitleFieldName: string = 'B2b Title';
    export const bundleProductOptionsFieldId: string = '60f860f0-69cb-4834-95a7-1391ba665a59';
    export const bundleProductOptionsFieldName: string = 'BundleProductOptions';
    export const productCustomOptionsFieldId: string = '3350a005-c47e-461c-b6ce-17e223960c71';
    export const productCustomOptionsFieldName: string = 'ProductCustomOptions';
    export const backordersFieldId: string = '6d849509-14d3-492a-b49a-550291a14caa';
    export const backordersFieldName: string = 'Backorders';
    export const courseKitTypeFieldId: string = '1e58883a-96ca-4e89-9b68-953a9678734c';
    export const courseKitTypeFieldName: string = 'course_kit_type';
    export const courseTypesFieldId: string = '5fc60777-de7e-4055-945e-6445f00f41a3';
    export const courseTypesFieldName: string = 'course_type';
    export const disabledProductURLFieldId: string = '0807e662-73c5-41c4-a975-96fd514e0ff9';
    export const disabledProductURLFieldName: string = 'disabled_Product_URL';
    export const fulfillmentProviderFieldId: string = '5ca91282-238c-4179-a84b-d7a777753a16';
    export const fulfillmentProviderFieldName: string = 'fulfillment_provider';
    export const includedInMembershipFieldId: string = 'eb7fc950-cb4c-41e4-82d1-747429338465';
    export const includedInMembershipFieldName: string = 'included_in_membership';
    export const isInStockFieldId: string = 'bb29b6b4-3456-4d3a-94db-b02656ca47a3';
    export const isInStockFieldName: string = 'Is In Stock';
    export const isReturnableFieldId: string = 'cae45dfb-0c62-4857-a91a-b72b50354c7d';
    export const isReturnableFieldName: string = 'Is Returnable';
    export const isMembershipFieldId: string = '47e537b2-9d39-47db-81f7-295157ed880e';
    export const isMembershipFieldName: string = 'is_membership';
    export const isSubscriptionProductFieldId: string = '4b7c5ef9-08c0-41c5-aa41-278641707375';
    export const isSubscriptionProductFieldName: string = 'is_subscription_product';
    export const languageFieldId: string = '595359da-ec9f-4c4c-a6a2-7546c7ef1240';
    export const languageFieldName: string = 'language';
    export const overviewDescriptionFieldId: string = 'df536977-1ee5-47e9-b7f6-11f25a9d9330';
    export const overviewDescriptionFieldName: string = 'OverviewDescription';
    export const pmiCatalogsFieldId: string = '8e050945-712f-407c-8c38-8835b5488376';
    export const pmiCatalogsFieldName: string = 'pmi_catalog';
    export const pmiProductCategoryFieldId: string = '3ce1903b-97fa-4f44-9395-4871dd6b91e0';
    export const pmiProductCategoryFieldName: string = 'pmi_product_category';
    export const pmiProductFamilyFieldId: string = '8a3f0338-265e-445d-8d06-47fb117c7bc4';
    export const pmiProductFamilyFieldName: string = 'pmi_product_family';
    export const pmiProductFormatFieldId: string = '8f191f3e-4c52-4091-a6dd-78915bc7af3a';
    export const pmiProductFormatFieldName: string = 'pmi_product_format';
    export const pmiProductTypeFieldId: string = 'fdb83207-3c1c-4566-ba47-d28568bbdeba';
    export const pmiProductTypeFieldName: string = 'pmi_product_type';
    export const productKindFieldId: string = '3d5ca766-9602-4a5b-aff6-8fcb50fca321';
    export const productKindFieldName: string = 'Product Kind';
    export const productStoreFieldId: string = '8e4499d7-9df2-4e4a-a170-3342dc989426';
    export const productStoreFieldName: string = 'Product Store';
    export const quantityFieldId: string = '549f9561-22be-4070-84f1-c82639c08c6d';
    export const quantityFieldName: string = 'Quantity';
    export const singleMembershipFieldId: string = '45cef470-41fe-48e2-835d-f7cc180fa1a5';
    export const singleMembershipFieldName: string = 'single_membership';
    export const soldAsBundleFieldId: string = '42c17217-ecc2-4d07-8d35-c5913a94d788';
    export const soldAsBundleFieldName: string = 'sold_as_bundle';
    export const subscriptionFrequencyFieldId: string = 'c2ddffb8-5490-4ebf-ad2e-1b220a466c5a';
    export const subscriptionFrequencyFieldName: string = 'subscription_frequency';
    export const weightFieldId: string = 'f3ef8df0-e281-4d4e-bfba-7d950d517170';
    export const weightFieldName: string = 'Weight';
    export const pmiProductIdFieldId: string = 'aa683096-5550-4fee-bf74-ffa9a08957bc';
    export const pmiProductIdFieldName: string = 'pmi_product_id';
    export const membershipTextHighlightColorFieldId: string = 'b18d1660-c856-4d2f-8e3d-c676d4a7421e';
    export const membershipTextHighlightColorFieldName: string = 'membershipTextHighlightColor';
    export const recommendedAltTextFieldId: string = '3568a091-d725-4a13-9b45-fb21802c9ca3';
    export const recommendedAltTextFieldName: string = 'RecommendedAltText';
    export const showCTAFieldId: string = '402d02c0-c629-4592-b316-2d1cfd687197';
    export const showCTAFieldName: string = 'showCTA';
    export const showPricesFieldId: string = '8318d912-a453-4e22-8ca2-6ddbc96c7646';
    export const showPricesFieldName: string = 'showPrices';
    export const customCartFieldId: string = 'b8fa0117-1511-40bb-95ec-7b265962cec0';
    export const customCartFieldName: string = 'custom_cart';
    export const heroBackgroundImageFieldId: string = '6d95f762-44dd-4123-8b31-36a03113d48a';
    export const heroBackgroundImageFieldName: string = 'HeroBackgroundImage';
    export const heroBackgroundImageMobileFieldId: string = 'daf9a28d-b84c-4a67-8595-a297c050909f';
    export const heroBackgroundImageMobileFieldName: string = 'HeroBackgroundImageMobile';
    export const heroImageFieldId: string = '5a0d0f52-211e-4e3c-ab2f-71726cbd9057';
    export const heroImageFieldName: string = 'HeroImage';
    export const imageFieldId: string = 'c89f0ba8-67f4-44f6-a7f1-f68a46cdbcb6';
    export const imageFieldName: string = 'Image';
    export const smallImageFieldId: string = 'f482cd3c-6b97-48c3-ab80-a898ca3f874b';
    export const smallImageFieldName: string = 'Small Image';
    export const thumbnailFieldId: string = '2e2436e4-e5c4-4a6c-909e-1eb7931cb2d9';
    export const thumbnailFieldName: string = 'Thumbnail';
    export const useDarkThemeForHeroFieldId: string = '7bdf557d-6804-49e5-acc8-a1835731babc';
    export const useDarkThemeForHeroFieldName: string = 'UseDarkThemeForHero';
    export const isFeaturedFieldId: string = '3bfaed00-9aab-42c8-9fb7-8e8d1f0027a6';
    export const isFeaturedFieldName: string = 'Is Featured';
    export const promoBadgesFieldId: string = '23ee2ae2-1d4d-4e51-8e2a-b251e55e8a0d';
    export const promoBadgesFieldName: string = 'PromoBadges';
    export const seoFriendlyTitleFieldId: string = '260a336f-7541-4b8c-a023-3198ca421c86';
    export const seoFriendlyTitleFieldName: string = 'SeoFriendlyTitle';
    export const wordmarkFieldId: string = '1fc37bfb-e2f6-4337-b793-b12477c305bd';
    export const wordmarkFieldName: string = 'Wordmark';
    export const isDeletedFieldId: string = '2aaa5d2e-4301-40ad-b6f9-d752c5fad3e3';
    export const isDeletedFieldName: string = 'Is Deleted';
    export const pmiMembershipFieldId: string = '50845396-2667-4e2a-a548-93c81fc48043';
    export const pmiMembershipFieldName: string = 'PMI Membership';
    export const pmiSegmentFieldId: string = '3b0804fc-df69-4424-9d09-892341852d72';
    export const pmiSegmentFieldName: string = 'PMI Segment';
    export const abPricesFieldId: string = '97749ffe-4bd0-4190-abe1-c92c68de1397';
    export const abPricesFieldName: string = 'AB Prices';
    export const countryPricesFieldId: string = '202e0c1e-5500-46be-bb5d-14a400eb6130';
    export const countryPricesFieldName: string = 'Country Prices';
    export const globalPriceFieldId: string = '3a732fb5-16ff-448d-932a-143810b5f682';
    export const globalPriceFieldName: string = 'Global Price';
    export const groupPricesFieldId: string = 'da043375-9353-44e1-b45c-d4ed88459ebf';
    export const groupPricesFieldName: string = 'Group Prices';
    export const renewalPriceFieldId: string = 'b3f78b0d-bf55-4b73-b003-dbafdb00ccb3';
    export const renewalPriceFieldName: string = 'Renewal Price';
    export const zonePricesFieldId: string = 'babb6e53-3777-4916-a764-01aad64cce60';
    export const zonePricesFieldName: string = 'Zone Prices';
    export const brandNameFieldId: string = '8f6025b8-1039-46ab-a606-849bfe0c6292';
    export const brandNameFieldName: string = 'BrandName';
    export const divisionsFieldId: string = '0f949b65-ee6c-4d70-b692-54d801ba812c';
    export const divisionsFieldName: string = 'Divisions';
    export const fullDescriptionFieldId: string = '25fe6930-d1f7-4924-95f9-770aa320a1f9';
    export const fullDescriptionFieldName: string = 'Full Description';
    export const identificationFieldId: string = '89d22f6f-409f-4463-bb09-0a79f29b9707';
    export const identificationFieldName: string = 'Identification';
    export const manufacturerFieldId: string = 'ea45edf7-cd8b-433f-99a6-ebfe3c7ce3a5';
    export const manufacturerFieldName: string = 'Manufacturer';
    export const modelNameFieldId: string = '7b3fad93-dcb0-4d8d-8181-13738502bea5';
    export const modelNameFieldName: string = 'ModelName';
    export const nameFieldId: string = 'e2cea8d5-65f4-4789-a778-b1b400b56928';
    export const nameFieldName: string = 'Name';
    export const productClassesFieldId: string = '891039c9-fe24-46b6-a5de-ba958a6925cf';
    export const productClassesFieldName: string = 'ProductClasses';
    export const productTypeFieldId: string = 'd5806f22-e082-4c82-87bd-439a62ef92c6';
    export const productTypeFieldName: string = 'ProductType';
    export const shortDescriptionFieldId: string = '87cf462a-a37a-4731-bf0b-370d3d7a9873';
    export const shortDescriptionFieldName: string = 'Short Description';
    export const externalIDFieldId: string = 'f2f908f1-806e-4706-911b-6794b73576d0';
    export const externalIDFieldName: string = 'ExternalID';
    export const shortTitleFieldId: string = 'e27e197c-ad78-4352-bb04-cb7da291a88e';
    export const shortTitleFieldName: string = 'Short Title';
    export const titleFieldId: string = '56d15ac6-4a9b-4e75-830c-a30e63384011';
    export const titleFieldName: string = 'Title';
    export const iconFieldId: string = '734b447e-44ec-4739-8acc-b89fd51e1fec';
    export const iconFieldName: string = 'Icon';
    export const iconClassFieldId: string = '4796a76c-5867-4640-9dca-8bb7a17fb185';
    export const iconClassFieldName: string = 'IconClass';
    export const level2HierarchiesFieldId: string = '691c85ee-9f50-4dfd-b3af-a6c0d1d676c9';
    export const level2HierarchiesFieldName: string = 'level_2_hierarchy';
    export const level3HierarchiesFieldId: string = 'd3a4b7de-eafd-4b13-8df7-67c7220dbdda';
    export const level3HierarchiesFieldName: string = 'level_3_hierarchy';
    export const level4HierarchiesFieldId: string = 'b695c62a-0edd-4951-bd55-024699c1b8ad';
    export const level4HierarchiesFieldName: string = 'level_4_hierarchy';
  }
  export namespace CommerceProductTypeBaseTemplate {
    export const templateId: string = 'd46f0f09-ee8e-44e6-a425-98d246f9a0c9';
    export const templateName: string = 'Commerce Product Type Base';
    export const externalParentIDFieldId: string = '5d89d406-b223-426e-a029-ce7138a9bab2';
    export const externalParentIDFieldName: string = 'ExternalParentID';
    export const descriptionFieldId: string = 'ec7d4ac3-83b6-4653-944a-097aafdfff07';
    export const descriptionFieldName: string = 'Description';
    export const nameFieldId: string = '93e7751e-aec7-4ea7-b5d8-b6d6653b3e8b';
    export const nameFieldName: string = 'Name';
    export const externalIDFieldId: string = 'f2f908f1-806e-4706-911b-6794b73576d0';
    export const externalIDFieldName: string = 'ExternalID';
  }
  export namespace ConnectSettingsTemplate {
    export const templateId: string = 'adf5c892-b08b-4294-9e28-da612b5fae41';
    export const templateName: string = 'Connect Settings';
  }
  export namespace ContentPageTemplate {
    export const templateId: string = '7e181119-46dd-489c-9fdf-0c9dc89141c2';
    export const templateName: string = 'Content Page';
    export const shortTitleFieldId: string = 'e27e197c-ad78-4352-bb04-cb7da291a88e';
    export const shortTitleFieldName: string = 'Short Title';
    export const titleFieldId: string = '56d15ac6-4a9b-4e75-830c-a30e63384011';
    export const titleFieldName: string = 'Title';
  }
  export namespace CountriesTemplate {
    export const templateId: string = 'd7640afe-63c0-4cdf-876f-c17c5d3fa5c4';
    export const templateName: string = 'Countries';
  }
  export namespace CountryTemplate {
    export const templateId: string = '45537cd4-5252-4436-8b1a-82c1c7c8fd90';
    export const templateName: string = 'Country';
    export const codeFieldId: string = 'b9203627-da88-48d6-b617-082458ac0e7c';
    export const codeFieldName: string = 'Code';
    export const code3FieldId: string = '61fe24c7-974b-493e-a16d-e1dddbbca3ee';
    export const code3FieldName: string = 'Code3';
    export const nameFieldId: string = 'bf30b4a3-0f21-4946-87c0-de453d9aa6fa';
    export const nameFieldName: string = 'Name';
  }
  export namespace CurrenciesTemplate {
    export const templateId: string = 'eba94d27-11ce-4bc2-a537-a1855aa0c20f';
    export const templateName: string = 'Currencies';
  }
  export namespace CurrencyTemplate {
    export const templateId: string = 'b5571a56-037b-48ff-b317-68e10a631079';
    export const templateName: string = 'Currency';
    export const allowedCardsFieldId: string = 'd9dc15f3-ae68-4ee8-82f7-d54e63cb6215';
    export const allowedCardsFieldName: string = 'Allowed Cards';
    export const countryFieldId: string = '6a85eea1-6911-4661-bb68-e297057568d4';
    export const countryFieldName: string = 'Country';
    export const currencyCodeFieldId: string = '6fe1ebdc-6340-4c66-bfda-4eb65f155180';
    export const currencyCodeFieldName: string = 'Currency Code';
    export const currencyNameFieldId: string = 'bea10233-e1b8-4651-b45f-90e9a4f4171a';
    export const currencyNameFieldName: string = 'Currency Name';
    export const currencySymbolFieldId: string = 'c0163f1f-9646-4d2f-82da-059d13b67b71';
    export const currencySymbolFieldName: string = 'Currency Symbol';
    export const currencyDecimalPlacesFieldId: string = 'c16357b6-0ea4-4067-bff6-045685442ec5';
    export const currencyDecimalPlacesFieldName: string = 'CurrencyDecimalPlaces';
  }
  export namespace CustomerGroupsTemplate {
    export const templateId: string = '4483c063-f8b4-42f9-8575-96413c8556b9';
    export const templateName: string = 'Customer Groups';
  }
  export namespace CustomerGroupTemplate {
    export const templateId: string = '12a53e3f-4e34-4a89-b308-4c0592139498';
    export const templateName: string = 'Customer Group';
    export const codeFieldId: string = 'e4b8bb8a-b700-4b11-b4b5-37e7c0dea2eb';
    export const codeFieldName: string = 'Code';
    export const externalIDFieldId: string = 'f2f908f1-806e-4706-911b-6794b73576d0';
    export const externalIDFieldName: string = 'ExternalID';
  }
  export namespace DigitalProductBaseTemplate {
    export const templateId: string = '559d3674-f2c5-40ba-9db5-da037785e2b6';
    export const templateName: string = 'Digital Product Base';
    export const ceuCreditValueFieldId: string = '79e27f62-fe68-4e2c-a6aa-e2c1fca27235';
    export const ceuCreditValueFieldName: string = 'ceu_credit_value';
    export const languageFormatsFieldId: string = '75c31d86-c4e7-4f60-b89a-446095adfbe9';
    export const languageFormatsFieldName: string = 'language_format';
    export const leadershipPdusFieldId: string = '76fc17bc-5a80-4ab5-bab3-d351b135e8e2';
    export const leadershipPdusFieldName: string = 'leadership_pdus';
    export const lengthOfCourseFieldId: string = 'bff354fa-9d7b-40a9-8f1d-6e82f2e691de';
    export const lengthOfCourseFieldName: string = 'length_of_course';
    export const courseLevelFieldId: string = '812400f2-0248-4b8e-8b38-bc822624009a';
    export const courseLevelFieldName: string = 'level_of_course';
    export const pdusFieldId: string = 'b888b428-2677-4008-9d1d-9667886a97a6';
    export const pdusFieldName: string = 'pdus';
    export const strategicBusinessPdusFieldId: string = 'eb492c8b-c4d1-44b3-a223-789c1432ca41';
    export const strategicBusinessPdusFieldName: string = 'strategic_business_pdus';
    export const technicalPdusFieldId: string = '77d189fe-5ede-4a12-8da1-a9fc11c5ec41';
    export const technicalPdusFieldName: string = 'technical_pdus';
    export const associatedCertificationsFieldId: string = 'f3c0f88e-5f66-499b-b927-a75e44f4f991';
    export const associatedCertificationsFieldName: string = 'AssociatedCertifications';
    export const bylineFieldId: string = '94b3b86b-1e81-4469-aa6f-19b529c84599';
    export const bylineFieldName: string = 'Byline';
    export const level2HierarchiesFieldId: string = '691c85ee-9f50-4dfd-b3af-a6c0d1d676c9';
    export const level2HierarchiesFieldName: string = 'level_2_hierarchy';
    export const level3HierarchiesFieldId: string = 'd3a4b7de-eafd-4b13-8df7-67c7220dbdda';
    export const level3HierarchiesFieldName: string = 'level_3_hierarchy';
    export const level4HierarchiesFieldId: string = 'b695c62a-0edd-4951-bd55-024699c1b8ad';
    export const level4HierarchiesFieldName: string = 'level_4_hierarchy';
  }
  export namespace DigitalProductTemplate {
    export const templateId: string = 'a7ef4f24-c310-436e-968b-831eacf63ad3';
    export const templateName: string = 'Digital Product';
    export const detailsFieldId: string = 'fdcff921-b60c-44c5-9d68-86ff124083c1';
    export const detailsFieldName: string = 'details';
    export const b2bFullDescriptionFieldId: string = 'e919cee7-a090-4848-b062-626a9e3c2ea7';
    export const b2bFullDescriptionFieldName: string = 'B2b Full Description';
    export const b2bImageFieldId: string = 'a88b0bd5-dc5c-4049-b3ae-5e48fa881c93';
    export const b2bImageFieldName: string = 'B2b image';
    export const b2bShortDescriptionFieldId: string = '540b1fca-a499-4ebd-80a0-cf252801e994';
    export const b2bShortDescriptionFieldName: string = 'B2b Short Description';
    export const b2bTitleFieldId: string = '15389578-4623-4398-901c-e9a7b5533a45';
    export const b2bTitleFieldName: string = 'B2b Title';
    export const bundleProductOptionsFieldId: string = '60f860f0-69cb-4834-95a7-1391ba665a59';
    export const bundleProductOptionsFieldName: string = 'BundleProductOptions';
    export const productCustomOptionsFieldId: string = '3350a005-c47e-461c-b6ce-17e223960c71';
    export const productCustomOptionsFieldName: string = 'ProductCustomOptions';
    export const backordersFieldId: string = '6d849509-14d3-492a-b49a-550291a14caa';
    export const backordersFieldName: string = 'Backorders';
    export const courseKitTypeFieldId: string = '1e58883a-96ca-4e89-9b68-953a9678734c';
    export const courseKitTypeFieldName: string = 'course_kit_type';
    export const courseTypesFieldId: string = '5fc60777-de7e-4055-945e-6445f00f41a3';
    export const courseTypesFieldName: string = 'course_type';
    export const disabledProductURLFieldId: string = '0807e662-73c5-41c4-a975-96fd514e0ff9';
    export const disabledProductURLFieldName: string = 'disabled_Product_URL';
    export const fulfillmentProviderFieldId: string = '5ca91282-238c-4179-a84b-d7a777753a16';
    export const fulfillmentProviderFieldName: string = 'fulfillment_provider';
    export const includedInMembershipFieldId: string = 'eb7fc950-cb4c-41e4-82d1-747429338465';
    export const includedInMembershipFieldName: string = 'included_in_membership';
    export const isInStockFieldId: string = 'bb29b6b4-3456-4d3a-94db-b02656ca47a3';
    export const isInStockFieldName: string = 'Is In Stock';
    export const isReturnableFieldId: string = 'cae45dfb-0c62-4857-a91a-b72b50354c7d';
    export const isReturnableFieldName: string = 'Is Returnable';
    export const isMembershipFieldId: string = '47e537b2-9d39-47db-81f7-295157ed880e';
    export const isMembershipFieldName: string = 'is_membership';
    export const isSubscriptionProductFieldId: string = '4b7c5ef9-08c0-41c5-aa41-278641707375';
    export const isSubscriptionProductFieldName: string = 'is_subscription_product';
    export const languageFieldId: string = '595359da-ec9f-4c4c-a6a2-7546c7ef1240';
    export const languageFieldName: string = 'language';
    export const overviewDescriptionFieldId: string = 'df536977-1ee5-47e9-b7f6-11f25a9d9330';
    export const overviewDescriptionFieldName: string = 'OverviewDescription';
    export const pmiCatalogsFieldId: string = '8e050945-712f-407c-8c38-8835b5488376';
    export const pmiCatalogsFieldName: string = 'pmi_catalog';
    export const pmiProductCategoryFieldId: string = '3ce1903b-97fa-4f44-9395-4871dd6b91e0';
    export const pmiProductCategoryFieldName: string = 'pmi_product_category';
    export const pmiProductFamilyFieldId: string = '8a3f0338-265e-445d-8d06-47fb117c7bc4';
    export const pmiProductFamilyFieldName: string = 'pmi_product_family';
    export const pmiProductFormatFieldId: string = '8f191f3e-4c52-4091-a6dd-78915bc7af3a';
    export const pmiProductFormatFieldName: string = 'pmi_product_format';
    export const pmiProductTypeFieldId: string = 'fdb83207-3c1c-4566-ba47-d28568bbdeba';
    export const pmiProductTypeFieldName: string = 'pmi_product_type';
    export const productKindFieldId: string = '3d5ca766-9602-4a5b-aff6-8fcb50fca321';
    export const productKindFieldName: string = 'Product Kind';
    export const productStoreFieldId: string = '8e4499d7-9df2-4e4a-a170-3342dc989426';
    export const productStoreFieldName: string = 'Product Store';
    export const quantityFieldId: string = '549f9561-22be-4070-84f1-c82639c08c6d';
    export const quantityFieldName: string = 'Quantity';
    export const singleMembershipFieldId: string = '45cef470-41fe-48e2-835d-f7cc180fa1a5';
    export const singleMembershipFieldName: string = 'single_membership';
    export const soldAsBundleFieldId: string = '42c17217-ecc2-4d07-8d35-c5913a94d788';
    export const soldAsBundleFieldName: string = 'sold_as_bundle';
    export const subscriptionFrequencyFieldId: string = 'c2ddffb8-5490-4ebf-ad2e-1b220a466c5a';
    export const subscriptionFrequencyFieldName: string = 'subscription_frequency';
    export const weightFieldId: string = 'f3ef8df0-e281-4d4e-bfba-7d950d517170';
    export const weightFieldName: string = 'Weight';
    export const pmiProductIdFieldId: string = 'aa683096-5550-4fee-bf74-ffa9a08957bc';
    export const pmiProductIdFieldName: string = 'pmi_product_id';
    export const membershipTextHighlightColorFieldId: string = 'b18d1660-c856-4d2f-8e3d-c676d4a7421e';
    export const membershipTextHighlightColorFieldName: string = 'membershipTextHighlightColor';
    export const recommendedAltTextFieldId: string = '3568a091-d725-4a13-9b45-fb21802c9ca3';
    export const recommendedAltTextFieldName: string = 'RecommendedAltText';
    export const showCTAFieldId: string = '402d02c0-c629-4592-b316-2d1cfd687197';
    export const showCTAFieldName: string = 'showCTA';
    export const showPricesFieldId: string = '8318d912-a453-4e22-8ca2-6ddbc96c7646';
    export const showPricesFieldName: string = 'showPrices';
    export const customCartFieldId: string = 'b8fa0117-1511-40bb-95ec-7b265962cec0';
    export const customCartFieldName: string = 'custom_cart';
    export const heroBackgroundImageFieldId: string = '6d95f762-44dd-4123-8b31-36a03113d48a';
    export const heroBackgroundImageFieldName: string = 'HeroBackgroundImage';
    export const heroBackgroundImageMobileFieldId: string = 'daf9a28d-b84c-4a67-8595-a297c050909f';
    export const heroBackgroundImageMobileFieldName: string = 'HeroBackgroundImageMobile';
    export const heroImageFieldId: string = '5a0d0f52-211e-4e3c-ab2f-71726cbd9057';
    export const heroImageFieldName: string = 'HeroImage';
    export const imageFieldId: string = 'c89f0ba8-67f4-44f6-a7f1-f68a46cdbcb6';
    export const imageFieldName: string = 'Image';
    export const smallImageFieldId: string = 'f482cd3c-6b97-48c3-ab80-a898ca3f874b';
    export const smallImageFieldName: string = 'Small Image';
    export const thumbnailFieldId: string = '2e2436e4-e5c4-4a6c-909e-1eb7931cb2d9';
    export const thumbnailFieldName: string = 'Thumbnail';
    export const useDarkThemeForHeroFieldId: string = '7bdf557d-6804-49e5-acc8-a1835731babc';
    export const useDarkThemeForHeroFieldName: string = 'UseDarkThemeForHero';
    export const isFeaturedFieldId: string = '3bfaed00-9aab-42c8-9fb7-8e8d1f0027a6';
    export const isFeaturedFieldName: string = 'Is Featured';
    export const promoBadgesFieldId: string = '23ee2ae2-1d4d-4e51-8e2a-b251e55e8a0d';
    export const promoBadgesFieldName: string = 'PromoBadges';
    export const seoFriendlyTitleFieldId: string = '260a336f-7541-4b8c-a023-3198ca421c86';
    export const seoFriendlyTitleFieldName: string = 'SeoFriendlyTitle';
    export const wordmarkFieldId: string = '1fc37bfb-e2f6-4337-b793-b12477c305bd';
    export const wordmarkFieldName: string = 'Wordmark';
    export const isDeletedFieldId: string = '2aaa5d2e-4301-40ad-b6f9-d752c5fad3e3';
    export const isDeletedFieldName: string = 'Is Deleted';
    export const pmiMembershipFieldId: string = '50845396-2667-4e2a-a548-93c81fc48043';
    export const pmiMembershipFieldName: string = 'PMI Membership';
    export const pmiSegmentFieldId: string = '3b0804fc-df69-4424-9d09-892341852d72';
    export const pmiSegmentFieldName: string = 'PMI Segment';
    export const abPricesFieldId: string = '97749ffe-4bd0-4190-abe1-c92c68de1397';
    export const abPricesFieldName: string = 'AB Prices';
    export const countryPricesFieldId: string = '202e0c1e-5500-46be-bb5d-14a400eb6130';
    export const countryPricesFieldName: string = 'Country Prices';
    export const globalPriceFieldId: string = '3a732fb5-16ff-448d-932a-143810b5f682';
    export const globalPriceFieldName: string = 'Global Price';
    export const groupPricesFieldId: string = 'da043375-9353-44e1-b45c-d4ed88459ebf';
    export const groupPricesFieldName: string = 'Group Prices';
    export const renewalPriceFieldId: string = 'b3f78b0d-bf55-4b73-b003-dbafdb00ccb3';
    export const renewalPriceFieldName: string = 'Renewal Price';
    export const zonePricesFieldId: string = 'babb6e53-3777-4916-a764-01aad64cce60';
    export const zonePricesFieldName: string = 'Zone Prices';
    export const brandNameFieldId: string = '8f6025b8-1039-46ab-a606-849bfe0c6292';
    export const brandNameFieldName: string = 'BrandName';
    export const divisionsFieldId: string = '0f949b65-ee6c-4d70-b692-54d801ba812c';
    export const divisionsFieldName: string = 'Divisions';
    export const fullDescriptionFieldId: string = '25fe6930-d1f7-4924-95f9-770aa320a1f9';
    export const fullDescriptionFieldName: string = 'Full Description';
    export const identificationFieldId: string = '89d22f6f-409f-4463-bb09-0a79f29b9707';
    export const identificationFieldName: string = 'Identification';
    export const manufacturerFieldId: string = 'ea45edf7-cd8b-433f-99a6-ebfe3c7ce3a5';
    export const manufacturerFieldName: string = 'Manufacturer';
    export const modelNameFieldId: string = '7b3fad93-dcb0-4d8d-8181-13738502bea5';
    export const modelNameFieldName: string = 'ModelName';
    export const nameFieldId: string = 'e2cea8d5-65f4-4789-a778-b1b400b56928';
    export const nameFieldName: string = 'Name';
    export const productClassesFieldId: string = '891039c9-fe24-46b6-a5de-ba958a6925cf';
    export const productClassesFieldName: string = 'ProductClasses';
    export const productTypeFieldId: string = 'd5806f22-e082-4c82-87bd-439a62ef92c6';
    export const productTypeFieldName: string = 'ProductType';
    export const shortDescriptionFieldId: string = '87cf462a-a37a-4731-bf0b-370d3d7a9873';
    export const shortDescriptionFieldName: string = 'Short Description';
    export const externalIDFieldId: string = 'f2f908f1-806e-4706-911b-6794b73576d0';
    export const externalIDFieldName: string = 'ExternalID';
    export const shortTitleFieldId: string = 'e27e197c-ad78-4352-bb04-cb7da291a88e';
    export const shortTitleFieldName: string = 'Short Title';
    export const titleFieldId: string = '56d15ac6-4a9b-4e75-830c-a30e63384011';
    export const titleFieldName: string = 'Title';
    export const iconFieldId: string = '734b447e-44ec-4739-8acc-b89fd51e1fec';
    export const iconFieldName: string = 'Icon';
    export const iconClassFieldId: string = '4796a76c-5867-4640-9dca-8bb7a17fb185';
    export const iconClassFieldName: string = 'IconClass';
    export const level2HierarchiesFieldId: string = '691c85ee-9f50-4dfd-b3af-a6c0d1d676c9';
    export const level2HierarchiesFieldName: string = 'level_2_hierarchy';
    export const level3HierarchiesFieldId: string = 'd3a4b7de-eafd-4b13-8df7-67c7220dbdda';
    export const level3HierarchiesFieldName: string = 'level_3_hierarchy';
    export const level4HierarchiesFieldId: string = 'b695c62a-0edd-4951-bd55-024699c1b8ad';
    export const level4HierarchiesFieldName: string = 'level_4_hierarchy';
    export const ceuCreditValueFieldId: string = '79e27f62-fe68-4e2c-a6aa-e2c1fca27235';
    export const ceuCreditValueFieldName: string = 'ceu_credit_value';
    export const languageFormatsFieldId: string = '75c31d86-c4e7-4f60-b89a-446095adfbe9';
    export const languageFormatsFieldName: string = 'language_format';
    export const leadershipPdusFieldId: string = '76fc17bc-5a80-4ab5-bab3-d351b135e8e2';
    export const leadershipPdusFieldName: string = 'leadership_pdus';
    export const lengthOfCourseFieldId: string = 'bff354fa-9d7b-40a9-8f1d-6e82f2e691de';
    export const lengthOfCourseFieldName: string = 'length_of_course';
    export const courseLevelFieldId: string = '812400f2-0248-4b8e-8b38-bc822624009a';
    export const courseLevelFieldName: string = 'level_of_course';
    export const pdusFieldId: string = 'b888b428-2677-4008-9d1d-9667886a97a6';
    export const pdusFieldName: string = 'pdus';
    export const strategicBusinessPdusFieldId: string = 'eb492c8b-c4d1-44b3-a223-789c1432ca41';
    export const strategicBusinessPdusFieldName: string = 'strategic_business_pdus';
    export const technicalPdusFieldId: string = '77d189fe-5ede-4a12-8da1-a9fc11c5ec41';
    export const technicalPdusFieldName: string = 'technical_pdus';
    export const associatedCertificationsFieldId: string = 'f3c0f88e-5f66-499b-b927-a75e44f4f991';
    export const associatedCertificationsFieldName: string = 'AssociatedCertifications';
    export const bylineFieldId: string = '94b3b86b-1e81-4469-aa6f-19b529c84599';
    export const bylineFieldName: string = 'Byline';
  }
  export namespace DivisionsTemplate {
    export const templateId: string = 'bf890455-a0ae-4502-8619-c952594605cb';
    export const templateName: string = 'Divisions';
  }
  export namespace DivisionTemplate {
    export const templateId: string = 'c715d20b-2e73-4046-9b87-749a11c27c5c';
    export const templateName: string = 'Division';
    export const descriptionFieldId: string = 'dd8ff6da-9e67-4b19-8409-58f4b271c14a';
    export const descriptionFieldName: string = 'Description';
    export const nameFieldId: string = 'b6c749f8-d9d7-477a-8c02-58914020fa6f';
    export const nameFieldName: string = 'Name';
    export const externalIDFieldId: string = 'f2f908f1-806e-4706-911b-6794b73576d0';
    export const externalIDFieldName: string = 'ExternalID';
  }
  export namespace DonationTemplate {
    export const templateId: string = 'd97497b8-fffe-4226-9613-35bca3737373';
    export const templateName: string = 'Donation';
    export const donationFixedAmountFieldId: string = '95ceafa0-7690-4aea-a9c6-e8eddfb3e047';
    export const donationFixedAmountFieldName: string = 'donation_fixed_amount';
    export const donationMaxAmountFieldId: string = '9708ab3d-47e5-4a2e-894d-af91d7f90060';
    export const donationMaxAmountFieldName: string = 'donation_max_amount';
    export const experiusDonationMinAmountFieldId: string = 'a9083ea4-d44d-4fd1-9de1-75fe0c136705';
    export const experiusDonationMinAmountFieldName: string = 'experius_donation_min_amount';
    export const b2bFullDescriptionFieldId: string = 'e919cee7-a090-4848-b062-626a9e3c2ea7';
    export const b2bFullDescriptionFieldName: string = 'B2b Full Description';
    export const b2bImageFieldId: string = 'a88b0bd5-dc5c-4049-b3ae-5e48fa881c93';
    export const b2bImageFieldName: string = 'B2b image';
    export const b2bShortDescriptionFieldId: string = '540b1fca-a499-4ebd-80a0-cf252801e994';
    export const b2bShortDescriptionFieldName: string = 'B2b Short Description';
    export const b2bTitleFieldId: string = '15389578-4623-4398-901c-e9a7b5533a45';
    export const b2bTitleFieldName: string = 'B2b Title';
    export const bundleProductOptionsFieldId: string = '60f860f0-69cb-4834-95a7-1391ba665a59';
    export const bundleProductOptionsFieldName: string = 'BundleProductOptions';
    export const productCustomOptionsFieldId: string = '3350a005-c47e-461c-b6ce-17e223960c71';
    export const productCustomOptionsFieldName: string = 'ProductCustomOptions';
    export const backordersFieldId: string = '6d849509-14d3-492a-b49a-550291a14caa';
    export const backordersFieldName: string = 'Backorders';
    export const courseKitTypeFieldId: string = '1e58883a-96ca-4e89-9b68-953a9678734c';
    export const courseKitTypeFieldName: string = 'course_kit_type';
    export const courseTypesFieldId: string = '5fc60777-de7e-4055-945e-6445f00f41a3';
    export const courseTypesFieldName: string = 'course_type';
    export const disabledProductURLFieldId: string = '0807e662-73c5-41c4-a975-96fd514e0ff9';
    export const disabledProductURLFieldName: string = 'disabled_Product_URL';
    export const fulfillmentProviderFieldId: string = '5ca91282-238c-4179-a84b-d7a777753a16';
    export const fulfillmentProviderFieldName: string = 'fulfillment_provider';
    export const includedInMembershipFieldId: string = 'eb7fc950-cb4c-41e4-82d1-747429338465';
    export const includedInMembershipFieldName: string = 'included_in_membership';
    export const isInStockFieldId: string = 'bb29b6b4-3456-4d3a-94db-b02656ca47a3';
    export const isInStockFieldName: string = 'Is In Stock';
    export const isReturnableFieldId: string = 'cae45dfb-0c62-4857-a91a-b72b50354c7d';
    export const isReturnableFieldName: string = 'Is Returnable';
    export const isMembershipFieldId: string = '47e537b2-9d39-47db-81f7-295157ed880e';
    export const isMembershipFieldName: string = 'is_membership';
    export const isSubscriptionProductFieldId: string = '4b7c5ef9-08c0-41c5-aa41-278641707375';
    export const isSubscriptionProductFieldName: string = 'is_subscription_product';
    export const languageFieldId: string = '595359da-ec9f-4c4c-a6a2-7546c7ef1240';
    export const languageFieldName: string = 'language';
    export const overviewDescriptionFieldId: string = 'df536977-1ee5-47e9-b7f6-11f25a9d9330';
    export const overviewDescriptionFieldName: string = 'OverviewDescription';
    export const pmiCatalogsFieldId: string = '8e050945-712f-407c-8c38-8835b5488376';
    export const pmiCatalogsFieldName: string = 'pmi_catalog';
    export const pmiProductCategoryFieldId: string = '3ce1903b-97fa-4f44-9395-4871dd6b91e0';
    export const pmiProductCategoryFieldName: string = 'pmi_product_category';
    export const pmiProductFamilyFieldId: string = '8a3f0338-265e-445d-8d06-47fb117c7bc4';
    export const pmiProductFamilyFieldName: string = 'pmi_product_family';
    export const pmiProductFormatFieldId: string = '8f191f3e-4c52-4091-a6dd-78915bc7af3a';
    export const pmiProductFormatFieldName: string = 'pmi_product_format';
    export const pmiProductTypeFieldId: string = 'fdb83207-3c1c-4566-ba47-d28568bbdeba';
    export const pmiProductTypeFieldName: string = 'pmi_product_type';
    export const productKindFieldId: string = '3d5ca766-9602-4a5b-aff6-8fcb50fca321';
    export const productKindFieldName: string = 'Product Kind';
    export const productStoreFieldId: string = '8e4499d7-9df2-4e4a-a170-3342dc989426';
    export const productStoreFieldName: string = 'Product Store';
    export const quantityFieldId: string = '549f9561-22be-4070-84f1-c82639c08c6d';
    export const quantityFieldName: string = 'Quantity';
    export const singleMembershipFieldId: string = '45cef470-41fe-48e2-835d-f7cc180fa1a5';
    export const singleMembershipFieldName: string = 'single_membership';
    export const soldAsBundleFieldId: string = '42c17217-ecc2-4d07-8d35-c5913a94d788';
    export const soldAsBundleFieldName: string = 'sold_as_bundle';
    export const subscriptionFrequencyFieldId: string = 'c2ddffb8-5490-4ebf-ad2e-1b220a466c5a';
    export const subscriptionFrequencyFieldName: string = 'subscription_frequency';
    export const weightFieldId: string = 'f3ef8df0-e281-4d4e-bfba-7d950d517170';
    export const weightFieldName: string = 'Weight';
    export const pmiProductIdFieldId: string = 'aa683096-5550-4fee-bf74-ffa9a08957bc';
    export const pmiProductIdFieldName: string = 'pmi_product_id';
    export const membershipTextHighlightColorFieldId: string = 'b18d1660-c856-4d2f-8e3d-c676d4a7421e';
    export const membershipTextHighlightColorFieldName: string = 'membershipTextHighlightColor';
    export const recommendedAltTextFieldId: string = '3568a091-d725-4a13-9b45-fb21802c9ca3';
    export const recommendedAltTextFieldName: string = 'RecommendedAltText';
    export const showCTAFieldId: string = '402d02c0-c629-4592-b316-2d1cfd687197';
    export const showCTAFieldName: string = 'showCTA';
    export const showPricesFieldId: string = '8318d912-a453-4e22-8ca2-6ddbc96c7646';
    export const showPricesFieldName: string = 'showPrices';
    export const customCartFieldId: string = 'b8fa0117-1511-40bb-95ec-7b265962cec0';
    export const customCartFieldName: string = 'custom_cart';
    export const heroBackgroundImageFieldId: string = '6d95f762-44dd-4123-8b31-36a03113d48a';
    export const heroBackgroundImageFieldName: string = 'HeroBackgroundImage';
    export const heroBackgroundImageMobileFieldId: string = 'daf9a28d-b84c-4a67-8595-a297c050909f';
    export const heroBackgroundImageMobileFieldName: string = 'HeroBackgroundImageMobile';
    export const heroImageFieldId: string = '5a0d0f52-211e-4e3c-ab2f-71726cbd9057';
    export const heroImageFieldName: string = 'HeroImage';
    export const imageFieldId: string = 'c89f0ba8-67f4-44f6-a7f1-f68a46cdbcb6';
    export const imageFieldName: string = 'Image';
    export const smallImageFieldId: string = 'f482cd3c-6b97-48c3-ab80-a898ca3f874b';
    export const smallImageFieldName: string = 'Small Image';
    export const thumbnailFieldId: string = '2e2436e4-e5c4-4a6c-909e-1eb7931cb2d9';
    export const thumbnailFieldName: string = 'Thumbnail';
    export const useDarkThemeForHeroFieldId: string = '7bdf557d-6804-49e5-acc8-a1835731babc';
    export const useDarkThemeForHeroFieldName: string = 'UseDarkThemeForHero';
    export const isFeaturedFieldId: string = '3bfaed00-9aab-42c8-9fb7-8e8d1f0027a6';
    export const isFeaturedFieldName: string = 'Is Featured';
    export const promoBadgesFieldId: string = '23ee2ae2-1d4d-4e51-8e2a-b251e55e8a0d';
    export const promoBadgesFieldName: string = 'PromoBadges';
    export const seoFriendlyTitleFieldId: string = '260a336f-7541-4b8c-a023-3198ca421c86';
    export const seoFriendlyTitleFieldName: string = 'SeoFriendlyTitle';
    export const wordmarkFieldId: string = '1fc37bfb-e2f6-4337-b793-b12477c305bd';
    export const wordmarkFieldName: string = 'Wordmark';
    export const isDeletedFieldId: string = '2aaa5d2e-4301-40ad-b6f9-d752c5fad3e3';
    export const isDeletedFieldName: string = 'Is Deleted';
    export const pmiMembershipFieldId: string = '50845396-2667-4e2a-a548-93c81fc48043';
    export const pmiMembershipFieldName: string = 'PMI Membership';
    export const pmiSegmentFieldId: string = '3b0804fc-df69-4424-9d09-892341852d72';
    export const pmiSegmentFieldName: string = 'PMI Segment';
    export const abPricesFieldId: string = '97749ffe-4bd0-4190-abe1-c92c68de1397';
    export const abPricesFieldName: string = 'AB Prices';
    export const countryPricesFieldId: string = '202e0c1e-5500-46be-bb5d-14a400eb6130';
    export const countryPricesFieldName: string = 'Country Prices';
    export const globalPriceFieldId: string = '3a732fb5-16ff-448d-932a-143810b5f682';
    export const globalPriceFieldName: string = 'Global Price';
    export const groupPricesFieldId: string = 'da043375-9353-44e1-b45c-d4ed88459ebf';
    export const groupPricesFieldName: string = 'Group Prices';
    export const renewalPriceFieldId: string = 'b3f78b0d-bf55-4b73-b003-dbafdb00ccb3';
    export const renewalPriceFieldName: string = 'Renewal Price';
    export const zonePricesFieldId: string = 'babb6e53-3777-4916-a764-01aad64cce60';
    export const zonePricesFieldName: string = 'Zone Prices';
    export const brandNameFieldId: string = '8f6025b8-1039-46ab-a606-849bfe0c6292';
    export const brandNameFieldName: string = 'BrandName';
    export const divisionsFieldId: string = '0f949b65-ee6c-4d70-b692-54d801ba812c';
    export const divisionsFieldName: string = 'Divisions';
    export const fullDescriptionFieldId: string = '25fe6930-d1f7-4924-95f9-770aa320a1f9';
    export const fullDescriptionFieldName: string = 'Full Description';
    export const identificationFieldId: string = '89d22f6f-409f-4463-bb09-0a79f29b9707';
    export const identificationFieldName: string = 'Identification';
    export const manufacturerFieldId: string = 'ea45edf7-cd8b-433f-99a6-ebfe3c7ce3a5';
    export const manufacturerFieldName: string = 'Manufacturer';
    export const modelNameFieldId: string = '7b3fad93-dcb0-4d8d-8181-13738502bea5';
    export const modelNameFieldName: string = 'ModelName';
    export const nameFieldId: string = 'e2cea8d5-65f4-4789-a778-b1b400b56928';
    export const nameFieldName: string = 'Name';
    export const productClassesFieldId: string = '891039c9-fe24-46b6-a5de-ba958a6925cf';
    export const productClassesFieldName: string = 'ProductClasses';
    export const productTypeFieldId: string = 'd5806f22-e082-4c82-87bd-439a62ef92c6';
    export const productTypeFieldName: string = 'ProductType';
    export const shortDescriptionFieldId: string = '87cf462a-a37a-4731-bf0b-370d3d7a9873';
    export const shortDescriptionFieldName: string = 'Short Description';
    export const externalIDFieldId: string = 'f2f908f1-806e-4706-911b-6794b73576d0';
    export const externalIDFieldName: string = 'ExternalID';
    export const shortTitleFieldId: string = 'e27e197c-ad78-4352-bb04-cb7da291a88e';
    export const shortTitleFieldName: string = 'Short Title';
    export const titleFieldId: string = '56d15ac6-4a9b-4e75-830c-a30e63384011';
    export const titleFieldName: string = 'Title';
    export const iconFieldId: string = '734b447e-44ec-4739-8acc-b89fd51e1fec';
    export const iconFieldName: string = 'Icon';
    export const iconClassFieldId: string = '4796a76c-5867-4640-9dca-8bb7a17fb185';
    export const iconClassFieldName: string = 'IconClass';
    export const level2HierarchiesFieldId: string = '691c85ee-9f50-4dfd-b3af-a6c0d1d676c9';
    export const level2HierarchiesFieldName: string = 'level_2_hierarchy';
    export const level3HierarchiesFieldId: string = 'd3a4b7de-eafd-4b13-8df7-67c7220dbdda';
    export const level3HierarchiesFieldName: string = 'level_3_hierarchy';
    export const level4HierarchiesFieldId: string = 'b695c62a-0edd-4951-bd55-024699c1b8ad';
    export const level4HierarchiesFieldName: string = 'level_4_hierarchy';
  }
  export namespace EBookTemplate {
    export const templateId: string = 'b4ba8998-578d-4854-9044-8f4105fa3e79';
    export const templateName: string = 'eBook';
    export const authorNameFieldId: string = 'cf4dc6b5-5c66-4207-8768-55cb951e75ff';
    export const authorNameFieldName: string = 'author_name';
    export const bookFormatFieldId: string = 'f923e5e9-85f8-4eba-b7e4-3378aea4e1cc';
    export const bookFormatFieldName: string = 'book_format';
    export const datePublishedFieldId: string = '108d89d0-1622-4eb1-b832-726a61252f7b';
    export const datePublishedFieldName: string = 'date_published';
    export const eisbn13NumberFieldId: string = '2b264b1a-0bfc-466a-9d21-303a0685995a';
    export const eisbn13NumberFieldName: string = 'eisbn13_number';
    export const languageFormatFieldId: string = 'b06f57ae-6094-4b6d-9869-7f0cd6eef0a5';
    export const languageFormatFieldName: string = 'language_format';
    export const preorderableFieldId: string = '21fc67d8-861c-4c18-953a-699cb1364729';
    export const preorderableFieldName: string = 'preorderable';
    export const publisherNameFieldId: string = 'fa54c689-092a-44ed-89f2-5c1593948268';
    export const publisherNameFieldName: string = 'publisher_name';
    export const subjectFieldId: string = '3882e089-90d3-426e-ad76-5174c4f733fa';
    export const subjectFieldName: string = 'subject';
    export const taxClassIdFieldId: string = 'fe5d416d-bc66-4814-b17c-9313a141091d';
    export const taxClassIdFieldName: string = 'tax_class_id';
    export const b2bFullDescriptionFieldId: string = 'e919cee7-a090-4848-b062-626a9e3c2ea7';
    export const b2bFullDescriptionFieldName: string = 'B2b Full Description';
    export const b2bImageFieldId: string = 'a88b0bd5-dc5c-4049-b3ae-5e48fa881c93';
    export const b2bImageFieldName: string = 'B2b image';
    export const b2bShortDescriptionFieldId: string = '540b1fca-a499-4ebd-80a0-cf252801e994';
    export const b2bShortDescriptionFieldName: string = 'B2b Short Description';
    export const b2bTitleFieldId: string = '15389578-4623-4398-901c-e9a7b5533a45';
    export const b2bTitleFieldName: string = 'B2b Title';
    export const bundleProductOptionsFieldId: string = '60f860f0-69cb-4834-95a7-1391ba665a59';
    export const bundleProductOptionsFieldName: string = 'BundleProductOptions';
    export const productCustomOptionsFieldId: string = '3350a005-c47e-461c-b6ce-17e223960c71';
    export const productCustomOptionsFieldName: string = 'ProductCustomOptions';
    export const backordersFieldId: string = '6d849509-14d3-492a-b49a-550291a14caa';
    export const backordersFieldName: string = 'Backorders';
    export const courseKitTypeFieldId: string = '1e58883a-96ca-4e89-9b68-953a9678734c';
    export const courseKitTypeFieldName: string = 'course_kit_type';
    export const courseTypesFieldId: string = '5fc60777-de7e-4055-945e-6445f00f41a3';
    export const courseTypesFieldName: string = 'course_type';
    export const disabledProductURLFieldId: string = '0807e662-73c5-41c4-a975-96fd514e0ff9';
    export const disabledProductURLFieldName: string = 'disabled_Product_URL';
    export const fulfillmentProviderFieldId: string = '5ca91282-238c-4179-a84b-d7a777753a16';
    export const fulfillmentProviderFieldName: string = 'fulfillment_provider';
    export const includedInMembershipFieldId: string = 'eb7fc950-cb4c-41e4-82d1-747429338465';
    export const includedInMembershipFieldName: string = 'included_in_membership';
    export const isInStockFieldId: string = 'bb29b6b4-3456-4d3a-94db-b02656ca47a3';
    export const isInStockFieldName: string = 'Is In Stock';
    export const isReturnableFieldId: string = 'cae45dfb-0c62-4857-a91a-b72b50354c7d';
    export const isReturnableFieldName: string = 'Is Returnable';
    export const isMembershipFieldId: string = '47e537b2-9d39-47db-81f7-295157ed880e';
    export const isMembershipFieldName: string = 'is_membership';
    export const isSubscriptionProductFieldId: string = '4b7c5ef9-08c0-41c5-aa41-278641707375';
    export const isSubscriptionProductFieldName: string = 'is_subscription_product';
    export const languageFieldId: string = '595359da-ec9f-4c4c-a6a2-7546c7ef1240';
    export const languageFieldName: string = 'language';
    export const overviewDescriptionFieldId: string = 'df536977-1ee5-47e9-b7f6-11f25a9d9330';
    export const overviewDescriptionFieldName: string = 'OverviewDescription';
    export const pmiCatalogsFieldId: string = '8e050945-712f-407c-8c38-8835b5488376';
    export const pmiCatalogsFieldName: string = 'pmi_catalog';
    export const pmiProductCategoryFieldId: string = '3ce1903b-97fa-4f44-9395-4871dd6b91e0';
    export const pmiProductCategoryFieldName: string = 'pmi_product_category';
    export const pmiProductFamilyFieldId: string = '8a3f0338-265e-445d-8d06-47fb117c7bc4';
    export const pmiProductFamilyFieldName: string = 'pmi_product_family';
    export const pmiProductFormatFieldId: string = '8f191f3e-4c52-4091-a6dd-78915bc7af3a';
    export const pmiProductFormatFieldName: string = 'pmi_product_format';
    export const pmiProductTypeFieldId: string = 'fdb83207-3c1c-4566-ba47-d28568bbdeba';
    export const pmiProductTypeFieldName: string = 'pmi_product_type';
    export const productKindFieldId: string = '3d5ca766-9602-4a5b-aff6-8fcb50fca321';
    export const productKindFieldName: string = 'Product Kind';
    export const productStoreFieldId: string = '8e4499d7-9df2-4e4a-a170-3342dc989426';
    export const productStoreFieldName: string = 'Product Store';
    export const quantityFieldId: string = '549f9561-22be-4070-84f1-c82639c08c6d';
    export const quantityFieldName: string = 'Quantity';
    export const singleMembershipFieldId: string = '45cef470-41fe-48e2-835d-f7cc180fa1a5';
    export const singleMembershipFieldName: string = 'single_membership';
    export const soldAsBundleFieldId: string = '42c17217-ecc2-4d07-8d35-c5913a94d788';
    export const soldAsBundleFieldName: string = 'sold_as_bundle';
    export const subscriptionFrequencyFieldId: string = 'c2ddffb8-5490-4ebf-ad2e-1b220a466c5a';
    export const subscriptionFrequencyFieldName: string = 'subscription_frequency';
    export const weightFieldId: string = 'f3ef8df0-e281-4d4e-bfba-7d950d517170';
    export const weightFieldName: string = 'Weight';
    export const pmiProductIdFieldId: string = 'aa683096-5550-4fee-bf74-ffa9a08957bc';
    export const pmiProductIdFieldName: string = 'pmi_product_id';
    export const membershipTextHighlightColorFieldId: string = 'b18d1660-c856-4d2f-8e3d-c676d4a7421e';
    export const membershipTextHighlightColorFieldName: string = 'membershipTextHighlightColor';
    export const recommendedAltTextFieldId: string = '3568a091-d725-4a13-9b45-fb21802c9ca3';
    export const recommendedAltTextFieldName: string = 'RecommendedAltText';
    export const showCTAFieldId: string = '402d02c0-c629-4592-b316-2d1cfd687197';
    export const showCTAFieldName: string = 'showCTA';
    export const showPricesFieldId: string = '8318d912-a453-4e22-8ca2-6ddbc96c7646';
    export const showPricesFieldName: string = 'showPrices';
    export const customCartFieldId: string = 'b8fa0117-1511-40bb-95ec-7b265962cec0';
    export const customCartFieldName: string = 'custom_cart';
    export const heroBackgroundImageFieldId: string = '6d95f762-44dd-4123-8b31-36a03113d48a';
    export const heroBackgroundImageFieldName: string = 'HeroBackgroundImage';
    export const heroBackgroundImageMobileFieldId: string = 'daf9a28d-b84c-4a67-8595-a297c050909f';
    export const heroBackgroundImageMobileFieldName: string = 'HeroBackgroundImageMobile';
    export const heroImageFieldId: string = '5a0d0f52-211e-4e3c-ab2f-71726cbd9057';
    export const heroImageFieldName: string = 'HeroImage';
    export const imageFieldId: string = 'c89f0ba8-67f4-44f6-a7f1-f68a46cdbcb6';
    export const imageFieldName: string = 'Image';
    export const smallImageFieldId: string = 'f482cd3c-6b97-48c3-ab80-a898ca3f874b';
    export const smallImageFieldName: string = 'Small Image';
    export const thumbnailFieldId: string = '2e2436e4-e5c4-4a6c-909e-1eb7931cb2d9';
    export const thumbnailFieldName: string = 'Thumbnail';
    export const useDarkThemeForHeroFieldId: string = '7bdf557d-6804-49e5-acc8-a1835731babc';
    export const useDarkThemeForHeroFieldName: string = 'UseDarkThemeForHero';
    export const isFeaturedFieldId: string = '3bfaed00-9aab-42c8-9fb7-8e8d1f0027a6';
    export const isFeaturedFieldName: string = 'Is Featured';
    export const promoBadgesFieldId: string = '23ee2ae2-1d4d-4e51-8e2a-b251e55e8a0d';
    export const promoBadgesFieldName: string = 'PromoBadges';
    export const seoFriendlyTitleFieldId: string = '260a336f-7541-4b8c-a023-3198ca421c86';
    export const seoFriendlyTitleFieldName: string = 'SeoFriendlyTitle';
    export const wordmarkFieldId: string = '1fc37bfb-e2f6-4337-b793-b12477c305bd';
    export const wordmarkFieldName: string = 'Wordmark';
    export const isDeletedFieldId: string = '2aaa5d2e-4301-40ad-b6f9-d752c5fad3e3';
    export const isDeletedFieldName: string = 'Is Deleted';
    export const pmiMembershipFieldId: string = '50845396-2667-4e2a-a548-93c81fc48043';
    export const pmiMembershipFieldName: string = 'PMI Membership';
    export const pmiSegmentFieldId: string = '3b0804fc-df69-4424-9d09-892341852d72';
    export const pmiSegmentFieldName: string = 'PMI Segment';
    export const abPricesFieldId: string = '97749ffe-4bd0-4190-abe1-c92c68de1397';
    export const abPricesFieldName: string = 'AB Prices';
    export const countryPricesFieldId: string = '202e0c1e-5500-46be-bb5d-14a400eb6130';
    export const countryPricesFieldName: string = 'Country Prices';
    export const globalPriceFieldId: string = '3a732fb5-16ff-448d-932a-143810b5f682';
    export const globalPriceFieldName: string = 'Global Price';
    export const groupPricesFieldId: string = 'da043375-9353-44e1-b45c-d4ed88459ebf';
    export const groupPricesFieldName: string = 'Group Prices';
    export const renewalPriceFieldId: string = 'b3f78b0d-bf55-4b73-b003-dbafdb00ccb3';
    export const renewalPriceFieldName: string = 'Renewal Price';
    export const zonePricesFieldId: string = 'babb6e53-3777-4916-a764-01aad64cce60';
    export const zonePricesFieldName: string = 'Zone Prices';
    export const brandNameFieldId: string = '8f6025b8-1039-46ab-a606-849bfe0c6292';
    export const brandNameFieldName: string = 'BrandName';
    export const divisionsFieldId: string = '0f949b65-ee6c-4d70-b692-54d801ba812c';
    export const divisionsFieldName: string = 'Divisions';
    export const fullDescriptionFieldId: string = '25fe6930-d1f7-4924-95f9-770aa320a1f9';
    export const fullDescriptionFieldName: string = 'Full Description';
    export const identificationFieldId: string = '89d22f6f-409f-4463-bb09-0a79f29b9707';
    export const identificationFieldName: string = 'Identification';
    export const manufacturerFieldId: string = 'ea45edf7-cd8b-433f-99a6-ebfe3c7ce3a5';
    export const manufacturerFieldName: string = 'Manufacturer';
    export const modelNameFieldId: string = '7b3fad93-dcb0-4d8d-8181-13738502bea5';
    export const modelNameFieldName: string = 'ModelName';
    export const nameFieldId: string = 'e2cea8d5-65f4-4789-a778-b1b400b56928';
    export const nameFieldName: string = 'Name';
    export const productClassesFieldId: string = '891039c9-fe24-46b6-a5de-ba958a6925cf';
    export const productClassesFieldName: string = 'ProductClasses';
    export const productTypeFieldId: string = 'd5806f22-e082-4c82-87bd-439a62ef92c6';
    export const productTypeFieldName: string = 'ProductType';
    export const shortDescriptionFieldId: string = '87cf462a-a37a-4731-bf0b-370d3d7a9873';
    export const shortDescriptionFieldName: string = 'Short Description';
    export const externalIDFieldId: string = 'f2f908f1-806e-4706-911b-6794b73576d0';
    export const externalIDFieldName: string = 'ExternalID';
    export const shortTitleFieldId: string = 'e27e197c-ad78-4352-bb04-cb7da291a88e';
    export const shortTitleFieldName: string = 'Short Title';
    export const titleFieldId: string = '56d15ac6-4a9b-4e75-830c-a30e63384011';
    export const titleFieldName: string = 'Title';
    export const iconFieldId: string = '734b447e-44ec-4739-8acc-b89fd51e1fec';
    export const iconFieldName: string = 'Icon';
    export const iconClassFieldId: string = '4796a76c-5867-4640-9dca-8bb7a17fb185';
    export const iconClassFieldName: string = 'IconClass';
    export const level2HierarchiesFieldId: string = '691c85ee-9f50-4dfd-b3af-a6c0d1d676c9';
    export const level2HierarchiesFieldName: string = 'level_2_hierarchy';
    export const level3HierarchiesFieldId: string = 'd3a4b7de-eafd-4b13-8df7-67c7220dbdda';
    export const level3HierarchiesFieldName: string = 'level_3_hierarchy';
    export const level4HierarchiesFieldId: string = 'b695c62a-0edd-4951-bd55-024699c1b8ad';
    export const level4HierarchiesFieldName: string = 'level_4_hierarchy';
  }
  export namespace ELearningTemplate {
    export const templateId: string = 'c4a2ccf8-a341-40bc-9170-ed121809fda4';
    export const templateName: string = 'eLearning';
    export const formatFieldId: string = '2cd04308-3313-487e-b75a-082856ba27ff';
    export const formatFieldName: string = 'format';
    export const lmsCodeFieldId: string = '2bc3c5d2-d436-4f8f-a549-57865dca4858';
    export const lmsCodeFieldName: string = 'lms_code';
    export const pmiElearningProviderFieldId: string = '0c4a8b9e-f2d9-4075-8fd6-6fb7d1cde018';
    export const pmiElearningProviderFieldName: string = 'pmi_elearning_provider';
    export const whoShouldAttendFieldId: string = 'fe9e9252-e629-496f-805d-5cb1a309c2bd';
    export const whoShouldAttendFieldName: string = 'who_should_attend';
    export const overviewLanguagesFieldId: string = '91df15c9-cdb4-4eda-8429-3ee46a99aeb6';
    export const overviewLanguagesFieldName: string = 'OverviewLanguages';
    export const b2bFullDescriptionFieldId: string = 'e919cee7-a090-4848-b062-626a9e3c2ea7';
    export const b2bFullDescriptionFieldName: string = 'B2b Full Description';
    export const b2bImageFieldId: string = 'a88b0bd5-dc5c-4049-b3ae-5e48fa881c93';
    export const b2bImageFieldName: string = 'B2b image';
    export const b2bShortDescriptionFieldId: string = '540b1fca-a499-4ebd-80a0-cf252801e994';
    export const b2bShortDescriptionFieldName: string = 'B2b Short Description';
    export const b2bTitleFieldId: string = '15389578-4623-4398-901c-e9a7b5533a45';
    export const b2bTitleFieldName: string = 'B2b Title';
    export const bundleProductOptionsFieldId: string = '60f860f0-69cb-4834-95a7-1391ba665a59';
    export const bundleProductOptionsFieldName: string = 'BundleProductOptions';
    export const productCustomOptionsFieldId: string = '3350a005-c47e-461c-b6ce-17e223960c71';
    export const productCustomOptionsFieldName: string = 'ProductCustomOptions';
    export const backordersFieldId: string = '6d849509-14d3-492a-b49a-550291a14caa';
    export const backordersFieldName: string = 'Backorders';
    export const courseKitTypeFieldId: string = '1e58883a-96ca-4e89-9b68-953a9678734c';
    export const courseKitTypeFieldName: string = 'course_kit_type';
    export const courseTypesFieldId: string = '5fc60777-de7e-4055-945e-6445f00f41a3';
    export const courseTypesFieldName: string = 'course_type';
    export const disabledProductURLFieldId: string = '0807e662-73c5-41c4-a975-96fd514e0ff9';
    export const disabledProductURLFieldName: string = 'disabled_Product_URL';
    export const fulfillmentProviderFieldId: string = '5ca91282-238c-4179-a84b-d7a777753a16';
    export const fulfillmentProviderFieldName: string = 'fulfillment_provider';
    export const includedInMembershipFieldId: string = 'eb7fc950-cb4c-41e4-82d1-747429338465';
    export const includedInMembershipFieldName: string = 'included_in_membership';
    export const isInStockFieldId: string = 'bb29b6b4-3456-4d3a-94db-b02656ca47a3';
    export const isInStockFieldName: string = 'Is In Stock';
    export const isReturnableFieldId: string = 'cae45dfb-0c62-4857-a91a-b72b50354c7d';
    export const isReturnableFieldName: string = 'Is Returnable';
    export const isMembershipFieldId: string = '47e537b2-9d39-47db-81f7-295157ed880e';
    export const isMembershipFieldName: string = 'is_membership';
    export const isSubscriptionProductFieldId: string = '4b7c5ef9-08c0-41c5-aa41-278641707375';
    export const isSubscriptionProductFieldName: string = 'is_subscription_product';
    export const languageFieldId: string = '595359da-ec9f-4c4c-a6a2-7546c7ef1240';
    export const languageFieldName: string = 'language';
    export const overviewDescriptionFieldId: string = 'df536977-1ee5-47e9-b7f6-11f25a9d9330';
    export const overviewDescriptionFieldName: string = 'OverviewDescription';
    export const pmiCatalogsFieldId: string = '8e050945-712f-407c-8c38-8835b5488376';
    export const pmiCatalogsFieldName: string = 'pmi_catalog';
    export const pmiProductCategoryFieldId: string = '3ce1903b-97fa-4f44-9395-4871dd6b91e0';
    export const pmiProductCategoryFieldName: string = 'pmi_product_category';
    export const pmiProductFamilyFieldId: string = '8a3f0338-265e-445d-8d06-47fb117c7bc4';
    export const pmiProductFamilyFieldName: string = 'pmi_product_family';
    export const pmiProductFormatFieldId: string = '8f191f3e-4c52-4091-a6dd-78915bc7af3a';
    export const pmiProductFormatFieldName: string = 'pmi_product_format';
    export const pmiProductTypeFieldId: string = 'fdb83207-3c1c-4566-ba47-d28568bbdeba';
    export const pmiProductTypeFieldName: string = 'pmi_product_type';
    export const productKindFieldId: string = '3d5ca766-9602-4a5b-aff6-8fcb50fca321';
    export const productKindFieldName: string = 'Product Kind';
    export const productStoreFieldId: string = '8e4499d7-9df2-4e4a-a170-3342dc989426';
    export const productStoreFieldName: string = 'Product Store';
    export const quantityFieldId: string = '549f9561-22be-4070-84f1-c82639c08c6d';
    export const quantityFieldName: string = 'Quantity';
    export const singleMembershipFieldId: string = '45cef470-41fe-48e2-835d-f7cc180fa1a5';
    export const singleMembershipFieldName: string = 'single_membership';
    export const soldAsBundleFieldId: string = '42c17217-ecc2-4d07-8d35-c5913a94d788';
    export const soldAsBundleFieldName: string = 'sold_as_bundle';
    export const subscriptionFrequencyFieldId: string = 'c2ddffb8-5490-4ebf-ad2e-1b220a466c5a';
    export const subscriptionFrequencyFieldName: string = 'subscription_frequency';
    export const weightFieldId: string = 'f3ef8df0-e281-4d4e-bfba-7d950d517170';
    export const weightFieldName: string = 'Weight';
    export const pmiProductIdFieldId: string = 'aa683096-5550-4fee-bf74-ffa9a08957bc';
    export const pmiProductIdFieldName: string = 'pmi_product_id';
    export const membershipTextHighlightColorFieldId: string = 'b18d1660-c856-4d2f-8e3d-c676d4a7421e';
    export const membershipTextHighlightColorFieldName: string = 'membershipTextHighlightColor';
    export const recommendedAltTextFieldId: string = '3568a091-d725-4a13-9b45-fb21802c9ca3';
    export const recommendedAltTextFieldName: string = 'RecommendedAltText';
    export const showCTAFieldId: string = '402d02c0-c629-4592-b316-2d1cfd687197';
    export const showCTAFieldName: string = 'showCTA';
    export const showPricesFieldId: string = '8318d912-a453-4e22-8ca2-6ddbc96c7646';
    export const showPricesFieldName: string = 'showPrices';
    export const customCartFieldId: string = 'b8fa0117-1511-40bb-95ec-7b265962cec0';
    export const customCartFieldName: string = 'custom_cart';
    export const heroBackgroundImageFieldId: string = '6d95f762-44dd-4123-8b31-36a03113d48a';
    export const heroBackgroundImageFieldName: string = 'HeroBackgroundImage';
    export const heroBackgroundImageMobileFieldId: string = 'daf9a28d-b84c-4a67-8595-a297c050909f';
    export const heroBackgroundImageMobileFieldName: string = 'HeroBackgroundImageMobile';
    export const heroImageFieldId: string = '5a0d0f52-211e-4e3c-ab2f-71726cbd9057';
    export const heroImageFieldName: string = 'HeroImage';
    export const imageFieldId: string = 'c89f0ba8-67f4-44f6-a7f1-f68a46cdbcb6';
    export const imageFieldName: string = 'Image';
    export const smallImageFieldId: string = 'f482cd3c-6b97-48c3-ab80-a898ca3f874b';
    export const smallImageFieldName: string = 'Small Image';
    export const thumbnailFieldId: string = '2e2436e4-e5c4-4a6c-909e-1eb7931cb2d9';
    export const thumbnailFieldName: string = 'Thumbnail';
    export const useDarkThemeForHeroFieldId: string = '7bdf557d-6804-49e5-acc8-a1835731babc';
    export const useDarkThemeForHeroFieldName: string = 'UseDarkThemeForHero';
    export const isFeaturedFieldId: string = '3bfaed00-9aab-42c8-9fb7-8e8d1f0027a6';
    export const isFeaturedFieldName: string = 'Is Featured';
    export const promoBadgesFieldId: string = '23ee2ae2-1d4d-4e51-8e2a-b251e55e8a0d';
    export const promoBadgesFieldName: string = 'PromoBadges';
    export const seoFriendlyTitleFieldId: string = '260a336f-7541-4b8c-a023-3198ca421c86';
    export const seoFriendlyTitleFieldName: string = 'SeoFriendlyTitle';
    export const wordmarkFieldId: string = '1fc37bfb-e2f6-4337-b793-b12477c305bd';
    export const wordmarkFieldName: string = 'Wordmark';
    export const isDeletedFieldId: string = '2aaa5d2e-4301-40ad-b6f9-d752c5fad3e3';
    export const isDeletedFieldName: string = 'Is Deleted';
    export const pmiMembershipFieldId: string = '50845396-2667-4e2a-a548-93c81fc48043';
    export const pmiMembershipFieldName: string = 'PMI Membership';
    export const pmiSegmentFieldId: string = '3b0804fc-df69-4424-9d09-892341852d72';
    export const pmiSegmentFieldName: string = 'PMI Segment';
    export const abPricesFieldId: string = '97749ffe-4bd0-4190-abe1-c92c68de1397';
    export const abPricesFieldName: string = 'AB Prices';
    export const countryPricesFieldId: string = '202e0c1e-5500-46be-bb5d-14a400eb6130';
    export const countryPricesFieldName: string = 'Country Prices';
    export const globalPriceFieldId: string = '3a732fb5-16ff-448d-932a-143810b5f682';
    export const globalPriceFieldName: string = 'Global Price';
    export const groupPricesFieldId: string = 'da043375-9353-44e1-b45c-d4ed88459ebf';
    export const groupPricesFieldName: string = 'Group Prices';
    export const renewalPriceFieldId: string = 'b3f78b0d-bf55-4b73-b003-dbafdb00ccb3';
    export const renewalPriceFieldName: string = 'Renewal Price';
    export const zonePricesFieldId: string = 'babb6e53-3777-4916-a764-01aad64cce60';
    export const zonePricesFieldName: string = 'Zone Prices';
    export const brandNameFieldId: string = '8f6025b8-1039-46ab-a606-849bfe0c6292';
    export const brandNameFieldName: string = 'BrandName';
    export const divisionsFieldId: string = '0f949b65-ee6c-4d70-b692-54d801ba812c';
    export const divisionsFieldName: string = 'Divisions';
    export const fullDescriptionFieldId: string = '25fe6930-d1f7-4924-95f9-770aa320a1f9';
    export const fullDescriptionFieldName: string = 'Full Description';
    export const identificationFieldId: string = '89d22f6f-409f-4463-bb09-0a79f29b9707';
    export const identificationFieldName: string = 'Identification';
    export const manufacturerFieldId: string = 'ea45edf7-cd8b-433f-99a6-ebfe3c7ce3a5';
    export const manufacturerFieldName: string = 'Manufacturer';
    export const modelNameFieldId: string = '7b3fad93-dcb0-4d8d-8181-13738502bea5';
    export const modelNameFieldName: string = 'ModelName';
    export const nameFieldId: string = 'e2cea8d5-65f4-4789-a778-b1b400b56928';
    export const nameFieldName: string = 'Name';
    export const productClassesFieldId: string = '891039c9-fe24-46b6-a5de-ba958a6925cf';
    export const productClassesFieldName: string = 'ProductClasses';
    export const productTypeFieldId: string = 'd5806f22-e082-4c82-87bd-439a62ef92c6';
    export const productTypeFieldName: string = 'ProductType';
    export const shortDescriptionFieldId: string = '87cf462a-a37a-4731-bf0b-370d3d7a9873';
    export const shortDescriptionFieldName: string = 'Short Description';
    export const externalIDFieldId: string = 'f2f908f1-806e-4706-911b-6794b73576d0';
    export const externalIDFieldName: string = 'ExternalID';
    export const shortTitleFieldId: string = 'e27e197c-ad78-4352-bb04-cb7da291a88e';
    export const shortTitleFieldName: string = 'Short Title';
    export const titleFieldId: string = '56d15ac6-4a9b-4e75-830c-a30e63384011';
    export const titleFieldName: string = 'Title';
    export const iconFieldId: string = '734b447e-44ec-4739-8acc-b89fd51e1fec';
    export const iconFieldName: string = 'Icon';
    export const iconClassFieldId: string = '4796a76c-5867-4640-9dca-8bb7a17fb185';
    export const iconClassFieldName: string = 'IconClass';
    export const level2HierarchiesFieldId: string = '691c85ee-9f50-4dfd-b3af-a6c0d1d676c9';
    export const level2HierarchiesFieldName: string = 'level_2_hierarchy';
    export const level3HierarchiesFieldId: string = 'd3a4b7de-eafd-4b13-8df7-67c7220dbdda';
    export const level3HierarchiesFieldName: string = 'level_3_hierarchy';
    export const level4HierarchiesFieldId: string = 'b695c62a-0edd-4951-bd55-024699c1b8ad';
    export const level4HierarchiesFieldName: string = 'level_4_hierarchy';
    export const ceuCreditValueFieldId: string = '79e27f62-fe68-4e2c-a6aa-e2c1fca27235';
    export const ceuCreditValueFieldName: string = 'ceu_credit_value';
    export const languageFormatsFieldId: string = '75c31d86-c4e7-4f60-b89a-446095adfbe9';
    export const languageFormatsFieldName: string = 'language_format';
    export const leadershipPdusFieldId: string = '76fc17bc-5a80-4ab5-bab3-d351b135e8e2';
    export const leadershipPdusFieldName: string = 'leadership_pdus';
    export const lengthOfCourseFieldId: string = 'bff354fa-9d7b-40a9-8f1d-6e82f2e691de';
    export const lengthOfCourseFieldName: string = 'length_of_course';
    export const courseLevelFieldId: string = '812400f2-0248-4b8e-8b38-bc822624009a';
    export const courseLevelFieldName: string = 'level_of_course';
    export const pdusFieldId: string = 'b888b428-2677-4008-9d1d-9667886a97a6';
    export const pdusFieldName: string = 'pdus';
    export const strategicBusinessPdusFieldId: string = 'eb492c8b-c4d1-44b3-a223-789c1432ca41';
    export const strategicBusinessPdusFieldName: string = 'strategic_business_pdus';
    export const technicalPdusFieldId: string = '77d189fe-5ede-4a12-8da1-a9fc11c5ec41';
    export const technicalPdusFieldName: string = 'technical_pdus';
    export const associatedCertificationsFieldId: string = 'f3c0f88e-5f66-499b-b927-a75e44f4f991';
    export const associatedCertificationsFieldName: string = 'AssociatedCertifications';
    export const bylineFieldId: string = '94b3b86b-1e81-4469-aa6f-19b529c84599';
    export const bylineFieldName: string = 'Byline';
  }
  export namespace EventTemplate {
    export const templateId: string = '0c5fe585-60fc-4da3-80d8-e56c86e89611';
    export const templateName: string = 'Event';
    export const eventDatesFieldId: string = '5803bf63-979a-43c8-8e6e-d4dafb2d9bd7';
    export const eventDatesFieldName: string = 'event_dates';
    export const eventEmailFieldId: string = '76063bd5-e959-426a-9cd1-6ea09a0f1d66';
    export const eventEmailFieldName: string = 'event_email';
    export const eventLocationFieldId: string = '651a8e42-3a06-421f-9e65-10cf228b1228';
    export const eventLocationFieldName: string = 'event_location';
    export const eventNameFieldId: string = 'c27a0fd2-544b-44c9-9450-2f499b187db1';
    export const eventNameFieldName: string = 'event_name';
    export const eventPolicyFieldId: string = '35e3b570-175d-4505-a941-7f390f1a400f';
    export const eventPolicyFieldName: string = 'event_policy';
    export const eventTicketsFieldId: string = '0cced7d8-1cef-4270-a88c-52f3f0f4e639';
    export const eventTicketsFieldName: string = 'event_tickets';
    export const targetAudienceFieldId: string = 'e4019318-a45c-4dc5-931e-c879be4cd2ea';
    export const targetAudienceFieldName: string = 'target_audience';
    export const b2bFullDescriptionFieldId: string = 'e919cee7-a090-4848-b062-626a9e3c2ea7';
    export const b2bFullDescriptionFieldName: string = 'B2b Full Description';
    export const b2bImageFieldId: string = 'a88b0bd5-dc5c-4049-b3ae-5e48fa881c93';
    export const b2bImageFieldName: string = 'B2b image';
    export const b2bShortDescriptionFieldId: string = '540b1fca-a499-4ebd-80a0-cf252801e994';
    export const b2bShortDescriptionFieldName: string = 'B2b Short Description';
    export const b2bTitleFieldId: string = '15389578-4623-4398-901c-e9a7b5533a45';
    export const b2bTitleFieldName: string = 'B2b Title';
    export const bundleProductOptionsFieldId: string = '60f860f0-69cb-4834-95a7-1391ba665a59';
    export const bundleProductOptionsFieldName: string = 'BundleProductOptions';
    export const productCustomOptionsFieldId: string = '3350a005-c47e-461c-b6ce-17e223960c71';
    export const productCustomOptionsFieldName: string = 'ProductCustomOptions';
    export const backordersFieldId: string = '6d849509-14d3-492a-b49a-550291a14caa';
    export const backordersFieldName: string = 'Backorders';
    export const courseKitTypeFieldId: string = '1e58883a-96ca-4e89-9b68-953a9678734c';
    export const courseKitTypeFieldName: string = 'course_kit_type';
    export const courseTypesFieldId: string = '5fc60777-de7e-4055-945e-6445f00f41a3';
    export const courseTypesFieldName: string = 'course_type';
    export const disabledProductURLFieldId: string = '0807e662-73c5-41c4-a975-96fd514e0ff9';
    export const disabledProductURLFieldName: string = 'disabled_Product_URL';
    export const fulfillmentProviderFieldId: string = '5ca91282-238c-4179-a84b-d7a777753a16';
    export const fulfillmentProviderFieldName: string = 'fulfillment_provider';
    export const includedInMembershipFieldId: string = 'eb7fc950-cb4c-41e4-82d1-747429338465';
    export const includedInMembershipFieldName: string = 'included_in_membership';
    export const isInStockFieldId: string = 'bb29b6b4-3456-4d3a-94db-b02656ca47a3';
    export const isInStockFieldName: string = 'Is In Stock';
    export const isReturnableFieldId: string = 'cae45dfb-0c62-4857-a91a-b72b50354c7d';
    export const isReturnableFieldName: string = 'Is Returnable';
    export const isMembershipFieldId: string = '47e537b2-9d39-47db-81f7-295157ed880e';
    export const isMembershipFieldName: string = 'is_membership';
    export const isSubscriptionProductFieldId: string = '4b7c5ef9-08c0-41c5-aa41-278641707375';
    export const isSubscriptionProductFieldName: string = 'is_subscription_product';
    export const languageFieldId: string = '595359da-ec9f-4c4c-a6a2-7546c7ef1240';
    export const languageFieldName: string = 'language';
    export const overviewDescriptionFieldId: string = 'df536977-1ee5-47e9-b7f6-11f25a9d9330';
    export const overviewDescriptionFieldName: string = 'OverviewDescription';
    export const pmiCatalogsFieldId: string = '8e050945-712f-407c-8c38-8835b5488376';
    export const pmiCatalogsFieldName: string = 'pmi_catalog';
    export const pmiProductCategoryFieldId: string = '3ce1903b-97fa-4f44-9395-4871dd6b91e0';
    export const pmiProductCategoryFieldName: string = 'pmi_product_category';
    export const pmiProductFamilyFieldId: string = '8a3f0338-265e-445d-8d06-47fb117c7bc4';
    export const pmiProductFamilyFieldName: string = 'pmi_product_family';
    export const pmiProductFormatFieldId: string = '8f191f3e-4c52-4091-a6dd-78915bc7af3a';
    export const pmiProductFormatFieldName: string = 'pmi_product_format';
    export const pmiProductTypeFieldId: string = 'fdb83207-3c1c-4566-ba47-d28568bbdeba';
    export const pmiProductTypeFieldName: string = 'pmi_product_type';
    export const productKindFieldId: string = '3d5ca766-9602-4a5b-aff6-8fcb50fca321';
    export const productKindFieldName: string = 'Product Kind';
    export const productStoreFieldId: string = '8e4499d7-9df2-4e4a-a170-3342dc989426';
    export const productStoreFieldName: string = 'Product Store';
    export const quantityFieldId: string = '549f9561-22be-4070-84f1-c82639c08c6d';
    export const quantityFieldName: string = 'Quantity';
    export const singleMembershipFieldId: string = '45cef470-41fe-48e2-835d-f7cc180fa1a5';
    export const singleMembershipFieldName: string = 'single_membership';
    export const soldAsBundleFieldId: string = '42c17217-ecc2-4d07-8d35-c5913a94d788';
    export const soldAsBundleFieldName: string = 'sold_as_bundle';
    export const subscriptionFrequencyFieldId: string = 'c2ddffb8-5490-4ebf-ad2e-1b220a466c5a';
    export const subscriptionFrequencyFieldName: string = 'subscription_frequency';
    export const weightFieldId: string = 'f3ef8df0-e281-4d4e-bfba-7d950d517170';
    export const weightFieldName: string = 'Weight';
    export const pmiProductIdFieldId: string = 'aa683096-5550-4fee-bf74-ffa9a08957bc';
    export const pmiProductIdFieldName: string = 'pmi_product_id';
    export const membershipTextHighlightColorFieldId: string = 'b18d1660-c856-4d2f-8e3d-c676d4a7421e';
    export const membershipTextHighlightColorFieldName: string = 'membershipTextHighlightColor';
    export const recommendedAltTextFieldId: string = '3568a091-d725-4a13-9b45-fb21802c9ca3';
    export const recommendedAltTextFieldName: string = 'RecommendedAltText';
    export const showCTAFieldId: string = '402d02c0-c629-4592-b316-2d1cfd687197';
    export const showCTAFieldName: string = 'showCTA';
    export const showPricesFieldId: string = '8318d912-a453-4e22-8ca2-6ddbc96c7646';
    export const showPricesFieldName: string = 'showPrices';
    export const customCartFieldId: string = 'b8fa0117-1511-40bb-95ec-7b265962cec0';
    export const customCartFieldName: string = 'custom_cart';
    export const heroBackgroundImageFieldId: string = '6d95f762-44dd-4123-8b31-36a03113d48a';
    export const heroBackgroundImageFieldName: string = 'HeroBackgroundImage';
    export const heroBackgroundImageMobileFieldId: string = 'daf9a28d-b84c-4a67-8595-a297c050909f';
    export const heroBackgroundImageMobileFieldName: string = 'HeroBackgroundImageMobile';
    export const heroImageFieldId: string = '5a0d0f52-211e-4e3c-ab2f-71726cbd9057';
    export const heroImageFieldName: string = 'HeroImage';
    export const imageFieldId: string = 'c89f0ba8-67f4-44f6-a7f1-f68a46cdbcb6';
    export const imageFieldName: string = 'Image';
    export const smallImageFieldId: string = 'f482cd3c-6b97-48c3-ab80-a898ca3f874b';
    export const smallImageFieldName: string = 'Small Image';
    export const thumbnailFieldId: string = '2e2436e4-e5c4-4a6c-909e-1eb7931cb2d9';
    export const thumbnailFieldName: string = 'Thumbnail';
    export const useDarkThemeForHeroFieldId: string = '7bdf557d-6804-49e5-acc8-a1835731babc';
    export const useDarkThemeForHeroFieldName: string = 'UseDarkThemeForHero';
    export const isFeaturedFieldId: string = '3bfaed00-9aab-42c8-9fb7-8e8d1f0027a6';
    export const isFeaturedFieldName: string = 'Is Featured';
    export const promoBadgesFieldId: string = '23ee2ae2-1d4d-4e51-8e2a-b251e55e8a0d';
    export const promoBadgesFieldName: string = 'PromoBadges';
    export const seoFriendlyTitleFieldId: string = '260a336f-7541-4b8c-a023-3198ca421c86';
    export const seoFriendlyTitleFieldName: string = 'SeoFriendlyTitle';
    export const wordmarkFieldId: string = '1fc37bfb-e2f6-4337-b793-b12477c305bd';
    export const wordmarkFieldName: string = 'Wordmark';
    export const isDeletedFieldId: string = '2aaa5d2e-4301-40ad-b6f9-d752c5fad3e3';
    export const isDeletedFieldName: string = 'Is Deleted';
    export const pmiMembershipFieldId: string = '50845396-2667-4e2a-a548-93c81fc48043';
    export const pmiMembershipFieldName: string = 'PMI Membership';
    export const pmiSegmentFieldId: string = '3b0804fc-df69-4424-9d09-892341852d72';
    export const pmiSegmentFieldName: string = 'PMI Segment';
    export const abPricesFieldId: string = '97749ffe-4bd0-4190-abe1-c92c68de1397';
    export const abPricesFieldName: string = 'AB Prices';
    export const countryPricesFieldId: string = '202e0c1e-5500-46be-bb5d-14a400eb6130';
    export const countryPricesFieldName: string = 'Country Prices';
    export const globalPriceFieldId: string = '3a732fb5-16ff-448d-932a-143810b5f682';
    export const globalPriceFieldName: string = 'Global Price';
    export const groupPricesFieldId: string = 'da043375-9353-44e1-b45c-d4ed88459ebf';
    export const groupPricesFieldName: string = 'Group Prices';
    export const renewalPriceFieldId: string = 'b3f78b0d-bf55-4b73-b003-dbafdb00ccb3';
    export const renewalPriceFieldName: string = 'Renewal Price';
    export const zonePricesFieldId: string = 'babb6e53-3777-4916-a764-01aad64cce60';
    export const zonePricesFieldName: string = 'Zone Prices';
    export const brandNameFieldId: string = '8f6025b8-1039-46ab-a606-849bfe0c6292';
    export const brandNameFieldName: string = 'BrandName';
    export const divisionsFieldId: string = '0f949b65-ee6c-4d70-b692-54d801ba812c';
    export const divisionsFieldName: string = 'Divisions';
    export const fullDescriptionFieldId: string = '25fe6930-d1f7-4924-95f9-770aa320a1f9';
    export const fullDescriptionFieldName: string = 'Full Description';
    export const identificationFieldId: string = '89d22f6f-409f-4463-bb09-0a79f29b9707';
    export const identificationFieldName: string = 'Identification';
    export const manufacturerFieldId: string = 'ea45edf7-cd8b-433f-99a6-ebfe3c7ce3a5';
    export const manufacturerFieldName: string = 'Manufacturer';
    export const modelNameFieldId: string = '7b3fad93-dcb0-4d8d-8181-13738502bea5';
    export const modelNameFieldName: string = 'ModelName';
    export const nameFieldId: string = 'e2cea8d5-65f4-4789-a778-b1b400b56928';
    export const nameFieldName: string = 'Name';
    export const productClassesFieldId: string = '891039c9-fe24-46b6-a5de-ba958a6925cf';
    export const productClassesFieldName: string = 'ProductClasses';
    export const productTypeFieldId: string = 'd5806f22-e082-4c82-87bd-439a62ef92c6';
    export const productTypeFieldName: string = 'ProductType';
    export const shortDescriptionFieldId: string = '87cf462a-a37a-4731-bf0b-370d3d7a9873';
    export const shortDescriptionFieldName: string = 'Short Description';
    export const externalIDFieldId: string = 'f2f908f1-806e-4706-911b-6794b73576d0';
    export const externalIDFieldName: string = 'ExternalID';
    export const shortTitleFieldId: string = 'e27e197c-ad78-4352-bb04-cb7da291a88e';
    export const shortTitleFieldName: string = 'Short Title';
    export const titleFieldId: string = '56d15ac6-4a9b-4e75-830c-a30e63384011';
    export const titleFieldName: string = 'Title';
    export const iconFieldId: string = '734b447e-44ec-4739-8acc-b89fd51e1fec';
    export const iconFieldName: string = 'Icon';
    export const iconClassFieldId: string = '4796a76c-5867-4640-9dca-8bb7a17fb185';
    export const iconClassFieldName: string = 'IconClass';
    export const level2HierarchiesFieldId: string = '691c85ee-9f50-4dfd-b3af-a6c0d1d676c9';
    export const level2HierarchiesFieldName: string = 'level_2_hierarchy';
    export const level3HierarchiesFieldId: string = 'd3a4b7de-eafd-4b13-8df7-67c7220dbdda';
    export const level3HierarchiesFieldName: string = 'level_3_hierarchy';
    export const level4HierarchiesFieldId: string = 'b695c62a-0edd-4951-bd55-024699c1b8ad';
    export const level4HierarchiesFieldName: string = 'level_4_hierarchy';
  }
  export namespace FacetTemplate {
    export const templateId: string = '5c125b6d-c481-4c24-b5b9-9a23fe396bf0';
    export const templateName: string = 'Facet';
    export const clientSideHandleFieldId: string = '7f42ed80-33f0-4c98-a1e7-b15be7551c36';
    export const clientSideHandleFieldName: string = 'Client Side Handle';
    export const displayNameFieldId: string = '5de17054-0141-4e58-935b-fec623a60806';
    export const displayNameFieldName: string = 'DisplayName';
    export const enabledFieldId: string = '3d0fe806-d0e3-4254-96be-a60899c7d2e6';
    export const enabledFieldName: string = 'Enabled';
    export const globalFieldId: string = '182bd0f4-c902-4a19-a1ba-d189caa18e1b';
    export const globalFieldName: string = 'Global';
    export const minimumNumberOfItemsFieldId: string = '8a3ac1b8-8cc1-4dd4-8766-9803f1601741';
    export const minimumNumberOfItemsFieldName: string = 'Minimum Number of Items';
    export const nameFieldId: string = '31ca077f-ef38-4980-8a24-05408031b2bc';
    export const nameFieldName: string = 'Name';
    export const parametersFieldId: string = '0e23bb51-c071-41b5-9d83-ac410b89b85a';
    export const parametersFieldName: string = 'Parameters';
    export const typeFieldId: string = 'fae92c1e-9e2c-4209-89eb-c256d55e3d7f';
    export const typeFieldName: string = 'Type';
  }
  export namespace GlobalConnectSettingsTemplate {
    export const templateId: string = '62d3fd7b-1c19-4428-8ad6-b107cca460ee';
    export const templateName: string = 'Global Connect Settings';
  }
  export namespace IdentificationTypesTemplate {
    export const templateId: string = '29fe9b4e-bda0-420f-97ca-ce1421cd9cc8';
    export const templateName: string = 'Identification Types';
  }
  export namespace IdentificationTypeTemplate {
    export const templateId: string = '0a5feaa1-1b35-441a-9cc0-8fc259a15213';
    export const templateName: string = 'Identification Type';
    export const descriptionFieldId: string = 'dada97b4-46a4-4f5f-a96f-64fde1e9d837';
    export const descriptionFieldName: string = 'Description';
    export const nameFieldId: string = '6cbb88aa-3a9f-4769-9996-6ed459bed324';
    export const nameFieldName: string = 'Name';
  }
  export namespace LookupValuesTemplate {
    export const templateId: string = 'ab6feece-afa4-49b8-b96d-f15e28eaefcd';
    export const templateName: string = 'Lookup Values';
    export const descriptionFieldId: string = '545f0965-e7a6-4231-9a13-4f7ff48c006f';
    export const descriptionFieldName: string = 'Description';
  }
  export namespace LookupValueTemplate {
    export const templateId: string = 'b95d069c-7867-4140-816d-6b204949b522';
    export const templateName: string = 'Lookup Value';
    export const descriptionFieldId: string = '7a3b32c4-ceaa-4759-9e6c-f2012fc40c91';
    export const descriptionFieldName: string = 'Description';
    export const shortFieldId: string = 'd8034d94-f87f-47e2-a884-f11abee21138';
    export const shortFieldName: string = 'Short';
  }
  export namespace ManufacturersTemplate {
    export const templateId: string = '6addbd3c-6a82-4361-a361-6c89b4cb05b2';
    export const templateName: string = 'Manufacturers';
  }
  export namespace ManufacturerTemplate {
    export const templateId: string = '8ecdc0a6-3a85-4f89-8f49-8a53aa75595e';
    export const templateName: string = 'Manufacturer';
    export const descriptionFieldId: string = '465f8abe-79fb-4c68-8dfa-5890bb7f2a60';
    export const descriptionFieldName: string = 'Description';
    export const nameFieldId: string = 'eca748ca-62b5-4f86-a213-796b668a0ad8';
    export const nameFieldName: string = 'Name';
    export const productURLMacroFieldId: string = '991e7d30-115b-444a-bfd5-3831727e78e1';
    export const productURLMacroFieldName: string = 'ProductURLMacro';
    export const websiteUrlFieldId: string = '3a6ff55f-4354-46f2-bf70-d6550d834922';
    export const websiteUrlFieldName: string = 'WebsiteUrl';
    export const externalIDFieldId: string = 'f2f908f1-806e-4706-911b-6794b73576d0';
    export const externalIDFieldName: string = 'ExternalID';
  }
  export namespace MembershipTemplate {
    export const templateId: string = '1193007d-5ebf-4b23-abb5-cbd5585c58e5';
    export const templateName: string = 'Membership';
    export const isStudentmembershipFieldId: string = '3aa37d98-25e5-4a3c-bc22-0e2d00279046';
    export const isStudentmembershipFieldName: string = 'is_studentmembership';
    export const optInForAutoRenewFieldId: string = '43df5139-0631-403f-aae2-569ac7354496';
    export const optInForAutoRenewFieldName: string = 'opt_in_for_auto_renew';
    export const validationFieldId: string = 'e81e4e50-0cf3-4042-95f3-644f3fe01ee1';
    export const validationFieldName: string = 'validation';
    export const b2bFullDescriptionFieldId: string = 'e919cee7-a090-4848-b062-626a9e3c2ea7';
    export const b2bFullDescriptionFieldName: string = 'B2b Full Description';
    export const b2bImageFieldId: string = 'a88b0bd5-dc5c-4049-b3ae-5e48fa881c93';
    export const b2bImageFieldName: string = 'B2b image';
    export const b2bShortDescriptionFieldId: string = '540b1fca-a499-4ebd-80a0-cf252801e994';
    export const b2bShortDescriptionFieldName: string = 'B2b Short Description';
    export const b2bTitleFieldId: string = '15389578-4623-4398-901c-e9a7b5533a45';
    export const b2bTitleFieldName: string = 'B2b Title';
    export const bundleProductOptionsFieldId: string = '60f860f0-69cb-4834-95a7-1391ba665a59';
    export const bundleProductOptionsFieldName: string = 'BundleProductOptions';
    export const productCustomOptionsFieldId: string = '3350a005-c47e-461c-b6ce-17e223960c71';
    export const productCustomOptionsFieldName: string = 'ProductCustomOptions';
    export const backordersFieldId: string = '6d849509-14d3-492a-b49a-550291a14caa';
    export const backordersFieldName: string = 'Backorders';
    export const courseKitTypeFieldId: string = '1e58883a-96ca-4e89-9b68-953a9678734c';
    export const courseKitTypeFieldName: string = 'course_kit_type';
    export const courseTypesFieldId: string = '5fc60777-de7e-4055-945e-6445f00f41a3';
    export const courseTypesFieldName: string = 'course_type';
    export const disabledProductURLFieldId: string = '0807e662-73c5-41c4-a975-96fd514e0ff9';
    export const disabledProductURLFieldName: string = 'disabled_Product_URL';
    export const fulfillmentProviderFieldId: string = '5ca91282-238c-4179-a84b-d7a777753a16';
    export const fulfillmentProviderFieldName: string = 'fulfillment_provider';
    export const includedInMembershipFieldId: string = 'eb7fc950-cb4c-41e4-82d1-747429338465';
    export const includedInMembershipFieldName: string = 'included_in_membership';
    export const isInStockFieldId: string = 'bb29b6b4-3456-4d3a-94db-b02656ca47a3';
    export const isInStockFieldName: string = 'Is In Stock';
    export const isReturnableFieldId: string = 'cae45dfb-0c62-4857-a91a-b72b50354c7d';
    export const isReturnableFieldName: string = 'Is Returnable';
    export const isMembershipFieldId: string = '47e537b2-9d39-47db-81f7-295157ed880e';
    export const isMembershipFieldName: string = 'is_membership';
    export const isSubscriptionProductFieldId: string = '4b7c5ef9-08c0-41c5-aa41-278641707375';
    export const isSubscriptionProductFieldName: string = 'is_subscription_product';
    export const languageFieldId: string = '595359da-ec9f-4c4c-a6a2-7546c7ef1240';
    export const languageFieldName: string = 'language';
    export const overviewDescriptionFieldId: string = 'df536977-1ee5-47e9-b7f6-11f25a9d9330';
    export const overviewDescriptionFieldName: string = 'OverviewDescription';
    export const pmiCatalogsFieldId: string = '8e050945-712f-407c-8c38-8835b5488376';
    export const pmiCatalogsFieldName: string = 'pmi_catalog';
    export const pmiProductCategoryFieldId: string = '3ce1903b-97fa-4f44-9395-4871dd6b91e0';
    export const pmiProductCategoryFieldName: string = 'pmi_product_category';
    export const pmiProductFamilyFieldId: string = '8a3f0338-265e-445d-8d06-47fb117c7bc4';
    export const pmiProductFamilyFieldName: string = 'pmi_product_family';
    export const pmiProductFormatFieldId: string = '8f191f3e-4c52-4091-a6dd-78915bc7af3a';
    export const pmiProductFormatFieldName: string = 'pmi_product_format';
    export const pmiProductTypeFieldId: string = 'fdb83207-3c1c-4566-ba47-d28568bbdeba';
    export const pmiProductTypeFieldName: string = 'pmi_product_type';
    export const productKindFieldId: string = '3d5ca766-9602-4a5b-aff6-8fcb50fca321';
    export const productKindFieldName: string = 'Product Kind';
    export const productStoreFieldId: string = '8e4499d7-9df2-4e4a-a170-3342dc989426';
    export const productStoreFieldName: string = 'Product Store';
    export const quantityFieldId: string = '549f9561-22be-4070-84f1-c82639c08c6d';
    export const quantityFieldName: string = 'Quantity';
    export const singleMembershipFieldId: string = '45cef470-41fe-48e2-835d-f7cc180fa1a5';
    export const singleMembershipFieldName: string = 'single_membership';
    export const soldAsBundleFieldId: string = '42c17217-ecc2-4d07-8d35-c5913a94d788';
    export const soldAsBundleFieldName: string = 'sold_as_bundle';
    export const subscriptionFrequencyFieldId: string = 'c2ddffb8-5490-4ebf-ad2e-1b220a466c5a';
    export const subscriptionFrequencyFieldName: string = 'subscription_frequency';
    export const weightFieldId: string = 'f3ef8df0-e281-4d4e-bfba-7d950d517170';
    export const weightFieldName: string = 'Weight';
    export const pmiProductIdFieldId: string = 'aa683096-5550-4fee-bf74-ffa9a08957bc';
    export const pmiProductIdFieldName: string = 'pmi_product_id';
    export const membershipTextHighlightColorFieldId: string = 'b18d1660-c856-4d2f-8e3d-c676d4a7421e';
    export const membershipTextHighlightColorFieldName: string = 'membershipTextHighlightColor';
    export const recommendedAltTextFieldId: string = '3568a091-d725-4a13-9b45-fb21802c9ca3';
    export const recommendedAltTextFieldName: string = 'RecommendedAltText';
    export const showCTAFieldId: string = '402d02c0-c629-4592-b316-2d1cfd687197';
    export const showCTAFieldName: string = 'showCTA';
    export const showPricesFieldId: string = '8318d912-a453-4e22-8ca2-6ddbc96c7646';
    export const showPricesFieldName: string = 'showPrices';
    export const customCartFieldId: string = 'b8fa0117-1511-40bb-95ec-7b265962cec0';
    export const customCartFieldName: string = 'custom_cart';
    export const heroBackgroundImageFieldId: string = '6d95f762-44dd-4123-8b31-36a03113d48a';
    export const heroBackgroundImageFieldName: string = 'HeroBackgroundImage';
    export const heroBackgroundImageMobileFieldId: string = 'daf9a28d-b84c-4a67-8595-a297c050909f';
    export const heroBackgroundImageMobileFieldName: string = 'HeroBackgroundImageMobile';
    export const heroImageFieldId: string = '5a0d0f52-211e-4e3c-ab2f-71726cbd9057';
    export const heroImageFieldName: string = 'HeroImage';
    export const imageFieldId: string = 'c89f0ba8-67f4-44f6-a7f1-f68a46cdbcb6';
    export const imageFieldName: string = 'Image';
    export const smallImageFieldId: string = 'f482cd3c-6b97-48c3-ab80-a898ca3f874b';
    export const smallImageFieldName: string = 'Small Image';
    export const thumbnailFieldId: string = '2e2436e4-e5c4-4a6c-909e-1eb7931cb2d9';
    export const thumbnailFieldName: string = 'Thumbnail';
    export const useDarkThemeForHeroFieldId: string = '7bdf557d-6804-49e5-acc8-a1835731babc';
    export const useDarkThemeForHeroFieldName: string = 'UseDarkThemeForHero';
    export const isFeaturedFieldId: string = '3bfaed00-9aab-42c8-9fb7-8e8d1f0027a6';
    export const isFeaturedFieldName: string = 'Is Featured';
    export const promoBadgesFieldId: string = '23ee2ae2-1d4d-4e51-8e2a-b251e55e8a0d';
    export const promoBadgesFieldName: string = 'PromoBadges';
    export const seoFriendlyTitleFieldId: string = '260a336f-7541-4b8c-a023-3198ca421c86';
    export const seoFriendlyTitleFieldName: string = 'SeoFriendlyTitle';
    export const wordmarkFieldId: string = '1fc37bfb-e2f6-4337-b793-b12477c305bd';
    export const wordmarkFieldName: string = 'Wordmark';
    export const isDeletedFieldId: string = '2aaa5d2e-4301-40ad-b6f9-d752c5fad3e3';
    export const isDeletedFieldName: string = 'Is Deleted';
    export const pmiMembershipFieldId: string = '50845396-2667-4e2a-a548-93c81fc48043';
    export const pmiMembershipFieldName: string = 'PMI Membership';
    export const pmiSegmentFieldId: string = '3b0804fc-df69-4424-9d09-892341852d72';
    export const pmiSegmentFieldName: string = 'PMI Segment';
    export const abPricesFieldId: string = '97749ffe-4bd0-4190-abe1-c92c68de1397';
    export const abPricesFieldName: string = 'AB Prices';
    export const countryPricesFieldId: string = '202e0c1e-5500-46be-bb5d-14a400eb6130';
    export const countryPricesFieldName: string = 'Country Prices';
    export const globalPriceFieldId: string = '3a732fb5-16ff-448d-932a-143810b5f682';
    export const globalPriceFieldName: string = 'Global Price';
    export const groupPricesFieldId: string = 'da043375-9353-44e1-b45c-d4ed88459ebf';
    export const groupPricesFieldName: string = 'Group Prices';
    export const renewalPriceFieldId: string = 'b3f78b0d-bf55-4b73-b003-dbafdb00ccb3';
    export const renewalPriceFieldName: string = 'Renewal Price';
    export const zonePricesFieldId: string = 'babb6e53-3777-4916-a764-01aad64cce60';
    export const zonePricesFieldName: string = 'Zone Prices';
    export const brandNameFieldId: string = '8f6025b8-1039-46ab-a606-849bfe0c6292';
    export const brandNameFieldName: string = 'BrandName';
    export const divisionsFieldId: string = '0f949b65-ee6c-4d70-b692-54d801ba812c';
    export const divisionsFieldName: string = 'Divisions';
    export const fullDescriptionFieldId: string = '25fe6930-d1f7-4924-95f9-770aa320a1f9';
    export const fullDescriptionFieldName: string = 'Full Description';
    export const identificationFieldId: string = '89d22f6f-409f-4463-bb09-0a79f29b9707';
    export const identificationFieldName: string = 'Identification';
    export const manufacturerFieldId: string = 'ea45edf7-cd8b-433f-99a6-ebfe3c7ce3a5';
    export const manufacturerFieldName: string = 'Manufacturer';
    export const modelNameFieldId: string = '7b3fad93-dcb0-4d8d-8181-13738502bea5';
    export const modelNameFieldName: string = 'ModelName';
    export const nameFieldId: string = 'e2cea8d5-65f4-4789-a778-b1b400b56928';
    export const nameFieldName: string = 'Name';
    export const productClassesFieldId: string = '891039c9-fe24-46b6-a5de-ba958a6925cf';
    export const productClassesFieldName: string = 'ProductClasses';
    export const productTypeFieldId: string = 'd5806f22-e082-4c82-87bd-439a62ef92c6';
    export const productTypeFieldName: string = 'ProductType';
    export const shortDescriptionFieldId: string = '87cf462a-a37a-4731-bf0b-370d3d7a9873';
    export const shortDescriptionFieldName: string = 'Short Description';
    export const externalIDFieldId: string = 'f2f908f1-806e-4706-911b-6794b73576d0';
    export const externalIDFieldName: string = 'ExternalID';
    export const shortTitleFieldId: string = 'e27e197c-ad78-4352-bb04-cb7da291a88e';
    export const shortTitleFieldName: string = 'Short Title';
    export const titleFieldId: string = '56d15ac6-4a9b-4e75-830c-a30e63384011';
    export const titleFieldName: string = 'Title';
    export const iconFieldId: string = '734b447e-44ec-4739-8acc-b89fd51e1fec';
    export const iconFieldName: string = 'Icon';
    export const iconClassFieldId: string = '4796a76c-5867-4640-9dca-8bb7a17fb185';
    export const iconClassFieldName: string = 'IconClass';
    export const level2HierarchiesFieldId: string = '691c85ee-9f50-4dfd-b3af-a6c0d1d676c9';
    export const level2HierarchiesFieldName: string = 'level_2_hierarchy';
    export const level3HierarchiesFieldId: string = 'd3a4b7de-eafd-4b13-8df7-67c7220dbdda';
    export const level3HierarchiesFieldName: string = 'level_3_hierarchy';
    export const level4HierarchiesFieldId: string = 'b695c62a-0edd-4951-bd55-024699c1b8ad';
    export const level4HierarchiesFieldName: string = 'level_4_hierarchy';
  }
  export namespace ProductAttributesTemplate {
    export const templateId: string = '472bc7d1-3730-4cf6-acab-65b015c9184c';
    export const templateName: string = 'Product Attributes';
  }
  export namespace ProductAttributeTemplate {
    export const templateId: string = 'a876e3f3-e9c2-43ac-9d05-3068ccbf9a3c';
    export const templateName: string = 'Product Attribute';
    export const codeFieldId: string = 'fc03e427-2d6e-4bc2-baee-a7bd00f743d3';
    export const codeFieldName: string = 'Code';
    export const externalIDFieldId: string = 'f2f908f1-806e-4706-911b-6794b73576d0';
    export const externalIDFieldName: string = 'ExternalID';
  }
  export namespace ProductAttributeValueTemplate {
    export const templateId: string = '5fa8a8cc-aaa9-4fb2-898a-7d0b79dcd4c1';
    export const templateName: string = 'Product Attribute Value';
    export const labelFieldId: string = '0ac1ed9b-d768-4c4a-90e1-2d766a20df67';
    export const labelFieldName: string = 'Label';
    export const externalIDFieldId: string = 'f2f908f1-806e-4706-911b-6794b73576d0';
    export const externalIDFieldName: string = 'ExternalID';
  }
  export namespace ProductClassificationGroupTemplate {
    export const templateId: string = 'cf8fec2c-96fc-46c8-8a49-7bf908482f4b';
    export const templateName: string = 'Product ClassificationGroup';
    export const descriptionFieldId: string = 'd918b5f7-75e9-4345-913c-b79e1171cc42';
    export const descriptionFieldName: string = 'Description';
    export const nameFieldId: string = '44c7d49f-0c37-4a56-b135-aa831e7f1eb9';
    export const nameFieldName: string = 'Name';
    export const externalIDFieldId: string = 'f2f908f1-806e-4706-911b-6794b73576d0';
    export const externalIDFieldName: string = 'ExternalID';
  }
  export namespace ProductClassificationsSpecificationTemplate {
    export const templateId: string = '9ca86513-c986-4bf3-9f9c-9791aa7ea352';
    export const templateName: string = 'Product Classifications Specification';
    export const groupFieldId: string = '6486cbd0-3e73-4293-9da9-52249bba163d';
    export const groupFieldName: string = 'Group';
    export const keyFieldId: string = '88892c6a-f865-493e-82b7-5c21e67c2fa4';
    export const keyFieldName: string = 'Key';
    export const lookupValueFieldId: string = '8377b000-0ee9-4f77-9eba-2b0822669f84';
    export const lookupValueFieldName: string = 'LookupValue';
    export const valueFieldId: string = '59b79baf-4f30-4eca-961d-4afab14d157c';
    export const valueFieldName: string = 'Value';
    export const externalIDFieldId: string = 'f2f908f1-806e-4706-911b-6794b73576d0';
    export const externalIDFieldName: string = 'ExternalID';
  }
  export namespace ProductClassificationsTemplate {
    export const templateId: string = '89c74115-3e03-4d1d-bd99-29aafcf9a649';
    export const templateName: string = 'Product Classifications';
    export const descriptionFieldId: string = '645563ed-597b-4095-acc8-eda71049c3d3';
    export const descriptionFieldName: string = 'Description';
    export const nameFieldId: string = '3308641b-eb85-4fe7-8d0d-543e5b7ea4b6';
    export const nameFieldName: string = 'Name';
    export const externalIDFieldId: string = 'f2f908f1-806e-4706-911b-6794b73576d0';
    export const externalIDFieldName: string = 'ExternalID';
  }
  export namespace ProductClassificationTemplate {
    export const templateId: string = 'd5b1659a-83e7-485f-ad9a-555eecb564bf';
    export const templateName: string = 'Product Classification';
    export const externalParentIDFieldId: string = '008f1da3-097e-4fec-8322-9303308c63ea';
    export const externalParentIDFieldName: string = 'ExternalParentID';
    export const descriptionFieldId: string = '3e873f0e-4a03-4169-91af-fae982f9cbe3';
    export const descriptionFieldName: string = 'Description';
    export const nameFieldId: string = '6a65dfc6-6a35-4e8d-93ea-d1d4f40711c2';
    export const nameFieldName: string = 'Name';
    export const externalIDFieldId: string = 'f2f908f1-806e-4706-911b-6794b73576d0';
    export const externalIDFieldName: string = 'ExternalID';
  }
  export namespace ProductGlobalSpecificationTemplate {
    export const templateId: string = '157c6369-db59-4299-8238-365d964c0965';
    export const templateName: string = 'Product Global Specification';
    export const groupFieldId: string = '6486cbd0-3e73-4293-9da9-52249bba163d';
    export const groupFieldName: string = 'Group';
    export const keyFieldId: string = '88892c6a-f865-493e-82b7-5c21e67c2fa4';
    export const keyFieldName: string = 'Key';
    export const lookupValueFieldId: string = '8377b000-0ee9-4f77-9eba-2b0822669f84';
    export const lookupValueFieldName: string = 'LookupValue';
    export const valueFieldId: string = '59b79baf-4f30-4eca-961d-4afab14d157c';
    export const valueFieldName: string = 'Value';
    export const externalIDFieldId: string = 'f2f908f1-806e-4706-911b-6794b73576d0';
    export const externalIDFieldName: string = 'ExternalID';
  }
  export namespace ProductHierarchyBaseTemplate {
    export const templateId: string = '8dd26570-494d-4867-9ff6-c15e56e8feca';
    export const templateName: string = 'Product Hierarchy Base';
    export const level2HierarchiesFieldId: string = '691c85ee-9f50-4dfd-b3af-a6c0d1d676c9';
    export const level2HierarchiesFieldName: string = 'level_2_hierarchy';
    export const level3HierarchiesFieldId: string = 'd3a4b7de-eafd-4b13-8df7-67c7220dbdda';
    export const level3HierarchiesFieldName: string = 'level_3_hierarchy';
    export const level4HierarchiesFieldId: string = 'b695c62a-0edd-4951-bd55-024699c1b8ad';
    export const level4HierarchiesFieldName: string = 'level_4_hierarchy';
  }
  export namespace ProductKindsTemplate {
    export const templateId: string = 'f106457b-dc94-4da8-a5fa-4944f1d56017';
    export const templateName: string = 'Product Kinds';
  }
  export namespace ProductKindTemplate {
    export const templateId: string = '4442ed16-6ad0-483f-8fb1-6e43de7a9ee7';
    export const templateName: string = 'Product Kind';
    export const nameFieldId: string = '3fd3f21f-6e3c-4c34-a7c6-71f20fb71550';
    export const nameFieldName: string = 'Name';
    export const externalIDFieldId: string = 'f2f908f1-806e-4706-911b-6794b73576d0';
    export const externalIDFieldName: string = 'ExternalID';
  }
  export namespace ProductMatchingTemplate {
    export const templateId: string = 'd185e14a-37bc-4cad-97af-2d34829ee819';
    export const templateName: string = 'Product Matching';
    export const productKindFieldId: string = 'd2e0628f-f47f-479c-ad48-5fc7ce2699f0';
    export const productKindFieldName: string = 'Product Kind';
    export const productTypeFieldId: string = 'b360298f-10b6-406e-a8ee-e3c6dcc2b8ab';
    export const productTypeFieldName: string = 'Product Type';
  }
  export namespace ProductPageTemplate {
    export const templateId: string = '2838844b-92ab-4bd4-a349-25a521a16f99';
    export const templateName: string = 'Product Page';
    export const productKindFieldId: string = 'd2e0628f-f47f-479c-ad48-5fc7ce2699f0';
    export const productKindFieldName: string = 'Product Kind';
    export const productTypeFieldId: string = 'b360298f-10b6-406e-a8ee-e3c6dcc2b8ab';
    export const productTypeFieldName: string = 'Product Type';
  }
  export namespace ProductRelationsTemplate {
    export const templateId: string = '6e5fbb20-2d51-4b63-8563-65e6e8c61dfd';
    export const templateName: string = 'Product Relations';
    export const descriptionFieldId: string = '62c925b2-b720-465b-8047-e776be22bd79';
    export const descriptionFieldName: string = 'Description';
  }
  export namespace ProductRelationTemplate {
    export const templateId: string = '93703013-77b6-48b5-8338-c29a5d15a1a6';
    export const templateName: string = 'Product Relation';
    export const descriptionFieldId: string = '609c7279-6e83-4ed2-9f7e-7e9bef1397f0';
    export const descriptionFieldName: string = 'Description';
    export const nameFieldId: string = '1ac1bfa4-4113-4c66-afe2-f3f3c0266c73';
    export const nameFieldName: string = 'Name';
    export const relatedProductsFieldId: string = 'b039e503-5eaf-4688-80a1-b9f32de6d800';
    export const relatedProductsFieldName: string = 'RelatedProducts';
    export const typeFieldId: string = '9d507af0-463a-4151-aaa2-8c5ad6632e36';
    export const typeFieldName: string = 'Type';
  }
  export namespace ProductRepositoryTemplate {
    export const templateId: string = 'f599bf48-d6fe-40dc-9f78-cf2d56bfb657';
    export const templateName: string = 'Product Repository';
  }
  export namespace ProductResourcesTemplate {
    export const templateId: string = 'fbca6069-d6c5-493e-83d7-c4a079e2717f';
    export const templateName: string = 'Product Resources';
    export const mainImageFieldId: string = 'bb61c579-50c7-40e7-b32e-a66bdc5d2dc3';
    export const mainImageFieldName: string = 'Main image';
  }
  export namespace ProductResourceTemplate {
    export const templateId: string = '0d52eb5c-a153-4688-a217-151a17de321e';
    export const templateName: string = 'Product Resource';
    export const resourceFieldId: string = 'fef7847e-3ac3-4c79-be15-bf407c461de6';
    export const resourceFieldName: string = 'Resource';
    export const typeFieldId: string = '10870bdb-03dc-4eb0-922e-c891a3b96f37';
    export const typeFieldName: string = 'Type';
    export const uriFieldId: string = 'd2a79cb7-a015-4ba4-b63d-beb127bf5f0e';
    export const uriFieldName: string = 'URI';
    export const externalIDFieldId: string = 'f2f908f1-806e-4706-911b-6794b73576d0';
    export const externalIDFieldName: string = 'ExternalID';
  }
  export namespace ProductSpecificationsTemplate {
    export const templateId: string = 'a1441a78-21ac-483c-89a6-d46521818b94';
    export const templateName: string = 'Product Specifications';
    export const descriptionFieldId: string = '92630a4a-369d-47ce-9ff1-a72a7dd67969';
    export const descriptionFieldName: string = 'Description';
    export const variantSpecificationsFieldId: string = '95cf928f-0ca7-4daf-b6b1-8dfe31ac9523';
    export const variantSpecificationsFieldName: string = 'VariantSpecifications';
  }
  export namespace ProductSpecificationTemplate {
    export const templateId: string = '687ebd28-8c90-4203-8527-a809d9b99680';
    export const templateName: string = 'Product Specification';
    export const groupFieldId: string = '6486cbd0-3e73-4293-9da9-52249bba163d';
    export const groupFieldName: string = 'Group';
    export const keyFieldId: string = '88892c6a-f865-493e-82b7-5c21e67c2fa4';
    export const keyFieldName: string = 'Key';
    export const lookupValueFieldId: string = '8377b000-0ee9-4f77-9eba-2b0822669f84';
    export const lookupValueFieldName: string = 'LookupValue';
    export const valueFieldId: string = '59b79baf-4f30-4eca-961d-4afab14d157c';
    export const valueFieldName: string = 'Value';
    export const externalIDFieldId: string = 'f2f908f1-806e-4706-911b-6794b73576d0';
    export const externalIDFieldName: string = 'ExternalID';
  }
  export namespace ProductsTemplate {
    export const templateId: string = '4d21ad2a-ba49-4dfc-9db9-c299c7fb7f6e';
    export const templateName: string = 'Products';
  }
  export namespace ProductStoresTemplate {
    export const templateId: string = 'f1f86d29-d324-4d86-8da4-c56770ae9283';
    export const templateName: string = 'Product Stores';
  }
  export namespace ProductStoreTemplate {
    export const templateId: string = '661d783a-fb8e-4d98-bbb4-8796e25bb2c7';
    export const templateName: string = 'Product Store';
    export const availableAddressCountriesFieldId: string = 'c8151ea4-ea10-421a-b44b-bcfc881e388a';
    export const availableAddressCountriesFieldName: string = 'AvailableAddressCountries';
    export const customerCareRegionalEmailAddressFieldId: string = '70ab3290-122a-4b3c-83d5-dd80db4567d3';
    export const customerCareRegionalEmailAddressFieldName: string = 'CustomerCareRegionalEmailAddress';
    export const customerCareRegionalPhoneNumberFieldId: string = '24e07817-b39e-4e4e-9d21-6e901c20d3f3';
    export const customerCareRegionalPhoneNumberFieldName: string = 'CustomerCareRegionalPhoneNumber';
    export const defaultCartLinkFieldId: string = '8c845038-eccb-4f69-a8e5-7da952cee494';
    export const defaultCartLinkFieldName: string = 'DefaultCartLink';
    export const enableRemoveChapterLinkFieldId: string = '0b5e7902-3a05-4b20-b1d3-9b03aac17021';
    export const enableRemoveChapterLinkFieldName: string = 'EnableRemoveChapterLink';
    export const isDefaultStoreFieldId: string = '272b3899-dd90-43be-933a-637cfbeb4872';
    export const isDefaultStoreFieldName: string = 'Is Default Store';
    export const isMasterStoreFieldId: string = 'a3f214ec-d7af-4590-818b-01c9ef57d7cf';
    export const isMasterStoreFieldName: string = 'Is Master Store';
    export const isStoreEnabledFieldId: string = '81f71b88-46cc-4d33-b28f-eb85d3b92221';
    export const isStoreEnabledFieldName: string = 'Is Store Enabled';
    export const isTieredStoreFieldId: string = 'b040c3bb-12ee-44ce-abfa-8034017d009c';
    export const isTieredStoreFieldName: string = 'Is Tiered Store';
    export const localCurrenciesFieldId: string = '49ba8681-8640-4dcb-adb8-682fa369bfdb';
    export const localCurrenciesFieldName: string = 'LocalCurrencies';
    export const storeCountriesFieldId: string = '97f9cbba-e6dd-4c3a-8657-b2d13f35bd5e';
    export const storeCountriesFieldName: string = 'Store Countries';
    export const storeCurrencyFieldId: string = '68411739-4d6c-4b23-a18c-91958b64a220';
    export const storeCurrencyFieldName: string = 'Store Currency';
    export const storeIdFieldId: string = 'fefd99ae-9387-4dcd-bb60-5e986b533054';
    export const storeIdFieldName: string = 'Store Id';
    export const storeIdentifierFieldId: string = '8241cef9-8088-4c7b-a667-7f9c303cc5a4';
    export const storeIdentifierFieldName: string = 'Store Identifier';
    export const storeNameFieldId: string = '00184368-779d-485f-9942-3f6570158ca0';
    export const storeNameFieldName: string = 'Store Name';
    export const storeLanguageFieldId: string = '72a58959-671e-4611-b99e-b89e69397f8d';
    export const storeLanguageFieldName: string = 'StoreLanguage';
    export const syncCountriesFieldId: string = '0a28ff27-3c94-46a3-8550-21342e82c466';
    export const syncCountriesFieldName: string = 'SyncCountries';
    export const tierFieldId: string = '87002ed3-365e-4a58-a467-768941b0d0dc';
    export const tierFieldName: string = 'Tier';
    export const websiteIdFieldId: string = '58af6183-73bd-4775-aeb6-ee4feeda40f9';
    export const websiteIdFieldName: string = 'WebsiteId';
    export const externalIDFieldId: string = 'f2f908f1-806e-4706-911b-6794b73576d0';
    export const externalIDFieldName: string = 'ExternalID';
  }
  export namespace ProductTemplate {
    export const templateId: string = '47d1a39e-3b4b-4428-a9f8-b446256c9581';
    export const templateName: string = 'Product';
    export const brandNameFieldId: string = '8f6025b8-1039-46ab-a606-849bfe0c6292';
    export const brandNameFieldName: string = 'BrandName';
    export const divisionsFieldId: string = '0f949b65-ee6c-4d70-b692-54d801ba812c';
    export const divisionsFieldName: string = 'Divisions';
    export const fullDescriptionFieldId: string = '25fe6930-d1f7-4924-95f9-770aa320a1f9';
    export const fullDescriptionFieldName: string = 'Full Description';
    export const identificationFieldId: string = '89d22f6f-409f-4463-bb09-0a79f29b9707';
    export const identificationFieldName: string = 'Identification';
    export const manufacturerFieldId: string = 'ea45edf7-cd8b-433f-99a6-ebfe3c7ce3a5';
    export const manufacturerFieldName: string = 'Manufacturer';
    export const modelNameFieldId: string = '7b3fad93-dcb0-4d8d-8181-13738502bea5';
    export const modelNameFieldName: string = 'ModelName';
    export const nameFieldId: string = 'e2cea8d5-65f4-4789-a778-b1b400b56928';
    export const nameFieldName: string = 'Name';
    export const productClassesFieldId: string = '891039c9-fe24-46b6-a5de-ba958a6925cf';
    export const productClassesFieldName: string = 'ProductClasses';
    export const productTypeFieldId: string = 'd5806f22-e082-4c82-87bd-439a62ef92c6';
    export const productTypeFieldName: string = 'ProductType';
    export const shortDescriptionFieldId: string = '87cf462a-a37a-4731-bf0b-370d3d7a9873';
    export const shortDescriptionFieldName: string = 'Short Description';
    export const externalIDFieldId: string = 'f2f908f1-806e-4706-911b-6794b73576d0';
    export const externalIDFieldName: string = 'ExternalID';
  }
  export namespace ProductTemplateMappingsTemplate {
    export const templateId: string = '679384ac-a080-4a48-b3c2-0794fb65e4e4';
    export const templateName: string = 'Product Template Mappings';
  }
  export namespace ProductTemplateMappingTemplate {
    export const templateId: string = '48856c4b-f788-4b33-bb42-10ca051a94cd';
    export const templateName: string = 'Product Template Mapping';
    export const productTemplateHeadlessFieldId: string = 'deb5fd81-4317-4ad7-a237-316446b2b345';
    export const productTemplateHeadlessFieldName: string = 'Product Template Headless';
    export const productTemplateFieldId: string = 'd78d8a82-643f-417c-bd1d-3abd6cb46bad';
    export const productTemplateFieldName: string = 'Product Template';
    export const productKindFieldId: string = 'd2e0628f-f47f-479c-ad48-5fc7ce2699f0';
    export const productKindFieldName: string = 'Product Kind';
    export const productTypeFieldId: string = 'b360298f-10b6-406e-a8ee-e3c6dcc2b8ab';
    export const productTypeFieldName: string = 'Product Type';
  }
  export namespace ProductTypeSpecificationTemplate {
    export const templateId: string = 'c696bec3-0414-4a3d-a4fe-3583a7ae051d';
    export const templateName: string = 'Product Type Specification';
    export const groupFieldId: string = '6486cbd0-3e73-4293-9da9-52249bba163d';
    export const groupFieldName: string = 'Group';
    export const keyFieldId: string = '88892c6a-f865-493e-82b7-5c21e67c2fa4';
    export const keyFieldName: string = 'Key';
    export const lookupValueFieldId: string = '8377b000-0ee9-4f77-9eba-2b0822669f84';
    export const lookupValueFieldName: string = 'LookupValue';
    export const valueFieldId: string = '59b79baf-4f30-4eca-961d-4afab14d157c';
    export const valueFieldName: string = 'Value';
    export const externalIDFieldId: string = 'f2f908f1-806e-4706-911b-6794b73576d0';
    export const externalIDFieldName: string = 'ExternalID';
  }
  export namespace ProductTypesTemplate {
    export const templateId: string = '26d1e614-5ae9-4e58-90ab-6cf9b9b6f938';
    export const templateName: string = 'Product Types';
  }
  export namespace ProductTypeTemplate {
    export const templateId: string = '2fc31cad-946f-4944-9a98-3da167bebc34';
    export const templateName: string = 'Product Type';
    export const externalParentIDFieldId: string = '5d89d406-b223-426e-a029-ce7138a9bab2';
    export const externalParentIDFieldName: string = 'ExternalParentID';
    export const descriptionFieldId: string = 'ec7d4ac3-83b6-4653-944a-097aafdfff07';
    export const descriptionFieldName: string = 'Description';
    export const nameFieldId: string = '93e7751e-aec7-4ea7-b5d8-b6d6653b3e8b';
    export const nameFieldName: string = 'Name';
    export const externalIDFieldId: string = 'f2f908f1-806e-4706-911b-6794b73576d0';
    export const externalIDFieldName: string = 'ExternalID';
  }
  export namespace RegionTemplate {
    export const templateId: string = 'c3a3d7c2-002d-4be9-a6d3-564481175f92';
    export const templateName: string = 'Region';
    export const codeFieldId: string = 'da530c00-2c3d-44fa-adbe-4555b94effcd';
    export const codeFieldName: string = 'Code';
    export const nameFieldId: string = '399c7c33-21a2-49c2-be65-d0d7ae956f0d';
    export const nameFieldName: string = 'Name';
  }
  export namespace REPCourseTemplate {
    export const templateId: string = 'ce4fd7d0-e6fa-400e-b09f-b29ddf7f2d0d';
    export const templateName: string = 'REP Course';
    export const courseLevelFieldId: string = '3203d3ac-9b02-416a-a436-f5133c265e23';
    export const courseLevelFieldName: string = 'course_level';
    export const formatFieldId: string = '603747cb-0bc8-4a3d-9ccc-fd3dabcd365d';
    export const formatFieldName: string = 'format';
    export const pdusFieldId: string = 'bb810404-13cd-4f78-a62d-7c2294674950';
    export const pdusFieldName: string = 'pdus';
    export const repProviderIdFieldId: string = 'a437255c-8fdf-44e5-93a2-1b64deacfc5f';
    export const repProviderIdFieldName: string = 'rep_provider_id';
    export const repProviderNameFieldId: string = 'f6da501a-305f-4467-8b50-588b8f2c336f';
    export const repProviderNameFieldName: string = 'rep_provider_name';
    export const b2bFullDescriptionFieldId: string = 'e919cee7-a090-4848-b062-626a9e3c2ea7';
    export const b2bFullDescriptionFieldName: string = 'B2b Full Description';
    export const b2bImageFieldId: string = 'a88b0bd5-dc5c-4049-b3ae-5e48fa881c93';
    export const b2bImageFieldName: string = 'B2b image';
    export const b2bShortDescriptionFieldId: string = '540b1fca-a499-4ebd-80a0-cf252801e994';
    export const b2bShortDescriptionFieldName: string = 'B2b Short Description';
    export const b2bTitleFieldId: string = '15389578-4623-4398-901c-e9a7b5533a45';
    export const b2bTitleFieldName: string = 'B2b Title';
    export const bundleProductOptionsFieldId: string = '60f860f0-69cb-4834-95a7-1391ba665a59';
    export const bundleProductOptionsFieldName: string = 'BundleProductOptions';
    export const productCustomOptionsFieldId: string = '3350a005-c47e-461c-b6ce-17e223960c71';
    export const productCustomOptionsFieldName: string = 'ProductCustomOptions';
    export const backordersFieldId: string = '6d849509-14d3-492a-b49a-550291a14caa';
    export const backordersFieldName: string = 'Backorders';
    export const courseKitTypeFieldId: string = '1e58883a-96ca-4e89-9b68-953a9678734c';
    export const courseKitTypeFieldName: string = 'course_kit_type';
    export const courseTypesFieldId: string = '5fc60777-de7e-4055-945e-6445f00f41a3';
    export const courseTypesFieldName: string = 'course_type';
    export const disabledProductURLFieldId: string = '0807e662-73c5-41c4-a975-96fd514e0ff9';
    export const disabledProductURLFieldName: string = 'disabled_Product_URL';
    export const fulfillmentProviderFieldId: string = '5ca91282-238c-4179-a84b-d7a777753a16';
    export const fulfillmentProviderFieldName: string = 'fulfillment_provider';
    export const includedInMembershipFieldId: string = 'eb7fc950-cb4c-41e4-82d1-747429338465';
    export const includedInMembershipFieldName: string = 'included_in_membership';
    export const isInStockFieldId: string = 'bb29b6b4-3456-4d3a-94db-b02656ca47a3';
    export const isInStockFieldName: string = 'Is In Stock';
    export const isReturnableFieldId: string = 'cae45dfb-0c62-4857-a91a-b72b50354c7d';
    export const isReturnableFieldName: string = 'Is Returnable';
    export const isMembershipFieldId: string = '47e537b2-9d39-47db-81f7-295157ed880e';
    export const isMembershipFieldName: string = 'is_membership';
    export const isSubscriptionProductFieldId: string = '4b7c5ef9-08c0-41c5-aa41-278641707375';
    export const isSubscriptionProductFieldName: string = 'is_subscription_product';
    export const languageFieldId: string = '595359da-ec9f-4c4c-a6a2-7546c7ef1240';
    export const languageFieldName: string = 'language';
    export const overviewDescriptionFieldId: string = 'df536977-1ee5-47e9-b7f6-11f25a9d9330';
    export const overviewDescriptionFieldName: string = 'OverviewDescription';
    export const pmiCatalogsFieldId: string = '8e050945-712f-407c-8c38-8835b5488376';
    export const pmiCatalogsFieldName: string = 'pmi_catalog';
    export const pmiProductCategoryFieldId: string = '3ce1903b-97fa-4f44-9395-4871dd6b91e0';
    export const pmiProductCategoryFieldName: string = 'pmi_product_category';
    export const pmiProductFamilyFieldId: string = '8a3f0338-265e-445d-8d06-47fb117c7bc4';
    export const pmiProductFamilyFieldName: string = 'pmi_product_family';
    export const pmiProductFormatFieldId: string = '8f191f3e-4c52-4091-a6dd-78915bc7af3a';
    export const pmiProductFormatFieldName: string = 'pmi_product_format';
    export const pmiProductTypeFieldId: string = 'fdb83207-3c1c-4566-ba47-d28568bbdeba';
    export const pmiProductTypeFieldName: string = 'pmi_product_type';
    export const productKindFieldId: string = '3d5ca766-9602-4a5b-aff6-8fcb50fca321';
    export const productKindFieldName: string = 'Product Kind';
    export const productStoreFieldId: string = '8e4499d7-9df2-4e4a-a170-3342dc989426';
    export const productStoreFieldName: string = 'Product Store';
    export const quantityFieldId: string = '549f9561-22be-4070-84f1-c82639c08c6d';
    export const quantityFieldName: string = 'Quantity';
    export const singleMembershipFieldId: string = '45cef470-41fe-48e2-835d-f7cc180fa1a5';
    export const singleMembershipFieldName: string = 'single_membership';
    export const soldAsBundleFieldId: string = '42c17217-ecc2-4d07-8d35-c5913a94d788';
    export const soldAsBundleFieldName: string = 'sold_as_bundle';
    export const subscriptionFrequencyFieldId: string = 'c2ddffb8-5490-4ebf-ad2e-1b220a466c5a';
    export const subscriptionFrequencyFieldName: string = 'subscription_frequency';
    export const weightFieldId: string = 'f3ef8df0-e281-4d4e-bfba-7d950d517170';
    export const weightFieldName: string = 'Weight';
    export const pmiProductIdFieldId: string = 'aa683096-5550-4fee-bf74-ffa9a08957bc';
    export const pmiProductIdFieldName: string = 'pmi_product_id';
    export const membershipTextHighlightColorFieldId: string = 'b18d1660-c856-4d2f-8e3d-c676d4a7421e';
    export const membershipTextHighlightColorFieldName: string = 'membershipTextHighlightColor';
    export const recommendedAltTextFieldId: string = '3568a091-d725-4a13-9b45-fb21802c9ca3';
    export const recommendedAltTextFieldName: string = 'RecommendedAltText';
    export const showCTAFieldId: string = '402d02c0-c629-4592-b316-2d1cfd687197';
    export const showCTAFieldName: string = 'showCTA';
    export const showPricesFieldId: string = '8318d912-a453-4e22-8ca2-6ddbc96c7646';
    export const showPricesFieldName: string = 'showPrices';
    export const customCartFieldId: string = 'b8fa0117-1511-40bb-95ec-7b265962cec0';
    export const customCartFieldName: string = 'custom_cart';
    export const heroBackgroundImageFieldId: string = '6d95f762-44dd-4123-8b31-36a03113d48a';
    export const heroBackgroundImageFieldName: string = 'HeroBackgroundImage';
    export const heroBackgroundImageMobileFieldId: string = 'daf9a28d-b84c-4a67-8595-a297c050909f';
    export const heroBackgroundImageMobileFieldName: string = 'HeroBackgroundImageMobile';
    export const heroImageFieldId: string = '5a0d0f52-211e-4e3c-ab2f-71726cbd9057';
    export const heroImageFieldName: string = 'HeroImage';
    export const imageFieldId: string = 'c89f0ba8-67f4-44f6-a7f1-f68a46cdbcb6';
    export const imageFieldName: string = 'Image';
    export const smallImageFieldId: string = 'f482cd3c-6b97-48c3-ab80-a898ca3f874b';
    export const smallImageFieldName: string = 'Small Image';
    export const thumbnailFieldId: string = '2e2436e4-e5c4-4a6c-909e-1eb7931cb2d9';
    export const thumbnailFieldName: string = 'Thumbnail';
    export const useDarkThemeForHeroFieldId: string = '7bdf557d-6804-49e5-acc8-a1835731babc';
    export const useDarkThemeForHeroFieldName: string = 'UseDarkThemeForHero';
    export const isFeaturedFieldId: string = '3bfaed00-9aab-42c8-9fb7-8e8d1f0027a6';
    export const isFeaturedFieldName: string = 'Is Featured';
    export const promoBadgesFieldId: string = '23ee2ae2-1d4d-4e51-8e2a-b251e55e8a0d';
    export const promoBadgesFieldName: string = 'PromoBadges';
    export const seoFriendlyTitleFieldId: string = '260a336f-7541-4b8c-a023-3198ca421c86';
    export const seoFriendlyTitleFieldName: string = 'SeoFriendlyTitle';
    export const wordmarkFieldId: string = '1fc37bfb-e2f6-4337-b793-b12477c305bd';
    export const wordmarkFieldName: string = 'Wordmark';
    export const isDeletedFieldId: string = '2aaa5d2e-4301-40ad-b6f9-d752c5fad3e3';
    export const isDeletedFieldName: string = 'Is Deleted';
    export const pmiMembershipFieldId: string = '50845396-2667-4e2a-a548-93c81fc48043';
    export const pmiMembershipFieldName: string = 'PMI Membership';
    export const pmiSegmentFieldId: string = '3b0804fc-df69-4424-9d09-892341852d72';
    export const pmiSegmentFieldName: string = 'PMI Segment';
    export const abPricesFieldId: string = '97749ffe-4bd0-4190-abe1-c92c68de1397';
    export const abPricesFieldName: string = 'AB Prices';
    export const countryPricesFieldId: string = '202e0c1e-5500-46be-bb5d-14a400eb6130';
    export const countryPricesFieldName: string = 'Country Prices';
    export const globalPriceFieldId: string = '3a732fb5-16ff-448d-932a-143810b5f682';
    export const globalPriceFieldName: string = 'Global Price';
    export const groupPricesFieldId: string = 'da043375-9353-44e1-b45c-d4ed88459ebf';
    export const groupPricesFieldName: string = 'Group Prices';
    export const renewalPriceFieldId: string = 'b3f78b0d-bf55-4b73-b003-dbafdb00ccb3';
    export const renewalPriceFieldName: string = 'Renewal Price';
    export const zonePricesFieldId: string = 'babb6e53-3777-4916-a764-01aad64cce60';
    export const zonePricesFieldName: string = 'Zone Prices';
    export const brandNameFieldId: string = '8f6025b8-1039-46ab-a606-849bfe0c6292';
    export const brandNameFieldName: string = 'BrandName';
    export const divisionsFieldId: string = '0f949b65-ee6c-4d70-b692-54d801ba812c';
    export const divisionsFieldName: string = 'Divisions';
    export const fullDescriptionFieldId: string = '25fe6930-d1f7-4924-95f9-770aa320a1f9';
    export const fullDescriptionFieldName: string = 'Full Description';
    export const identificationFieldId: string = '89d22f6f-409f-4463-bb09-0a79f29b9707';
    export const identificationFieldName: string = 'Identification';
    export const manufacturerFieldId: string = 'ea45edf7-cd8b-433f-99a6-ebfe3c7ce3a5';
    export const manufacturerFieldName: string = 'Manufacturer';
    export const modelNameFieldId: string = '7b3fad93-dcb0-4d8d-8181-13738502bea5';
    export const modelNameFieldName: string = 'ModelName';
    export const nameFieldId: string = 'e2cea8d5-65f4-4789-a778-b1b400b56928';
    export const nameFieldName: string = 'Name';
    export const productClassesFieldId: string = '891039c9-fe24-46b6-a5de-ba958a6925cf';
    export const productClassesFieldName: string = 'ProductClasses';
    export const productTypeFieldId: string = 'd5806f22-e082-4c82-87bd-439a62ef92c6';
    export const productTypeFieldName: string = 'ProductType';
    export const shortDescriptionFieldId: string = '87cf462a-a37a-4731-bf0b-370d3d7a9873';
    export const shortDescriptionFieldName: string = 'Short Description';
    export const externalIDFieldId: string = 'f2f908f1-806e-4706-911b-6794b73576d0';
    export const externalIDFieldName: string = 'ExternalID';
    export const shortTitleFieldId: string = 'e27e197c-ad78-4352-bb04-cb7da291a88e';
    export const shortTitleFieldName: string = 'Short Title';
    export const titleFieldId: string = '56d15ac6-4a9b-4e75-830c-a30e63384011';
    export const titleFieldName: string = 'Title';
    export const iconFieldId: string = '734b447e-44ec-4739-8acc-b89fd51e1fec';
    export const iconFieldName: string = 'Icon';
    export const iconClassFieldId: string = '4796a76c-5867-4640-9dca-8bb7a17fb185';
    export const iconClassFieldName: string = 'IconClass';
    export const level2HierarchiesFieldId: string = '691c85ee-9f50-4dfd-b3af-a6c0d1d676c9';
    export const level2HierarchiesFieldName: string = 'level_2_hierarchy';
    export const level3HierarchiesFieldId: string = 'd3a4b7de-eafd-4b13-8df7-67c7220dbdda';
    export const level3HierarchiesFieldName: string = 'level_3_hierarchy';
    export const level4HierarchiesFieldId: string = 'b695c62a-0edd-4951-bd55-024699c1b8ad';
    export const level4HierarchiesFieldName: string = 'level_4_hierarchy';
  }
  export namespace REPMemberTemplate {
    export const templateId: string = '63ed873e-e791-46c5-9932-cfb9bfd66e85';
    export const templateName: string = 'REP Member';
    export const b2bFullDescriptionFieldId: string = 'e919cee7-a090-4848-b062-626a9e3c2ea7';
    export const b2bFullDescriptionFieldName: string = 'B2b Full Description';
    export const b2bImageFieldId: string = 'a88b0bd5-dc5c-4049-b3ae-5e48fa881c93';
    export const b2bImageFieldName: string = 'B2b image';
    export const b2bShortDescriptionFieldId: string = '540b1fca-a499-4ebd-80a0-cf252801e994';
    export const b2bShortDescriptionFieldName: string = 'B2b Short Description';
    export const b2bTitleFieldId: string = '15389578-4623-4398-901c-e9a7b5533a45';
    export const b2bTitleFieldName: string = 'B2b Title';
    export const bundleProductOptionsFieldId: string = '60f860f0-69cb-4834-95a7-1391ba665a59';
    export const bundleProductOptionsFieldName: string = 'BundleProductOptions';
    export const productCustomOptionsFieldId: string = '3350a005-c47e-461c-b6ce-17e223960c71';
    export const productCustomOptionsFieldName: string = 'ProductCustomOptions';
    export const backordersFieldId: string = '6d849509-14d3-492a-b49a-550291a14caa';
    export const backordersFieldName: string = 'Backorders';
    export const courseKitTypeFieldId: string = '1e58883a-96ca-4e89-9b68-953a9678734c';
    export const courseKitTypeFieldName: string = 'course_kit_type';
    export const courseTypesFieldId: string = '5fc60777-de7e-4055-945e-6445f00f41a3';
    export const courseTypesFieldName: string = 'course_type';
    export const disabledProductURLFieldId: string = '0807e662-73c5-41c4-a975-96fd514e0ff9';
    export const disabledProductURLFieldName: string = 'disabled_Product_URL';
    export const fulfillmentProviderFieldId: string = '5ca91282-238c-4179-a84b-d7a777753a16';
    export const fulfillmentProviderFieldName: string = 'fulfillment_provider';
    export const includedInMembershipFieldId: string = 'eb7fc950-cb4c-41e4-82d1-747429338465';
    export const includedInMembershipFieldName: string = 'included_in_membership';
    export const isInStockFieldId: string = 'bb29b6b4-3456-4d3a-94db-b02656ca47a3';
    export const isInStockFieldName: string = 'Is In Stock';
    export const isReturnableFieldId: string = 'cae45dfb-0c62-4857-a91a-b72b50354c7d';
    export const isReturnableFieldName: string = 'Is Returnable';
    export const isMembershipFieldId: string = '47e537b2-9d39-47db-81f7-295157ed880e';
    export const isMembershipFieldName: string = 'is_membership';
    export const isSubscriptionProductFieldId: string = '4b7c5ef9-08c0-41c5-aa41-278641707375';
    export const isSubscriptionProductFieldName: string = 'is_subscription_product';
    export const languageFieldId: string = '595359da-ec9f-4c4c-a6a2-7546c7ef1240';
    export const languageFieldName: string = 'language';
    export const overviewDescriptionFieldId: string = 'df536977-1ee5-47e9-b7f6-11f25a9d9330';
    export const overviewDescriptionFieldName: string = 'OverviewDescription';
    export const pmiCatalogsFieldId: string = '8e050945-712f-407c-8c38-8835b5488376';
    export const pmiCatalogsFieldName: string = 'pmi_catalog';
    export const pmiProductCategoryFieldId: string = '3ce1903b-97fa-4f44-9395-4871dd6b91e0';
    export const pmiProductCategoryFieldName: string = 'pmi_product_category';
    export const pmiProductFamilyFieldId: string = '8a3f0338-265e-445d-8d06-47fb117c7bc4';
    export const pmiProductFamilyFieldName: string = 'pmi_product_family';
    export const pmiProductFormatFieldId: string = '8f191f3e-4c52-4091-a6dd-78915bc7af3a';
    export const pmiProductFormatFieldName: string = 'pmi_product_format';
    export const pmiProductTypeFieldId: string = 'fdb83207-3c1c-4566-ba47-d28568bbdeba';
    export const pmiProductTypeFieldName: string = 'pmi_product_type';
    export const productKindFieldId: string = '3d5ca766-9602-4a5b-aff6-8fcb50fca321';
    export const productKindFieldName: string = 'Product Kind';
    export const productStoreFieldId: string = '8e4499d7-9df2-4e4a-a170-3342dc989426';
    export const productStoreFieldName: string = 'Product Store';
    export const quantityFieldId: string = '549f9561-22be-4070-84f1-c82639c08c6d';
    export const quantityFieldName: string = 'Quantity';
    export const singleMembershipFieldId: string = '45cef470-41fe-48e2-835d-f7cc180fa1a5';
    export const singleMembershipFieldName: string = 'single_membership';
    export const soldAsBundleFieldId: string = '42c17217-ecc2-4d07-8d35-c5913a94d788';
    export const soldAsBundleFieldName: string = 'sold_as_bundle';
    export const subscriptionFrequencyFieldId: string = 'c2ddffb8-5490-4ebf-ad2e-1b220a466c5a';
    export const subscriptionFrequencyFieldName: string = 'subscription_frequency';
    export const weightFieldId: string = 'f3ef8df0-e281-4d4e-bfba-7d950d517170';
    export const weightFieldName: string = 'Weight';
    export const pmiProductIdFieldId: string = 'aa683096-5550-4fee-bf74-ffa9a08957bc';
    export const pmiProductIdFieldName: string = 'pmi_product_id';
    export const membershipTextHighlightColorFieldId: string = 'b18d1660-c856-4d2f-8e3d-c676d4a7421e';
    export const membershipTextHighlightColorFieldName: string = 'membershipTextHighlightColor';
    export const recommendedAltTextFieldId: string = '3568a091-d725-4a13-9b45-fb21802c9ca3';
    export const recommendedAltTextFieldName: string = 'RecommendedAltText';
    export const showCTAFieldId: string = '402d02c0-c629-4592-b316-2d1cfd687197';
    export const showCTAFieldName: string = 'showCTA';
    export const showPricesFieldId: string = '8318d912-a453-4e22-8ca2-6ddbc96c7646';
    export const showPricesFieldName: string = 'showPrices';
    export const customCartFieldId: string = 'b8fa0117-1511-40bb-95ec-7b265962cec0';
    export const customCartFieldName: string = 'custom_cart';
    export const heroBackgroundImageFieldId: string = '6d95f762-44dd-4123-8b31-36a03113d48a';
    export const heroBackgroundImageFieldName: string = 'HeroBackgroundImage';
    export const heroBackgroundImageMobileFieldId: string = 'daf9a28d-b84c-4a67-8595-a297c050909f';
    export const heroBackgroundImageMobileFieldName: string = 'HeroBackgroundImageMobile';
    export const heroImageFieldId: string = '5a0d0f52-211e-4e3c-ab2f-71726cbd9057';
    export const heroImageFieldName: string = 'HeroImage';
    export const imageFieldId: string = 'c89f0ba8-67f4-44f6-a7f1-f68a46cdbcb6';
    export const imageFieldName: string = 'Image';
    export const smallImageFieldId: string = 'f482cd3c-6b97-48c3-ab80-a898ca3f874b';
    export const smallImageFieldName: string = 'Small Image';
    export const thumbnailFieldId: string = '2e2436e4-e5c4-4a6c-909e-1eb7931cb2d9';
    export const thumbnailFieldName: string = 'Thumbnail';
    export const useDarkThemeForHeroFieldId: string = '7bdf557d-6804-49e5-acc8-a1835731babc';
    export const useDarkThemeForHeroFieldName: string = 'UseDarkThemeForHero';
    export const isFeaturedFieldId: string = '3bfaed00-9aab-42c8-9fb7-8e8d1f0027a6';
    export const isFeaturedFieldName: string = 'Is Featured';
    export const promoBadgesFieldId: string = '23ee2ae2-1d4d-4e51-8e2a-b251e55e8a0d';
    export const promoBadgesFieldName: string = 'PromoBadges';
    export const seoFriendlyTitleFieldId: string = '260a336f-7541-4b8c-a023-3198ca421c86';
    export const seoFriendlyTitleFieldName: string = 'SeoFriendlyTitle';
    export const wordmarkFieldId: string = '1fc37bfb-e2f6-4337-b793-b12477c305bd';
    export const wordmarkFieldName: string = 'Wordmark';
    export const isDeletedFieldId: string = '2aaa5d2e-4301-40ad-b6f9-d752c5fad3e3';
    export const isDeletedFieldName: string = 'Is Deleted';
    export const pmiMembershipFieldId: string = '50845396-2667-4e2a-a548-93c81fc48043';
    export const pmiMembershipFieldName: string = 'PMI Membership';
    export const pmiSegmentFieldId: string = '3b0804fc-df69-4424-9d09-892341852d72';
    export const pmiSegmentFieldName: string = 'PMI Segment';
    export const abPricesFieldId: string = '97749ffe-4bd0-4190-abe1-c92c68de1397';
    export const abPricesFieldName: string = 'AB Prices';
    export const countryPricesFieldId: string = '202e0c1e-5500-46be-bb5d-14a400eb6130';
    export const countryPricesFieldName: string = 'Country Prices';
    export const globalPriceFieldId: string = '3a732fb5-16ff-448d-932a-143810b5f682';
    export const globalPriceFieldName: string = 'Global Price';
    export const groupPricesFieldId: string = 'da043375-9353-44e1-b45c-d4ed88459ebf';
    export const groupPricesFieldName: string = 'Group Prices';
    export const renewalPriceFieldId: string = 'b3f78b0d-bf55-4b73-b003-dbafdb00ccb3';
    export const renewalPriceFieldName: string = 'Renewal Price';
    export const zonePricesFieldId: string = 'babb6e53-3777-4916-a764-01aad64cce60';
    export const zonePricesFieldName: string = 'Zone Prices';
    export const brandNameFieldId: string = '8f6025b8-1039-46ab-a606-849bfe0c6292';
    export const brandNameFieldName: string = 'BrandName';
    export const divisionsFieldId: string = '0f949b65-ee6c-4d70-b692-54d801ba812c';
    export const divisionsFieldName: string = 'Divisions';
    export const fullDescriptionFieldId: string = '25fe6930-d1f7-4924-95f9-770aa320a1f9';
    export const fullDescriptionFieldName: string = 'Full Description';
    export const identificationFieldId: string = '89d22f6f-409f-4463-bb09-0a79f29b9707';
    export const identificationFieldName: string = 'Identification';
    export const manufacturerFieldId: string = 'ea45edf7-cd8b-433f-99a6-ebfe3c7ce3a5';
    export const manufacturerFieldName: string = 'Manufacturer';
    export const modelNameFieldId: string = '7b3fad93-dcb0-4d8d-8181-13738502bea5';
    export const modelNameFieldName: string = 'ModelName';
    export const nameFieldId: string = 'e2cea8d5-65f4-4789-a778-b1b400b56928';
    export const nameFieldName: string = 'Name';
    export const productClassesFieldId: string = '891039c9-fe24-46b6-a5de-ba958a6925cf';
    export const productClassesFieldName: string = 'ProductClasses';
    export const productTypeFieldId: string = 'd5806f22-e082-4c82-87bd-439a62ef92c6';
    export const productTypeFieldName: string = 'ProductType';
    export const shortDescriptionFieldId: string = '87cf462a-a37a-4731-bf0b-370d3d7a9873';
    export const shortDescriptionFieldName: string = 'Short Description';
    export const externalIDFieldId: string = 'f2f908f1-806e-4706-911b-6794b73576d0';
    export const externalIDFieldName: string = 'ExternalID';
    export const shortTitleFieldId: string = 'e27e197c-ad78-4352-bb04-cb7da291a88e';
    export const shortTitleFieldName: string = 'Short Title';
    export const titleFieldId: string = '56d15ac6-4a9b-4e75-830c-a30e63384011';
    export const titleFieldName: string = 'Title';
    export const iconFieldId: string = '734b447e-44ec-4739-8acc-b89fd51e1fec';
    export const iconFieldName: string = 'Icon';
    export const iconClassFieldId: string = '4796a76c-5867-4640-9dca-8bb7a17fb185';
    export const iconClassFieldName: string = 'IconClass';
    export const level2HierarchiesFieldId: string = '691c85ee-9f50-4dfd-b3af-a6c0d1d676c9';
    export const level2HierarchiesFieldName: string = 'level_2_hierarchy';
    export const level3HierarchiesFieldId: string = 'd3a4b7de-eafd-4b13-8df7-67c7220dbdda';
    export const level3HierarchiesFieldName: string = 'level_3_hierarchy';
    export const level4HierarchiesFieldId: string = 'b695c62a-0edd-4951-bd55-024699c1b8ad';
    export const level4HierarchiesFieldName: string = 'level_4_hierarchy';
  }
  export namespace ResourceFolderTemplate {
    export const templateId: string = 'a0a1342a-6339-445c-94a1-31cb7756aa0b';
    export const templateName: string = 'Resource Folder';
  }
  export namespace SearchSettingsTemplate {
    export const templateId: string = '5901d826-15c7-4219-a985-bcdc46736d70';
    export const templateName: string = 'Search Settings';
    export const itemsPerPageFieldId: string = '35fc96a7-bf12-4c02-a66a-1ae68d3b4432';
    export const itemsPerPageFieldName: string = 'Items Per Page';
    export const searchFacetsFieldId: string = 'a3683321-18c4-424a-94aa-fc1ce9b03b92';
    export const searchFacetsFieldName: string = 'Search Facets';
    export const sortFieldsFieldId: string = '91c3811d-b760-475c-87e7-0aee5d4080c9';
    export const sortFieldsFieldName: string = 'Sort Fields';
  }
  export namespace SpecificationLookupsTemplate {
    export const templateId: string = 'a8a3d56b-bca0-4877-b137-86992f341d81';
    export const templateName: string = 'Specification Lookups';
  }
  export namespace SpecificationLookupTemplate {
    export const templateId: string = '64ee9778-10d3-492a-a52e-d229ce8b9fad';
    export const templateName: string = 'Specification Lookup';
    export const nameFieldId: string = 'f3f37114-38b0-43df-b9c7-512b62081bf1';
    export const nameFieldName: string = 'Name';
    export const externalIDFieldId: string = 'f2f908f1-806e-4706-911b-6794b73576d0';
    export const externalIDFieldName: string = 'ExternalID';
  }
  export namespace SpecificationTemplate {
    export const templateId: string = '284f5f48-7d2e-48d3-b3f5-c8f45549a2a0';
    export const templateName: string = 'Specification';
    export const nameFieldId: string = '02eaa1d4-a90a-4124-b2ba-3453794daf10';
    export const nameFieldName: string = 'Name';
  }
  export namespace SynchronizationTemplate {
    export const templateId: string = '240334a5-cfe8-4450-bb85-253d620cba02';
    export const templateName: string = 'Synchronization';
    export const externalIDFieldId: string = 'f2f908f1-806e-4706-911b-6794b73576d0';
    export const externalIDFieldName: string = 'ExternalID';
  }
  export namespace WebinarTemplate {
    export const templateId: string = '805fa044-5fd1-45c2-acf2-b199d9dd2366';
    export const templateName: string = 'Webinar';
    export const courseLevelFieldId: string = '10662483-c0e9-4e7c-81b0-58f7db29f0b0';
    export const courseLevelFieldName: string = 'course_level';
    export const formatFieldId: string = '67596356-2d5c-4213-8a6f-e55536ab098a';
    export const formatFieldName: string = 'format';
    export const pdusFieldId: string = 'c48a932b-6fa1-472a-b7ae-1b02c6d59853';
    export const pdusFieldName: string = 'pdus';
    export const b2bFullDescriptionFieldId: string = 'e919cee7-a090-4848-b062-626a9e3c2ea7';
    export const b2bFullDescriptionFieldName: string = 'B2b Full Description';
    export const b2bImageFieldId: string = 'a88b0bd5-dc5c-4049-b3ae-5e48fa881c93';
    export const b2bImageFieldName: string = 'B2b image';
    export const b2bShortDescriptionFieldId: string = '540b1fca-a499-4ebd-80a0-cf252801e994';
    export const b2bShortDescriptionFieldName: string = 'B2b Short Description';
    export const b2bTitleFieldId: string = '15389578-4623-4398-901c-e9a7b5533a45';
    export const b2bTitleFieldName: string = 'B2b Title';
    export const bundleProductOptionsFieldId: string = '60f860f0-69cb-4834-95a7-1391ba665a59';
    export const bundleProductOptionsFieldName: string = 'BundleProductOptions';
    export const productCustomOptionsFieldId: string = '3350a005-c47e-461c-b6ce-17e223960c71';
    export const productCustomOptionsFieldName: string = 'ProductCustomOptions';
    export const backordersFieldId: string = '6d849509-14d3-492a-b49a-550291a14caa';
    export const backordersFieldName: string = 'Backorders';
    export const courseKitTypeFieldId: string = '1e58883a-96ca-4e89-9b68-953a9678734c';
    export const courseKitTypeFieldName: string = 'course_kit_type';
    export const courseTypesFieldId: string = '5fc60777-de7e-4055-945e-6445f00f41a3';
    export const courseTypesFieldName: string = 'course_type';
    export const disabledProductURLFieldId: string = '0807e662-73c5-41c4-a975-96fd514e0ff9';
    export const disabledProductURLFieldName: string = 'disabled_Product_URL';
    export const fulfillmentProviderFieldId: string = '5ca91282-238c-4179-a84b-d7a777753a16';
    export const fulfillmentProviderFieldName: string = 'fulfillment_provider';
    export const includedInMembershipFieldId: string = 'eb7fc950-cb4c-41e4-82d1-747429338465';
    export const includedInMembershipFieldName: string = 'included_in_membership';
    export const isInStockFieldId: string = 'bb29b6b4-3456-4d3a-94db-b02656ca47a3';
    export const isInStockFieldName: string = 'Is In Stock';
    export const isReturnableFieldId: string = 'cae45dfb-0c62-4857-a91a-b72b50354c7d';
    export const isReturnableFieldName: string = 'Is Returnable';
    export const isMembershipFieldId: string = '47e537b2-9d39-47db-81f7-295157ed880e';
    export const isMembershipFieldName: string = 'is_membership';
    export const isSubscriptionProductFieldId: string = '4b7c5ef9-08c0-41c5-aa41-278641707375';
    export const isSubscriptionProductFieldName: string = 'is_subscription_product';
    export const languageFieldId: string = '595359da-ec9f-4c4c-a6a2-7546c7ef1240';
    export const languageFieldName: string = 'language';
    export const overviewDescriptionFieldId: string = 'df536977-1ee5-47e9-b7f6-11f25a9d9330';
    export const overviewDescriptionFieldName: string = 'OverviewDescription';
    export const pmiCatalogsFieldId: string = '8e050945-712f-407c-8c38-8835b5488376';
    export const pmiCatalogsFieldName: string = 'pmi_catalog';
    export const pmiProductCategoryFieldId: string = '3ce1903b-97fa-4f44-9395-4871dd6b91e0';
    export const pmiProductCategoryFieldName: string = 'pmi_product_category';
    export const pmiProductFamilyFieldId: string = '8a3f0338-265e-445d-8d06-47fb117c7bc4';
    export const pmiProductFamilyFieldName: string = 'pmi_product_family';
    export const pmiProductFormatFieldId: string = '8f191f3e-4c52-4091-a6dd-78915bc7af3a';
    export const pmiProductFormatFieldName: string = 'pmi_product_format';
    export const pmiProductTypeFieldId: string = 'fdb83207-3c1c-4566-ba47-d28568bbdeba';
    export const pmiProductTypeFieldName: string = 'pmi_product_type';
    export const productKindFieldId: string = '3d5ca766-9602-4a5b-aff6-8fcb50fca321';
    export const productKindFieldName: string = 'Product Kind';
    export const productStoreFieldId: string = '8e4499d7-9df2-4e4a-a170-3342dc989426';
    export const productStoreFieldName: string = 'Product Store';
    export const quantityFieldId: string = '549f9561-22be-4070-84f1-c82639c08c6d';
    export const quantityFieldName: string = 'Quantity';
    export const singleMembershipFieldId: string = '45cef470-41fe-48e2-835d-f7cc180fa1a5';
    export const singleMembershipFieldName: string = 'single_membership';
    export const soldAsBundleFieldId: string = '42c17217-ecc2-4d07-8d35-c5913a94d788';
    export const soldAsBundleFieldName: string = 'sold_as_bundle';
    export const subscriptionFrequencyFieldId: string = 'c2ddffb8-5490-4ebf-ad2e-1b220a466c5a';
    export const subscriptionFrequencyFieldName: string = 'subscription_frequency';
    export const weightFieldId: string = 'f3ef8df0-e281-4d4e-bfba-7d950d517170';
    export const weightFieldName: string = 'Weight';
    export const pmiProductIdFieldId: string = 'aa683096-5550-4fee-bf74-ffa9a08957bc';
    export const pmiProductIdFieldName: string = 'pmi_product_id';
    export const membershipTextHighlightColorFieldId: string = 'b18d1660-c856-4d2f-8e3d-c676d4a7421e';
    export const membershipTextHighlightColorFieldName: string = 'membershipTextHighlightColor';
    export const recommendedAltTextFieldId: string = '3568a091-d725-4a13-9b45-fb21802c9ca3';
    export const recommendedAltTextFieldName: string = 'RecommendedAltText';
    export const showCTAFieldId: string = '402d02c0-c629-4592-b316-2d1cfd687197';
    export const showCTAFieldName: string = 'showCTA';
    export const showPricesFieldId: string = '8318d912-a453-4e22-8ca2-6ddbc96c7646';
    export const showPricesFieldName: string = 'showPrices';
    export const customCartFieldId: string = 'b8fa0117-1511-40bb-95ec-7b265962cec0';
    export const customCartFieldName: string = 'custom_cart';
    export const heroBackgroundImageFieldId: string = '6d95f762-44dd-4123-8b31-36a03113d48a';
    export const heroBackgroundImageFieldName: string = 'HeroBackgroundImage';
    export const heroBackgroundImageMobileFieldId: string = 'daf9a28d-b84c-4a67-8595-a297c050909f';
    export const heroBackgroundImageMobileFieldName: string = 'HeroBackgroundImageMobile';
    export const heroImageFieldId: string = '5a0d0f52-211e-4e3c-ab2f-71726cbd9057';
    export const heroImageFieldName: string = 'HeroImage';
    export const imageFieldId: string = 'c89f0ba8-67f4-44f6-a7f1-f68a46cdbcb6';
    export const imageFieldName: string = 'Image';
    export const smallImageFieldId: string = 'f482cd3c-6b97-48c3-ab80-a898ca3f874b';
    export const smallImageFieldName: string = 'Small Image';
    export const thumbnailFieldId: string = '2e2436e4-e5c4-4a6c-909e-1eb7931cb2d9';
    export const thumbnailFieldName: string = 'Thumbnail';
    export const useDarkThemeForHeroFieldId: string = '7bdf557d-6804-49e5-acc8-a1835731babc';
    export const useDarkThemeForHeroFieldName: string = 'UseDarkThemeForHero';
    export const isFeaturedFieldId: string = '3bfaed00-9aab-42c8-9fb7-8e8d1f0027a6';
    export const isFeaturedFieldName: string = 'Is Featured';
    export const promoBadgesFieldId: string = '23ee2ae2-1d4d-4e51-8e2a-b251e55e8a0d';
    export const promoBadgesFieldName: string = 'PromoBadges';
    export const seoFriendlyTitleFieldId: string = '260a336f-7541-4b8c-a023-3198ca421c86';
    export const seoFriendlyTitleFieldName: string = 'SeoFriendlyTitle';
    export const wordmarkFieldId: string = '1fc37bfb-e2f6-4337-b793-b12477c305bd';
    export const wordmarkFieldName: string = 'Wordmark';
    export const isDeletedFieldId: string = '2aaa5d2e-4301-40ad-b6f9-d752c5fad3e3';
    export const isDeletedFieldName: string = 'Is Deleted';
    export const pmiMembershipFieldId: string = '50845396-2667-4e2a-a548-93c81fc48043';
    export const pmiMembershipFieldName: string = 'PMI Membership';
    export const pmiSegmentFieldId: string = '3b0804fc-df69-4424-9d09-892341852d72';
    export const pmiSegmentFieldName: string = 'PMI Segment';
    export const abPricesFieldId: string = '97749ffe-4bd0-4190-abe1-c92c68de1397';
    export const abPricesFieldName: string = 'AB Prices';
    export const countryPricesFieldId: string = '202e0c1e-5500-46be-bb5d-14a400eb6130';
    export const countryPricesFieldName: string = 'Country Prices';
    export const globalPriceFieldId: string = '3a732fb5-16ff-448d-932a-143810b5f682';
    export const globalPriceFieldName: string = 'Global Price';
    export const groupPricesFieldId: string = 'da043375-9353-44e1-b45c-d4ed88459ebf';
    export const groupPricesFieldName: string = 'Group Prices';
    export const renewalPriceFieldId: string = 'b3f78b0d-bf55-4b73-b003-dbafdb00ccb3';
    export const renewalPriceFieldName: string = 'Renewal Price';
    export const zonePricesFieldId: string = 'babb6e53-3777-4916-a764-01aad64cce60';
    export const zonePricesFieldName: string = 'Zone Prices';
    export const brandNameFieldId: string = '8f6025b8-1039-46ab-a606-849bfe0c6292';
    export const brandNameFieldName: string = 'BrandName';
    export const divisionsFieldId: string = '0f949b65-ee6c-4d70-b692-54d801ba812c';
    export const divisionsFieldName: string = 'Divisions';
    export const fullDescriptionFieldId: string = '25fe6930-d1f7-4924-95f9-770aa320a1f9';
    export const fullDescriptionFieldName: string = 'Full Description';
    export const identificationFieldId: string = '89d22f6f-409f-4463-bb09-0a79f29b9707';
    export const identificationFieldName: string = 'Identification';
    export const manufacturerFieldId: string = 'ea45edf7-cd8b-433f-99a6-ebfe3c7ce3a5';
    export const manufacturerFieldName: string = 'Manufacturer';
    export const modelNameFieldId: string = '7b3fad93-dcb0-4d8d-8181-13738502bea5';
    export const modelNameFieldName: string = 'ModelName';
    export const nameFieldId: string = 'e2cea8d5-65f4-4789-a778-b1b400b56928';
    export const nameFieldName: string = 'Name';
    export const productClassesFieldId: string = '891039c9-fe24-46b6-a5de-ba958a6925cf';
    export const productClassesFieldName: string = 'ProductClasses';
    export const productTypeFieldId: string = 'd5806f22-e082-4c82-87bd-439a62ef92c6';
    export const productTypeFieldName: string = 'ProductType';
    export const shortDescriptionFieldId: string = '87cf462a-a37a-4731-bf0b-370d3d7a9873';
    export const shortDescriptionFieldName: string = 'Short Description';
    export const externalIDFieldId: string = 'f2f908f1-806e-4706-911b-6794b73576d0';
    export const externalIDFieldName: string = 'ExternalID';
    export const shortTitleFieldId: string = 'e27e197c-ad78-4352-bb04-cb7da291a88e';
    export const shortTitleFieldName: string = 'Short Title';
    export const titleFieldId: string = '56d15ac6-4a9b-4e75-830c-a30e63384011';
    export const titleFieldName: string = 'Title';
    export const iconFieldId: string = '734b447e-44ec-4739-8acc-b89fd51e1fec';
    export const iconFieldName: string = 'Icon';
    export const iconClassFieldId: string = '4796a76c-5867-4640-9dca-8bb7a17fb185';
    export const iconClassFieldName: string = 'IconClass';
    export const level2HierarchiesFieldId: string = '691c85ee-9f50-4dfd-b3af-a6c0d1d676c9';
    export const level2HierarchiesFieldName: string = 'level_2_hierarchy';
    export const level3HierarchiesFieldId: string = 'd3a4b7de-eafd-4b13-8df7-67c7220dbdda';
    export const level3HierarchiesFieldName: string = 'level_3_hierarchy';
    export const level4HierarchiesFieldId: string = 'b695c62a-0edd-4951-bd55-024699c1b8ad';
    export const level4HierarchiesFieldName: string = 'level_4_hierarchy';
  }
  export namespace WithCategoryTemplate {
    export const templateId: string = 'ebd414c1-b36d-4efe-b41f-6ef2643ad09b';
    export const templateName: string = '_With Category';
  }
  export namespace WithProductTemplate {
    export const templateId: string = '818c94a3-4023-4e2e-b93e-7a2be09eab9a';
    export const templateName: string = '_With Product';
  }

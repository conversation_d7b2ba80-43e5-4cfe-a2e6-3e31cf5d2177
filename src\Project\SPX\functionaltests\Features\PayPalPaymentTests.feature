﻿@local @browser:Chrome-Headless @desktop
Feature: PayPalPaymentTests
	Ensure user can purchase different products using PayPal Payment
	Background: 
	Given an empty cart

#Use New,Edit and Default keywords to add,edit or use default address for paypal payment.
#Use 'Save' and 'Not Save' keywords to decide the paypal payment to be saved for future use or not.

@Region2Store @Qa @Can
Scenario: Buy course using using tokenized PayPal payment
   
   Given a newly registered user from data scaffolding is logged in 
	When Add digital product to the cart via SKU's
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Proceed to checkout from cart
	 And Fill out billing address and save for future use
	 And Add PayPal payment method and save for future use
	Then Verify the order summary on the checkout page
	
	When Place Paypal order
	Then Verify order confirmation page
	When Empty the cart
	 And Add digital product to the cart via SKU's
	Then Verify products on the cart page
    When Proceed to checkout from cart
	 And Select saved payment details
	 And Place order on checkout page
	Then Verify order confirmation page

@Region2Store @Qa @Can
Scenario: Buy individual membership with chapter using PayPal

	Given a newly registered user from data scaffolding is logged in
	When Add digital product to the cart via SKU's
	 And Add membership to the cart
	 And Add chapter membership to the cart
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Proceed to checkout from cart
	 And Fill out billing address and save for future use
	 And Add PayPal payment method and save for future use
	Then Verify the order summary on the checkout page
	
	When Place Paypal order
	Then Verify order confirmation page  

@Region2Store @Qa @Can
Scenario: Buy individual membership with chapter using PayPal after cancelling the Paypal payment for first attempt
   
	Given a newly registered user from data scaffolding is logged in
	When Add digital product to the cart via SKU's
	 And Add membership to the cart
	 And Add chapter membership to the cart
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Proceed to checkout from cart
	 And Fill out billing address and save for future use
	 And Add PayPal payment method and save for future use
	Then Verify the order summary on the checkout page
	
	When Cancel Paypal order
	 And Add PayPal payment method and save for future use
	Then Verify the order summary on the checkout page
	
	When Place Paypal order
	Then Verify order confirmation page  

@Region2Store @Qa @Can
Scenario: Buy course with promo using Credit card after cancelling the PayPal payment for first attempt
   
	Given a newly registered user from data scaffolding is logged in 
	When Add digital product to the cart via SKU's
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Proceed to checkout from cart
	 And Fill out billing address and save for future use
	 And Add PayPal payment method and save for future use
	 And Apply promo code on the cart page
	Then Verify the order summary on the checkout page
	
	When Cancel Paypal order
	 And Update creditcard payment details and save for future use
	 And Place worldpay creditcard order
	Then Verify order confirmation page 

@Region2Store @Qa @Can
Scenario: Buy course using PayPal one time payment
   
    Given an eligible to pay certification user from data scaffolding is logged in
	When Add digital product to the cart via SKU's
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Proceed to checkout from cart
	 And Fill out billing address and not save for future use
	 And Add PayPal payment method and not save for future use
	Then Verify the order summary on the checkout page
	
	When Place Paypal order
	Then Verify order confirmation page  

@Region2Store @Qa @Can
Scenario: Buy course with promo using Credit card after cancelling the PayPal payment
   
	Given a newly registered user from data scaffolding is logged in
	When Add digital product to the cart via SKU's
	 And Add donation to the cart
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Proceed to checkout from cart
	 And Fill out billing address and not save for future use
	 And Add PayPal payment method and not save for future use
	 And Apply promo code on the cart page
	Then Verify the order summary on the checkout page
	
	When Cancel Paypal order
	 And Update creditcard payment details and not save for future use
	 And Place worldpay creditcard order
	Then Verify order confirmation page  

@Region2Store @Qa @Can
Scenario: Purchase subscription product using PayPal payment 
	
	Given a newly registered user from data scaffolding is logged in 
	When Add digital product to the cart via SKU's
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Proceed to checkout from cart
	 And Fill out billing address and save for future use
	 And Add PayPal payment method and save for future use
	Then Verify the order summary on the checkout page
	
	When Place Paypal order
	Then Verify order confirmation page
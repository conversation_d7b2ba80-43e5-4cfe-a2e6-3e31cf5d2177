﻿namespace Pmi.Spx.Foundation.Commerce.Models.Product
{
    using System.Collections.Generic;

    public class Product : Sitecore.Commerce.Entities.Products.Product
    {
        public float GlobalPrice { get; set; }
        public float AllGroupsPrice { get; set; }
        public string StoreCode { get; set; }
        public bool IsInStock { get; set; }
        public long Quantity { get; set; }
        public string Image { get; set; }
        public string SmallImage { get; set; }
        public string Thumbnail { get; set; }
        public string Description { get; set; }
        public long ProductAttributeTypeId { get; set; }
        public string ProductTypeId { get; set; }
        public Dictionary<string, object> CustomFields { get; set; } = new Dictionary<string, object>();
        public IEnumerable<CustomerPrice> CustomerPrices { get; set; } = new List<CustomerPrice>();
        public ProductStatus Status { get; set; }
        public IEnumerable<int> StoreIds { get; set; } = new List<int>();

        public string ProductBundleOptions { get; set; }
        public string ProductCustomOptions { get; set; }
        public string ZonePrices { get; set; }
        public string CountryPrices { get; set; }
        public float RenewalPrice { get; set; }
        public string ABPrices { get; set; }

        public Product ShallowClone()
        {
            return this.MemberwiseClone() as Product;
        }
    }
}
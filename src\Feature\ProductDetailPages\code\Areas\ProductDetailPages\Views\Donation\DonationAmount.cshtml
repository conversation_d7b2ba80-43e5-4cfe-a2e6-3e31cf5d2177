﻿@using Glass.Mapper.Sc.Web.Mvc
@using Pmi.Spx.Foundation.Framework.Services
@model Pmi.Spx.Feature.ProductDetailPages.Models.GenericViewModel
@{
    if (Model == null)
    {
        return;
    }
    var baseProduct = Model.Item;
    if (baseProduct == null)
    {
        return;
    }
}

<script src="@AssetMap.Instance.MapSpxAssetName("runtime.js")"></script>
<script src="@AssetMap.Instance.MapSpxAssetName("vendors.js")"></script>
<script src="@AssetMap.Instance.MapSpxAssetName("dsm-react.js")"></script>
<script src="@AssetMap.Instance.MapSpxAssetName("base-client.js")"></script>
<script defer src="@AssetMap.Instance.MapSpxAssetName("mvc-client.js")"></script>
<link href="@AssetMap.Instance.MapSpxAssetName("base-client.css")" rel="stylesheet" type="text/css" />

<div data-mvc-component-name="Product Donation" style="background:#f5f5f5;"></div>
@Html.EnsureStoreState()

﻿// ReSharper disable CollectionNeverUpdated.Local

#pragma warning disable 649

namespace Pmi.Spx.Project.Functional.Tests.Components
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using FluentAssertions;
    using OpenQA.Selenium;
    using OpenQA.Selenium.Support.UI;
    using Pmi.Spx.Project.Functional.Tests.Models;
    using Pmi.Web.Ui.Framework.Extensions;
    using Pmi.Web.Ui.Framework.Page;

    public class BillingAddressModal : PageComponent<BillingAddressModal>
    {
        private IWebElement AddressLine1 => Root.FindElement(By.XPath(".//label[@for='addressLine1']//following-sibling::input"));

        private IWebElement AddressLine2 => Root.FindElement(By.XPath(".//label[@for='addressLine2']//following-sibling::input"));

        private IWebElement City => Root.FindElement(By.XPath(".//label[@for='city']//following-sibling::input"));

        private IWebElement AutoSaveAddress => Root.FindElement(By.XPath($".//span[@class='address-auto-save-text']"));

        private IList<IWebElement> Country => Root.FindElements(By.XPath(".//select[@aria-label='Country / Region']/option"));

        private IWebElement CountryDropDown => Root.FindElement(By.XPath(".//select[@aria-label='Country / Region']/option[contains(text(),'Loading')]")); 

        private IWebElement SaveForFutureCheckbox => Root.FindElement(By.XPath($".//div[@class='mui-checkbox']//span[contains(@class,'mui-checkbox-component__icon')]"));

        private IList<IWebElement> SaveForFutureCheckboxSelected => Root.FindElements(By.XPath(".//div[@class='mui-checkbox']//span[contains(@class,'checked')]"));

        private IWebElement UseThisAddress => Root.FindElement(By.XPath(".//button[text()='Use this Address'] | .//button[text()='Use This Address']"));

        private IList<IWebElement> State => Root.FindElements(By.XPath(".//select[@aria-label='State / Province']/option"));

        private IWebElement ZipCode => Root.FindElement(By.XPath(".//label[@for='postalCode']//following-sibling::input"));

        private IWebElement EditAddress => Root.FindElement(By.XPath(".//div[@class='payment-billing__item-edit']/button"));

        private IWebElement AddNewAddress => Root.FindElement(By.XPath(".//button[text()='Add New Address']"));

        private IWebElement AutoSaveBillingAddressMessage => Root.FindElement(By.XPath(".//span[@class='address-auto-save-text']"));

        private readonly WebDriverWait _wait;

        private readonly UserSettings _userSettings;

        public BillingAddressModal(IPage page, IWebElement rootElement, TimeSpan? timeout) : base(page, rootElement, timeout)
        {
            _userSettings = new UserSettings();
            _wait = new WebDriverWait(page.WebDriver, _userSettings.DefaultExplicitWaitTimeout);
        }

        private bool AddressLine1Displayed() => Extensions.CatchUnavailableElement(() => AddressLine1.Displayed, false);

        private bool CountryDropDownDisplayed() => Extensions.CatchUnavailableElement(() => CountryDropDown.Displayed, false);

        private bool AutoSaveBillingAddressMessageDisplayed() => Extensions.CatchUnavailableElement(() => AutoSaveBillingAddressMessage.Displayed, false);

        public void FillInAddressValues(Address address, bool savePaymentForFutureUse, CartContext cartContext)
        {
            _wait.Until(_ => !CountryDropDownDisplayed() && AddressLine1Displayed());
            Country.SelectOptionByValue(address.Country);
            AddressLine1.EnterText(Page.WebDriver, address.AddressLine1);
            if(!string.IsNullOrEmpty(address.AddressLine2))
                AddressLine2.EnterText(Page.WebDriver, address.AddressLine2);
            ZipCode.EnterText(Page.WebDriver, address.ZipCode);
            City.EnterText(Page.WebDriver, address.City);
            State.SelectOptionByText(address.State);
            HandleAddressCheckboxes(savePaymentForFutureUse, cartContext);
            UseThisAddress.ScrollAndClick(Page.WebDriver);
        }

        public void HandleAddressCheckboxes(bool savePaymentForFutureUse, CartContext cartContext)
        {
            if(cartContext.ProductList.Any(d => d.Title.Contains(MembershipType.Membership.ToString()))
                && !cartContext.ProductList.Any(d => d.Sku?.Contains(ProductSKU.OldSingleMembership.GetEnumDisplayName()) ?? false)
                && !cartContext.ProductList.Any(d => d.Title.Contains(MembershipType.Student.ToString()))
                && !cartContext.ProductList.Any(d => d.Sku?.Contains(ProductSKU.Retiree.GetEnumDisplayName()) ?? false)
                || cartContext.ProductList.Any(d => d.Sku?.Contains("DP") ?? false))
            {
                if(!(cartContext.ProductList.Any(d => d.CartPrice.Currency.Equals(Currency.IndiaStore.Value)) && _userSettings.Environment.Equals("PROD") || _userSettings.Environment.Equals("CAN")))
                    AutoSaveAddress.Displayed.Should().BeTrue();
            }
            else
            {
                SaveForFutureCheckbox.Displayed.Should().BeTrue();
                if(savePaymentForFutureUse && !(SaveForFutureCheckboxSelected.Count > 0))
                    SaveForFutureCheckbox.ScrollAndClick(Page.WebDriver);
                else if(!savePaymentForFutureUse && SaveForFutureCheckboxSelected.Count > 0)
                    SaveForFutureCheckbox.ScrollAndClick(Page.WebDriver);
            }
        }

        private bool EditAddressDisplayed() => Extensions.CatchUnavailableElement(() => EditAddress.Displayed, false);

        private void OpenEditAddress()
        {
            _wait.Until(_ => EditAddressDisplayed());
            EditAddress.ScrollAndClickAndWait(Page.WebDriver);
            _wait.Until(_ => !EditAddressDisplayed());
        }

        public void EditBillingAddress(Address address, bool savePaymentForFutureUse, CartContext cartContext)
        {
            OpenEditAddress();
            FillInAddressValues(address, savePaymentForFutureUse, cartContext);
        }

        private bool AddNewAddressDisplayed() => Extensions.CatchUnavailableElement(() => AddNewAddress.Displayed, false);

        private void OpenNewAddress()
        {
            if(AddNewAddressDisplayed())
            {
                AddNewAddress.ScrollAndClickAndWait(Page.WebDriver);
                _wait.Until(_ => !AddNewAddressDisplayed());
            }
        }

        public void AddNewBillingAddress(Address address, bool savePaymentForFutureUse, CartContext cartContext)
        {
            OpenNewAddress();
            FillInAddressValues(address, savePaymentForFutureUse, cartContext);
        }

        public void SelectExistingBillingAddress()
        {
            UseThisAddress.ScrollAndClick(Page.WebDriver);
        }

        
        //Adyen ACH Payment Address Form

        private IList<IWebElement> AdyenACHCountry => Root.FindElements(By.XPath("//div[contains(@class, 'adyen-checkout__field--country')]//li"));

        private IWebElement AdyenACHAddressLine1 => Root.FindElement(By.XPath("//input[contains(@id,'adyen-checkout-street')]"));

        private IWebElement AdyenACHAddressLine2 => Root.FindElement(By.XPath("//input[contains(@id,'adyen-checkout-houseNumberOrName')]"));

        private IWebElement AdyenACHCity => Root.FindElement(By.XPath("//input[contains(@id,'adyen-checkout-city')]"));

        private IList<IWebElement> AdyenACHState => Root.FindElements(By.XPath("//div[contains(@class, 'adyen-checkout__field--stateOrProvince')]//li"));

        private IWebElement AdyenACHZipcode => Root.FindElement(By.XPath("//input[contains(@id,'adyen-checkout-postalCode')]"));


        private const string ACHDropdownXpath = ".//span[@class='adyen-checkout__dropdown__element__text']";

        public void SelectAdyenACHCountry(Address address)
        {
            AdyenACHCountry.First(countryElement => 
                countryElement.FindElement(By.XPath(ACHDropdownXpath)).Text.Trim() == address.FullCountryName)
                .ScrollAndClick(Page.WebDriver);
        }

        public void SelectAdyenACHState(Address address)
        {
            AdyenACHState.First(stateElement => 
                stateElement.FindElement(By.XPath(ACHDropdownXpath)).Text.Trim() == address.State)
                .ScrollAndClick(Page.WebDriver);
        }

        public void FillInACHPaymentAddressValues(Address address)
        {

            SelectAdyenACHCountry(address);
            AdyenACHAddressLine1.EnterText(Page.WebDriver, address.AddressLine1);
            if(!string.IsNullOrEmpty(address.AddressLine2))
                AdyenACHAddressLine2.EnterText(Page.WebDriver, address.AddressLine2);
            AdyenACHCity.EnterText(Page.WebDriver, address.City);
            SelectAdyenACHState(address);
            AdyenACHZipcode.EnterText(Page.WebDriver, address.ZipCode);

        }
    }
}
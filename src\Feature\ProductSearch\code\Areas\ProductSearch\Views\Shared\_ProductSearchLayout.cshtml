﻿@using Pmi.Spx.Feature.ProductSearch.Constants
@using Pmi.Spx.Foundation.Framework.Helpers
@using Pmi.Spx.Foundation.Framework.Services
@using Pmi.Spx.Foundation.Analytics.Constants
@model Pmi.Spx.Foundation.Connect.TemplateModels.IContent_Page
@{
    ViewBag.Title = Model.Short_Title;
    Layout = "~/Views/Shared/_Layout.cshtml";
    var isExperienceEditor = Sitecore.Context.PageMode.IsExperienceEditor;
}
@if (isExperienceEditor)
{
    <style type="text/css">
        div.scLooseFrameZone {
            overflow-y: unset;
        }
    </style>
}
<link href="@AssetMap.Instance.MapCssAssetName("main.min.css")?v=@AssemblyHelpers.BuildTime" rel="stylesheet"
      type="text/css" />
<link href="@AssetMap.Instance.MapCssAssetName("coveo.min.css")?v=@AssemblyHelpers.BuildTime" rel="stylesheet"
      type="text/css" />
<link href="@AssetMap.Instance.MapCssAssetName("productsearch.css")?v=@AssemblyHelpers.BuildTime" rel="stylesheet"
      type="text/css" />
<link href="@AssetMap.Instance.MapSpxAssetName("base-client.css")" rel="stylesheet" type="text/css" />
@Html.Partial(LogRocketConstants.LogRocketInitializerPartial)
<script src="@AssetMap.Instance.MapSpxAssetName("runtime.js")"></script>
<script src="@AssetMap.Instance.MapSpxAssetName("vendors.js")"></script>
<script src="@AssetMap.Instance.MapSpxAssetName("dsm-react.js")"></script>
<script src="@AssetMap.Instance.MapSpxAssetName("base-client.js")"></script>
<script defer src="@AssetMap.Instance.MapSpxAssetName("mvc-client.js")"></script>
<script>

</script>

<div class="dsm">
    <div class="container-fluid">
        <!-- Breadcrumbs -->
        <div class="breadcrumbs">
            @Html.Sitecore().Placeholder("breadcrumb")
        </div>
    </div>
</div>
<div id="spx-product-search" adoberegion="coveo-search" class="dsm product-search-container">
    <div class="container-fluid">
        <!-- Main Header -->
        <div class="row relative">
            <div class="col-12 col-md-8">
                @Html.Sitecore().Placeholder("title")
                <script type="text/javascript">
                    var lazy = document.getElementsByClassName('lazy-ad');

                    for (var i = 0; i < lazy.length; i++) {
                        lazy[i].src = lazy[i].getAttribute('data-src');
                    }
                </script>
            </div>
            <!-- Social share -->
            <div class="col-md-4 social-row">
                <div class="social-wrapper">
                    @Html.Sitecore().Placeholder("social")
                </div>
            </div>
        </div>
        <div class="row relative">
            <div class="col-12 col-lg-10">
                @Html.Sitecore().Placeholder("content-wrapper")
            </div>
            <div class="col-12 col-lg-2 hide-on-mobile">
                @Html.Sitecore().Placeholder("product-search-aside-right")
            </div>
        </div>
    </div>
</div>
<div>
    @Html.Sitecore().Placeholder("product-search-footer")
</div>
<script language="javascript" type="text/javascript">
    document.addEventListener('DOMContentLoaded', function () {
        Coveo.$$(document.body).on(Coveo.AnalyticsEvents.documentViewEvent, function (event, action) {
            if (typeof (adobeDataLayerAdd) !== 'undefined' && adobeDataLayerAdd !== null) {
                const encodedDocumentURL = encodeURI(action.documentViewEvent.documentUrl)
                const links = document.querySelectorAll('a.CoveoResultLink');
                for (let i = 0; i < links.length; i++) {
                    if (links[i].href == encodedDocumentURL) {
                        var title = links[i].getAttribute('data-linktitle');

                        links[i].addEventListener("click", function (clickEvent) {
                            const resultData = {
                                event: 'search-click',
                                eventInfo: 'search-click'
                            };
                            adobeDataLayerAdd(resultData);

                            const linkData = {
                                event: 'linkClick',
                                eventInfo: 'linkClick',
                                link: {
                                    linkTitle: title,
                                    linkModule: 'coveo-title',
                                    targetURL: action.documentViewEvent.documentUrl
                                },
                            };
                            adobeDataLayerAdd(linkData);
                        });
                    }
                }
            }
        });
    });
</script>
<script language="javascript" type="text/javascript">
    document.addEventListener('DOMContentLoaded', function () {
        Coveo.$$(document.body).on(Coveo.ResultListEvents.newResultDisplayed, function (event, action) {
			@* Added timeout because when event is fired system cannot find link buttons. *@
			setTimeout(() => {
				if (typeof (adobeDataLayerAdd) !== 'undefined' && adobeDataLayerAdd !== null) {
					const encodedDocumentURL = encodeURI(action.result.ClickUri)
					const links = document.querySelectorAll('a.CoveoResultLinkButton');
					for (let i = 0; i < links.length; i++) {
						if (links[i].href == encodedDocumentURL) {
							var title = links[i].getAttribute('data-linktitle');

							links[i].addEventListener("click", function (clickEvent) {
								const resultData = {
									event: 'search-click',
									eventInfo: 'search-click'
								};
								adobeDataLayerAdd(resultData);

								const linkData = {
									event: 'linkClick',
									eventInfo: 'linkClick',
									link: {
										linkTitle: title,
										linkModule: 'coveo-learn-more',
										targetURL: action.result.ClickUri
									},
								};
								adobeDataLayerAdd(linkData);
							});
						}
					}
				}
			}, 0);
        });
    });
</script>
<script language="javascript" type="text/javascript">
    document.addEventListener('DOMContentLoaded', function () {
        Coveo.$$(document.body).on(Coveo.AnalyticsEvents.changeAnalyticsCustomData, function (event, action) {
            if (!event || !action) return;

            if (typeof (adobeDataLayerAdd) !== 'undefined' && adobeDataLayerAdd !== null) {
                switch (action.actionCause) {
                    case "resultsSort":
                        handleResultsSortEvent();
                        break;
                    case "pagerNumber":
                    case "pagerPrevious":
                    case "pagerNext":
                        handlePagerNumberEvent();
                        break;
                    case "facetSelect":
                        handleFacetSelectEvent();
                        break;
                    default:
                        break;
                }
            }

            function handleResultsSortEvent() {
                const sortType = action.metaObject.resultsSortBy;

                const data = {
                    event: 'search-sort',
                    eventInfo: 'search-sort',
                    search: {
                        searchSort: 'Most Popular',
                    },
                };

                if (sortType.includes('pricedescending')) {
                    data.search.searchSort = 'Price Descending';
                } else if (sortType.includes('priceascending')) {
                    data.search.searchSort = 'Price Ascending';
                }

                adobeDataLayerAdd(data);
                handleLinkClickEvent(data.search.searchSort, 'coveo-sort-section');
            }

            function handlePagerNumberEvent() {
                const data = {
                    event: 'search-pagination',
                    eventInfo: 'search-pagination',
                    search: {
                        searchPagination: action.metaObject.pagerNumber.toString(),
                    },
                };

                adobeDataLayerAdd(data);
                handleLinkClickEvent(action.metaObject.pagerNumber.toString(), 'coveo-pager-list');
            }

            function handleFacetSelectEvent() {
                const data = {
                    event: 'search-filter',
                    eventInfo: 'search-filter',
                    search: {
                        searchFilter: `${action.metaObject.facetTitle}:${action.metaObject.facetValue}`,
                    },
                };

                adobeDataLayerAdd(data);
                handleLinkClickEvent(action.metaObject.facetValue, 'coveo-facet-values');
            }

            function handleLinkClickEvent(title, module) {
                const data = {
                    event: 'linkClick',
                    eventInfo: 'linkClick',
                    link: {
                        linkTitle: title,
                        linkModule: module,
                        targetURL: '',
                    },
                };

                adobeDataLayerAdd(data);
            }
        });
    });
</script>
<script language="javascript" type="text/javascript">
    document.addEventListener('DOMContentLoaded',
        function () {
            Coveo.$$(document.body).on(Coveo.AnalyticsEvents.searchEvent,
                function (event, action) {
                    if (!event || !action || action.searchEvents.length === 0) {
                        return;
                    }

                    if (typeof (adobeDataLayerAdd) !== 'undefined' && adobeDataLayerAdd !== null) {

                        const searchEvent = action.searchEvents[0];
                        if (searchEvent.actionCause != 'searchboxSubmit' &&
                            searchEvent.actionCause != 'searchFromLink' &&
                            searchEvent.actionCause != 'interfaceLoad' &&
                            searchEvent.actionCause != 'didyoumeanAutomatic' &&
                            searchEvent.actionCause != 'omniboxAnalytics' &&
                            searchEvent.actionCause != 'didyoumeanClick') {
                            return;
                        }
                        const spxStoreData = window.pmistore;
                        const screenData = setGenericPageData();
                        screenData.event = 'screenload';
                        screenData.eventInfo = 'screenload';

                        if (spxStoreData) {
                            screenData.pmistore = {
                                storecountry: spxStoreData?.country?.toUpperCase(),
                                storeid: spxStoreData?.storeid,
                                storecurrency: spxStoreData?.currency?.toUpperCase(),
                            }

                        }

                        if (searchEvent.actionCause != 'searchFromLink' && searchEvent.actionCause != 'interfaceLoad') {
                            //TODO: borrowed from DCP, should be integrated into the framework.
                            const loggedinEvent = window.adobeDataLayer.find(function (e) {
                                return e.event === "loggedin";
                            });
                            resetAdobeDataLayer();
                            if (loggedinEvent !== undefined) {
                                adobeDataLayerAdd(loggedinEvent);
                            }
                        }
                        else {
                            screenData.event = 'pageload'; screenData.eventInfo = 'pageload';
                        }

                        const filters = window.location.hash.split('&f:');
                        filters.shift();
                        const searchFilterValue = filters.join('|').replace(/([a-z])([A-Z])/g, '$1 $2').replace(/[\[\]']+/g, '').replaceAll('=', ':').replace('e Learning', 'eLearning');
                        const decodedSearchFilterValue = decodeURI(searchFilterValue);


                        let searchSortValue = 'Most Popular';
                        if (window.location.hash.includes('price%20descending')) {
                            searchSortValue = 'Price Descending';
                        } else if (window.location.hash.includes('price%20ascending')) {
                            searchSortValue = 'Price Ascending';
                        }

                        let actionName = 'search-success';
                        if (searchEvent.numberOfResults == 0) {
                            actionName = 'search-no-results';
                        }

                        const resultData = {
                            action: actionName,
                            event: 'search-load',
                            eventInfo: 'search-load',
                            search: {
                                searchTerm: searchEvent.queryText,
                                searchResults: searchEvent.numberOfResults.toString(),
                                searchFilter: decodedSearchFilterValue,
                                searchSort: searchSortValue,
                                searchType: 'store',
                                searchPagination: '1',
                            }
                        };

                        adobeDataLayerAdd(resultData);
                        adobeDataLayerAdd(screenData);
                    }
                });
        });
</script>
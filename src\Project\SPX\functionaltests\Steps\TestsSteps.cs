﻿namespace Pmi.Spx.Project.Functional.Tests.Steps
{
    using FluentAssertions;
    using OpenQA.Selenium;
    using OpenQA.Selenium.Support.UI;
    using Pmi.Spx.Project.Functional.Tests.Components;
    using Pmi.Spx.Project.Functional.Tests.Models;
    using Pmi.Spx.Project.Functional.Tests.Pages;
    using Pmi.Spx.Project.SPX.Functional.Tests.Hooks;
    using Pmi.Web.Ui.Framework;
    using Pmi.Web.Ui.Framework.Extensions;
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using TechTalk.SpecFlow;
    using static Pmi.Spx.Project.Functional.Tests.Currency;
    using Product = Models.Product;

    [Binding]
    public class TestsSteps
    {
        private readonly ScenarioContext _context;
        private CartContext _cartContext;
        private readonly StoreContext _storeContext;
        private readonly TestData _testData;
        private readonly UserSettings _userSettings;
        private readonly StudentInfo _studentInfo;
        private readonly ProductBuilder _productBuilder;
        private bool isUserCreated = false;


        public TestsSteps(ScenarioContext context, StoreContext storeContext, UserSettings userSettings, CartContext cartContext, TestData testData, StudentInfo studentInfo)
        {
            _context = context;
            _cartContext = cartContext;
            _storeContext = storeContext;
            _testData = testData;
            _userSettings = userSettings;
            _studentInfo = studentInfo;
            _productBuilder = new ProductBuilder(_testData, _storeContext);
        }

        private void AddSkuToTheCart(string sku)
        {
            _context.GoTo(_userSettings.BaseUrl, $"{_userSettings.CheckoutUrl}/cart?sku={sku}")
                .VerifyPage<CartPage>();
        }

        [When(@"the user attemps to add a chapter membership to the cart")]
        [When(@"Add chapter membership to the cart")]
        public void WhenAddChapterMembershipToTheCart()
        {
            var product = _productBuilder.GetChapterProduct();
            if(_cartContext.ProductList.Exists(p => p.Title.Equals(product.Title)))
                return;

            _context.GoTo(_userSettings.BaseUrl, $"{_userSettings.CheckoutUrl}/cart?chaptercode={_testData.ChapterCode}")
                .VerifyPage<CartPage>();

            _cartContext.ProductList.Add(product);
        }

        private void UpdateCartDetails()
        {
            if(!_userSettings.Environment.Equals("PROD"))
            {
                _cartContext.CartDetails = AdobeCommerce.GetCartDetails(_testData.User, _storeContext.Store);
            }
        }

        [Then(@"Verify products on the cart page")]
        public void ThenVerifyTheProductsInTheCart()
        {
            if(_storeContext.Store == Store.IndiaStore && _userSettings.Environment.Equals("PROD"))
            {
                _context.VerifyPage<CartPage>()
                    .VerifyCreditCardUsageWarning();
            }

            UpdateCartDetails();
            var promoDiscountPercent = _context.ScenarioInfo.Title.Contains("hundred percent promo") ? decimal.Parse("100") / 100 : decimal.Parse("25") / 100;
            _cartContext = _context.VerifyPage<CartPage>()
                .VerifyProducts(_cartContext, _storeContext.Currency, _testData.PricingType, promoDiscountPercent, false);
        }

        [When(@"Add custom donation to the cart")]
        public void AddCustomDonationToCart()
        {
            _context.VerifyPage<CartPage>()
                .AddCustomDonation(_testData.DonationAmount);
        }

        [Then(@"Verify more than maximum donation not allowed to add to cart")]
        public void VerifyEnsureMoreThanMaximumDonationNotAllowed()
        {
            _context.VerifyPage<CartPage>()
                .VerifyMaxDonationAmount();
        }

        [Then(@"Verify cart page components when product added to cart")]
        public void VerifyCartPageWithDonationProduct()
        {
            _context.VerifyPage<CartPage>()
                .VerifyCartPageComponents(_cartContext, _productBuilder);
        }

        private Product GetMembershipProductFromCart()
        {
            return _cartContext.ProductList.First(p => p.Title.Contains(Title.Membership));
        }

        [Then(@"Verify explore chapter membership option available in the cart")]
        public void ThenVerifyChapterMembershipOptionAvailableInTheCart()
        {
            _context.VerifyPage<CartPage>()
                .VerifyExploreChapterDisplayed();
        }

        [Then(@"Verify membership option available in the cart")]
        public void VerifyEnsureMembershipOptionAvailableInTheCart()
        {
            _context.VerifyPage<CartPage>()
                .VerifyMembershipOptionDisplayed();
        }

        [When(@"Remove membership and related product from the cart")]
        public void WhenRemoveTheMembershipProductFromTheCart()
        {
            var membershipProduct = GetMembershipProductFromCart();
            _context.VerifyPage<CartPage>()
                .DeleteAllChapterMembership(_cartContext)
                .RemoveProduct(_cartContext);
            _cartContext.ProductList.Remove(membershipProduct);
        }

        [When(@"Remove all chapter products from the cart")]
        public void WhenRemoveAllChapterProductsFromTheCart()
        {
            _context.VerifyPage<CartPage>().RemoveProductsFromCart(_cartContext, Title.Chapter);
        }

        [When(@"Add donation to the cart")]
        public void WhenAddDonationToTheCart()
        {
            var product = _context.VerifyPage<CartPage>()
                .AddDonation(_testData.DonationAmount);

            _context.VerifyPage<CartPage>();
            _cartContext.ProductList.Add(product);
        }

        [When(@"Try to add the donation to the cart")]
        public void WhenTryToAddDonationToTheCart()
        {
            _context.VerifyPage<CartPage>().AttemptToAddDonation(_testData.DonationAmount);
        }

        [When(@"Remove all cert products from the cart")]
        public void WhenRemoveAllCertProductsFromTheCart()
        {
            _context.VerifyPage<CartPage>().RemoveProductsFromCart(_cartContext, Title.Certification);
        }

        [When(@"Apply promo code on the cart page")]
        public void WhenApplyPromoCodeCartPage()
        {
            var promoCode = _testData.PromoCode;
            if(_context.WebDriver().Url.Contains(_context.InitPage<CartPage>().RelativePath))
                _context.VerifyPage<CartPage>().AddPromoCode(promoCode);
            else
                _context.VerifyPage<CheckoutPage>().AddPromoCode(promoCode);
            _cartContext.PromoCode = promoCode;
        }

        [Then(@"Validate promo tooltip mesage on the cart page")]
        public void ValidatePromoTooltipMessage()
        {
            _context.VerifyPage<CartPage>().ValidatePromoCodeTootltipMessage();
        }

        private Product AddDonationFromDetailPage(string amount)
        {
            NavigateToDonationDetailsPage();

            var product = _context.InitPage<DonationPage>()
                .AddDonation(amount);

            _context.WebDriver().LogCurrentUrl()
                .VerifyPage<CartPage>();

            return product;
        }

        private void NavigateToDonationDetailsPage()
        {
            _context.GoTo(_userSettings.BaseUrl, $"/{Store.GetShopPath(_storeContext.Store, _userSettings.Environment)}/p-/donation/pmi-educational-foundation-(pmief)-donation/101479")
                 .VerifyPage<DonationPage>();
        }

        [When(@"Add donation to the cart from product details")]
        public void WhenAddDonationToTheCartFromProductDetails()
        {
            _cartContext.ProductList.Add(AddDonationFromDetailPage(_testData.DonationAmount));
        }

        [When(@"Add course to the cart from product details page")]
        public void WhenAddCourseToTheCartFromProductDetails()
        {
            var product = _productBuilder.GetDigitalProduct();

            _context.GoTo(_userSettings.BaseUrl, "/store/products#sort=relevancy")
                .VerifyPage<ProductsPage>()
                    .Search(product.Title)
                    .ClickOnProductTitle()
                    .VerifyPage<ProductDetailsPage>()
                    .AddToCart()
                    .VerifyPage<CartPage>();

            _cartContext.ProductList.Add(product);
        }

        [When(@"Navigate to product details page")]
        public void NavigateToProductDetailsPage()
        {
            var product = _productBuilder.GetDigitalProduct();
            _context.GoTo(_userSettings.BaseUrl, "/store/products#sort=relevancy")
                .VerifyPage<ProductsPage>()
                    .Search(product.Title)
                    .ClickOnProductTitle()
                    .VerifyPage<ProductDetailsPage>();

        }

        [Then(@"Verify Already in Cart Message")]
        public void VerifyAlreadyInCartMessage()
        {
            _context.InitPage<ProductDetailsPage>().VerifyAlreadyInCart();
        }

        [Then(@"Verify Already in Cart Message Not Displayed")]
        public void VerifyAlreadyInCartMessageNotDisplayed()
        {
            _context.InitPage<ProductDetailsPage>().VerifyAlreadyInCartNotDisplayed();
        }

        [When(@"Navigate to Subscription PDP")]
        public void WhenNavigateToSubscriptionPDP()
        {
            var product = _productBuilder.GetDigitalProduct();
             _context.GoTo(_userSettings.BaseUrl, "/store/products#sort=relevancy")
                .VerifyPage<ProductsPage>()
                .Search(product.Title)
                .ClickOnProductTitle()
                    .VerifyPage<SubscriptionDetailPage>();
            
        }

        [Then(@"Verify Breadcrumbs should navigate to product search page")]
        public void VerifyBreadcrumbShouldNavigateToProductSearchPage()
        {
            _context.VerifyPage<ProductDetailsPage>()
                .VerifyProductTypeInBreadcrumb();
        }

        [Then(@"Verify You May Also Like in product details page")]
        public void VerifyingYouMayAlsoLikeInProductDetailsPage()
        {
            _context.VerifyPage<ProductDetailsPage>()
                .VerifyYouMayAlsoLike();
        }

        [Then(@"Verify Subscription status on the Subscription Detail Page")]
        public void ThenVerifySubscriptionStatusOnTheSubscriptionPDPPage()
        {
            _context.VerifyPage<SubscriptionDetailPage>()
                .SubscriptionStatusMessage()
                .AutoRenewMessage()
                .ViewInAccount();

        }

        [When(@"Navigate to MyPmi Subscription page")]
        public void WhenNavigateToSubscriptionMyPmiSubscriptionPage()
        {
            _context.GoTo(_userSettings.MyPMIBaseUrl, "/subscriptions")
                .VerifyPage<MyPmiSubscriptionPage>();

        }

        [Then(@"Verify Subscription product on MyPmi Subscription page")]
        public void ThenVerifySubscriptionProductOnMyPmiSubscriptionPage()
        {
            _context.VerifyPage<MyPmiSubscriptionPage>()
                .VerifySubscriptionProduct(_productBuilder);
             
        }

        private void AddMembershipFromDetailsPage(MembershipType membershipType, MembershipName membershipName)
        {
            _testData.User.Country = _storeContext.Country;

            _context.InitPage<HomePage>()
                .NavigateToMembershipThroughAdaptiveNavigation(membershipName.ToDescription())
                .VerifyPage<MembershipDetailPage>()
                .JoinMembership();

            if(membershipType.ToString().Contains("Student"))
                _context.VerifyPage<StudentPage>().AddStudentMembership(_testData.User);

            _context.VerifyPage<CartPage>();
        }

        private void AddMembershipFromPDP(MembershipType membershipType, string productTitle)
        {
            _testData.User.Country = _storeContext.Country;
            _context.GoTo(_userSettings.BaseUrl, "/store/products#sort=relevancy")
            .VerifyPage<ProductsPage>()
                   .Search(productTitle)
                   .ClickOnProductTitle()
                   .VerifyPage<ProductDetailsPage>();

            if(membershipType.ToString().Contains("Student"))
                _context.VerifyPage<StudentPage>().AddStudentMembershipFromPDP(_testData.User);
            _context.VerifyPage<CartPage>();
        }

        [When(@"Add membership to the cart from the details page")]
        public void WhenAddMembershipToTheCartFromTheDetailsPage()
        {
            AddMembershipFromDetailsPage(MembershipType.Individual, MembershipName.PMIMembership);

            var product = _productBuilder.GetMembershipProduct(MembershipType.Individual);
            _cartContext.ProductList.Add(product);
            if(_storeContext.Store != Store.ChinaStore)
                AddAutoChapterToProductListOnTheCartPage(_storeContext);
        }

        [When(@"Add membership to the cart from the PDP page")]
        public void WhenAddMembershipToTheCartFromPDPPage()
        {
            AddMembershipFromDetailsPage(MembershipType.Individual, MembershipName.PMIMembership);

            var product = _productBuilder.GetMembershipProduct(MembershipType.Individual);
            _cartContext.ProductList.Add(product);
            AddAutoChapterToProductListOnTheCartPage(_storeContext);
        }

        [When(@"Add student membership to the cart from the details page")]
        public void WhenAddStudentMembershipToTheCartFromTheDetailsPage()
        {

            _context.VerifyPage<MembershipDetailPage>()
                .AddToCart();

            _testData.User.Country = _storeContext.Country;
            _testData.User.Email = _context.Get<string>("EmailComponent");

            _context.InitPage<StudentPage>().AddStudentMembership(_testData.User)
                .VerifyPage<CartPage>();

            var product = _productBuilder.GetMembershipProduct(MembershipType.Student);
            _cartContext.ProductList.Add(product);
            AddAutoChapterToProductListOnTheCartPage(_storeContext);

        }

        [Given(@"an empty cart")]
        public void GivenAnEmptyCart()
        {
            if(string.IsNullOrEmpty(_testData.User?.Username) || string.IsNullOrEmpty(_testData.User?.Password) || _userSettings.Environment.Equals("PROD")) return;
            var itemIds = UserCartManagement.GetCartItemIds(_testData.User, _storeContext.Store);
            foreach(var itemId in itemIds.Reverse<string>())
            {
                UserCartManagement.RemoveItemIdFromCart(itemId, _storeContext.Store);
            }

            _cartContext.ProductList.Clear();
        }

        [Then(@"Try adding product to cart")]
        public void AddProductToCart()
        {
            _context.VerifyPage<ProductDetailsPage>()
                .AddToCart();
        }

        private void GoToPMIHome()
        {
            _context.GoTo(_userSettings.BaseUrl, HomePage.RelativeUrl)
                .VerifyPage<HomePage>();
        }

        [Given(@"On the PMI\.org store")]
        [When(@"Go to store")]
        public void GoToStore()
        {
            _context.GoTo(_userSettings.BaseUrl, "/store")
                .VerifyPage<StoreHomePage>();
        }

        private void NavigateToLoginPage()
        {
            var homePage = _context.InitPage<HomePage>();
            var loginPage = _context.InitPage<LoginPage>();
            if(loginPage.LoginButtonClickable()){ }
            else if(!homePage.LoginLinkDisplayed())
            {
                GoToPMIHome();
                homePage.Login();
            }
            _context.VerifyPage<LoginPage>();
        }

        private void Login()
        {
            _context.InitPage<LoginPage>()
                .GetComponent<IdpLogin>(IdpLogin.RootElement)
                .Login(_testData.User);
            Console.WriteLine($"User:{_testData.User.Username}/{_testData.User.Password}");
            _context.VerifyPage<HomePage>().SetUserDetails(_testData);
        }

        [Given(@"the user is logged in")]
        public void LoginWithUser()
        {
            NavigateToLoginPage();
            Login();
            _context["IsUserCreated"] = isUserCreated;
            string email;
            if(!isUserCreated)
            {
                email = _testData.User.Username.Contains("@") ? _testData.User.Username : _testData.User.Username + "@pmi.org";
            }
            else
            {
                email = _testData.User.Email;
            }

            _context["EmailComponent"] = email;
        }

        [Given(@"a newly registered user from data scaffolding is logged in")]
        public void GivenANewlyRegisteredUserFromDataScaffolding()
        {
            if(!_userSettings.Environment.Equals("PROD"))
            {
                GetNewlyRegisteredUserFromDataScaffolding();
                isUserCreated = true;
            }
            LoginWithUser();
        }

        [Given(@"the cart is empty")]
        [When(@"Empty the cart")]
        public void WhenEmptyTheCart()
        {
            var cartPage = _context.InitPage<CartPage>();
            if(!_context.WebDriver().Url.Contains(cartPage.RelativePath))
                _context.GoTo(_userSettings.BaseUrl, cartPage.RelativePath);

            _context.VerifyPage<CartPage>()
                .EmptyCart()
                .VerifyPageLoaded();

            _cartContext.ProductList.Clear();
        }

        [Then(@"Clear The Product List")]
        public void WhenClearTheProductList()
        {
            _cartContext.ProductList.Clear();
        }

        [Then(@"Verify empty cart page components")]
        public void ThenVerifyEmptyCartPageComponents()
        {
            _context.GoTo(_userSettings.BaseUrl, "/store/checkout/cart");

            var cartPage = _context.InitPage<CartPage>();
            
            _context.VerifyPage<CartPage>()
                .VerifyEmptyCartPageComponents(_testData, _storeContext);
        }

        [When(@"Add course to the cart")]
        public void WhenAddCourseToTheCart()
        {
            var product = _productBuilder.GetDigitalProduct();

            _context.GoTo(_userSettings.BaseUrl, "/store/products#sort=relevancy")
                .VerifyPage<ProductsPage>()
                .Search(product.Title)
                .LearnMore()
                .VerifyPage<ProductDetailsPage>()
                .AddToCart()
                .VerifyPage<CartPage>();

            _cartContext.ProductList.Add(product);
        }

        [When(@"Add digital product to the cart")]
        [When(@"Add subscription product to the cart")]
        [When(@"Add bundle product to the cart")]
        public void WhenAddDigitalOrBundleOrSubscriptionProductToTheCart()
        {
            var product = _productBuilder.GetDigitalProduct();
            AddSkuToTheCart(product.Sku);
            _cartContext.ProductList.Add(product);
        }

        [When(@"Navigate to order review page by clicking purchase quote button")]
        public void NavigateToOrderReviewPagebyClickingPurchaseQuoteButton()
        {
            var wait = new WebDriverWait(_context.WebDriver(), _userSettings.DefaultExplicitWaitTimeout);
            var url = $"{_userSettings.MyPMIBaseUrl.TrimEnd('/')}/account/orders";
            _context.GoTo(_userSettings.MyPMIBaseUrl, "/account/orders");
            wait.Until(d => d.Url.Equals(url));
            _context.VerifyPage<MyPmiOrderHistoryPage>()
                .WaitForOrderItemDisplayed()
                .PurchaseQuoteButtonClick()
                .VerifyPage<CheckoutPage>();
        }

        [When(@"Add (.*) certification to the cart")]
        public void AddCertificationToTheCart(string certType)
        {
            var product = _productBuilder.GetCertificationProduct();

            _context.GoTo(_userSettings.MyPMIBaseUrl, "/certification")
                .VerifyPage<MyPmiCertificationPage>();

            try
            {
                _context.VerifyPage<MyPmiCertificationPage>()
                    .CertPayForExam(product.Title)
                    .VerifyPage<CartPage>();

                if(!_context.VerifyPage<CartPage>().VerifyExpectedCertAddedToCart(product.Title))
                {
                    _context.VerifyPage<CartPage>().RemoveCertFromTheCart(product.Title);
                    throw new Exception("Expected certification not added to cart");
                }
            }
            catch
            {
                Console.WriteLine("Certification was added to cart using Sku");
                AddSkuToTheCart(product.Sku);
            }

            _cartContext.ProductList.Add(product);
        }

        [When(@"Add (.*) certification renewal to the cart")]
        public void AddCertificationRenewalToTheCart(string certType)
        {
            var product = _productBuilder.GetCertificationProduct();

            _context.GoTo(_userSettings.MyPMIBaseUrl, "/certification")
                .VerifyPage<MyPmiCertificationPage>();

            try
            {
                _context.VerifyPage<MyPmiCertificationPage>()
                    .CertRenewExam(product.Title)
                    .VerifyPage<CartPage>();
            }
            catch
            {
                AddSkuToTheCart(product.Sku);
            }

            _cartContext.ProductList.Add(product);
        }

        [When(@"Add (.*) certification retake to the cart")]
        public void AddCertificationRetakeToTheCart(string certType)
        {
            var product = _productBuilder.GetCertificationProduct();

            AddSkuToTheCart(product.Sku);

            _cartContext.ProductList.Add(product);
        }

        private void AddAutoChapterToProductListOnTheCartPage(StoreContext storeValue = null)
        {
            if(_context.InitPage<CartPage>().SingleMembershipDisplayed())
                return;

            ReloadIfPresumptiveChapterNotInCart();
            var chapterInCart = _context.InitPage<CartPage>().ChapterProductInCartDisplayed();
            if(!chapterInCart)
            {
                Console.WriteLine("Auto Chapter not added to the cart");
                if(!_userSettings.AssertPresumptiveClose)
                    return;

                throw new Exception("Auto Chapter not added to the cart");
            }

            if(!_userSettings.Environment.Equals("PROD"))
            {
                var cartDetails = AdobeCommerce.GetCartDetails(_testData.User, _storeContext.Store);
                var chapterSku = cartDetails.Items.Find(i => i.Name.Contains("Chapter")).Sku;
                var chapterProduct = _productBuilder.GetChapterProduct(chapterSku);

                _cartContext.ProductList.Add(chapterProduct);
            }
            else
            {
                AddAutoChapterMembershipManuallyInTheCart(_cartContext.ProductList);
            }
        }

        private void ReloadIfPresumptiveChapterNotInCart()
        {
            var cartPage = _context.InitPage<CartPage>();
            var chapterInCart = cartPage.ChapterProductInCartDisplayed();
            if(!chapterInCart)
            {
                _context.WebDriver().Navigate().Refresh();
                cartPage.VerifyPageLoaded();
            }
                
        }

        private void AddAutoChapterMembershipManuallyInTheCart(List<Product> productsInCart)
        {
            var product = _context.InitPage<CartPage>()
                .AddChapterMembershipManuallyToCart(productsInCart);

            _cartContext.ProductList.Add(product);
        }

        [When(@"Add membership to the cart")]
        public void WhenAddMembershipToTheCart()
        {
            _context.InitPage<CartPage>()
                .AddMembership(MembershipType.Individual)
                .VerifyPage<CartPage>();

            var product = _productBuilder.GetMembershipProduct(MembershipType.Individual);
            _cartContext.ProductList.Add(product);
            AddAutoChapterToProductListOnTheCartPage(_storeContext);
        }

        [When(@"Add membership renewal to the cart")]
        public void AddMembershipRenewalToTheCart()
        {
            AddMembershipFromDetailsPage(MembershipType.IndividualRenewal, MembershipName.PMIMembership);

            var product = _productBuilder.GetMembershipProduct(MembershipType.IndividualRenewal);
            _cartContext.ProductList.Add(product);
            AddAutoChapterToProductListOnTheCartPage(_storeContext);
        }

        [When(@"Add student membership renewal to the cart")]
        public void AddStudentMembershipRenewalOnNewCartPage()
        {
            _testData.User.Country = _storeContext.Country;
            _testData.User.Email = _context.Get<string>("EmailComponent");
            AddMembershipFromDetailsPage(MembershipType.StudentRenewal, MembershipName.StudentMembership);
            var product = _productBuilder.GetMembershipProduct(MembershipType.StudentRenewal);
            _cartContext.ProductList.Add(product);
            AddAutoChapterToProductListOnTheCartPage(_storeContext);
        }

        [When(@"Add student membership renewal to the cart from PDP")]
        public void AddStudentMembershipRenewalOnNewCartPagePDP()
        {
            _testData.User.Country = _storeContext.Country;
            _testData.User.Email = _context.Get<string>("EmailComponent");
            var productTitle = _productBuilder.GetMembershipProductTitle();
            AddMembershipFromPDP(MembershipType.StudentRenewal, productTitle);
            var product = _productBuilder.GetMembershipProduct(MembershipType.StudentRenewal);
            _cartContext.ProductList.Add(product);
            AddAutoChapterToProductListOnTheCartPage(_storeContext);
        }
        [When(@"Add retiree membership to the cart")]
        public void WhenAddRetireeMembershipToTheCart()
        {
            AddMembershipFromDetailsPage(MembershipType.Retiree, MembershipName.PMIMembership);

            var product = _productBuilder.GetMembershipProduct(MembershipType.Retiree);
            _cartContext.ProductList.Add(product);
        }

        [When(@"Add im sixty five membership to the cart")]
        public void WhenAddImSixtyFiveMembershipToTheCart()
        {
            AddMembershipFromDetailsPage(MembershipType.IM65, MembershipName.PMIMembership);

            var product = _productBuilder.GetMembershipProduct(MembershipType.IM65);
            _cartContext.ProductList.Add(product);
        }

        [When(@"Proceed to checkout from cart")]
        public void ThenCheckoutFromCart()
        {
            _context.InitPage<CartPage>()
                .ContinueToCheckout();
        }

        [When(@"Proceed to cart from checkout")]
        public void ThenProceedToCartFromCheckout()
        {
            _context.InitPage<CheckoutPage>()
                .ContinueToCart();
        }

        private Address GetNonIndiaAddress()
        {
            var nonIndiaAddress = _testData.Addresses.Where(a => a.Country != "IND").ToList();
            Random random = new Random();
            var index = random.Next(nonIndiaAddress.Count);
            return nonIndiaAddress[index];
        }

        private void HandleStoreSpecificPaymentSteps()
        {
            if(_storeContext.Store == Store.BrazilStore)
                _context.InitPage<CheckoutPage>()
                    .AddCpf(_testData.CpfNumber);
        }

        [When(@"Add PayPal payment method and (.*) for future use")]
        public void WhenAddPaypalPaymentDetailsInCheckout(string saveForFuture)
        {
            if(_userSettings.Environment.Equals("PROD"))
                return;

            _context.VerifyPage<CheckoutPage>()
                .EnterPayPalPaymentDetails(IsSaveForFutureUse(saveForFuture), _cartContext);
            HandleStoreSpecificPaymentSteps();
        }

        private void RegisterUser()
        {
            var username = UserService.GenerateUsername().Substring(0, 20);
            var password = SpxUserService.GenerateRandomPassword();

            _testData.User = new User
            {
                Username = username,
                Password = password,
                Email = username + "@pmi.org"
            };
            Console.WriteLine($"User:{_testData.User.Username}/{_testData.User.Password}");
            UserRegistration.RegisterUser(_testData.User, _testData.Country, _storeContext.RegistrationCountryID);
        }

        //Avoid using this step. Always get a user from data scaffolding.
        [Given(@"a newly registered user is logged in")]
        public void GivenANewlyRegisteredUser()
        {
            RegisterUser();
            UserProfile.UpdateAddress(_storeContext.Store, _testData.User,_studentInfo);
            isUserCreated = true;

            LoginWithUser();
        }

        [Given(@"a newly registered user without an Address is logged in")]
        public void GivenANewlyRegisteredUserWithoutAnAddress()
        {
            RegisterUser();
            isUserCreated = true;

            LoginWithUser();
        }

        private void PrintCookies()
        {
            foreach (var cookie in _context.WebDriver().Manage().Cookies.AllCookies)
                if (cookie.Name.Equals("sc_store"))
                    Console.WriteLine($"Name : {cookie.Name} / Value : {cookie.Value}");
        }

        [When(@"Go to the cart page")]
        public void GoToTheCart()
        {
            var homePage = _context.InitPage<HomePage>();
            var cartPage = _context.InitPage<CartPage>();

            if(_context.WebDriver().Url.EndsWith(cartPage.RelativePath))
                return;

            if(!homePage.CartLinkDisplayed())
                GoToPMIHome();

            if(homePage.CartLinkUrl.Contains(_userSettings.CheckoutUrl))
            {
                homePage.NavigateToCart();
            }
            else
            {
                _context.GoTo(_userSettings.BaseUrl, cartPage.RelativePath);
            }

            _context.WebDriver().LogCurrentUrl().VerifyPage<CartPage>();
            PrintCookies();
        }

        private decimal GetPromoDiscountPercent() => 
            _context.ScenarioInfo.Title.Contains("hundred percent promo") ? decimal.Parse("100") / 100 : decimal.Parse("25") / 100;

        [Then(@"Verify the order summary on the cart page")]
        public void ThenVerifyTheOrderSummaryInCartPage()
        {
            _context.VerifyPage<CartPage>();
            if(!_userSettings.Environment.Equals("PROD"))
                _cartContext.CartDetails = AdobeCommerce.GetCartSubtotalDetails(_testData.User, _storeContext.Store);

            if(!string.IsNullOrEmpty(_cartContext.PromoCode))   
            _cartContext.OtherCost.TotalDiscount = GetExpectedDiscount();

            _context.VerifyPage<CartPage>()
                .ValidateOrderSummary(_cartContext, _storeContext, _testData.User);
        }

        private Amount GetExpectedDiscount()
        {
            var promoDiscount = GetPromoDiscountPercent();

            var nonDonationProducts = _cartContext.ProductList
                .Where(product => !product.Title.Contains("Donation"))
                .ToList();
            var discountableTotal = (from product in nonDonationProducts
                                     select product).Sum(product => product.CartPrice.Value);

            //calculate the expected discount for the cart. For testing 25% discount promo code is applied
            var discount = Math.Round((decimal)(discountableTotal * promoDiscount), 2, MidpointRounding.AwayFromZero);

            return new Amount(_storeContext.Currency, discount);
        }

        [Then(@"Verify the order summary on the checkout page")]
        public void ThenVerifyTheOrderSummaryInCheckoutPage()
        {
            _context.VerifyPage<CheckoutPage>();
            _cartContext.CartDetails = AdobeCommerce.GetCartSubtotalDetails(_testData.User, _storeContext.Store);
            _context.VerifyPage<CheckoutPage>()
                .VerifySummary(ref _cartContext, _storeContext, _testData.User, GetPromoDiscountPercent());
        }

       [Then(@"Verify Adyen Price After Voucher Discount on the checkout page")]
        public void ThenVerifyAdyenPriceAfterVoucherDiscountInCheckoutPage()
        {
                _context.VerifyPage<CheckoutPage>()
            .VerifyAdyenPriceAfterVoucherDiscount(_cartContext.OtherCost.TotalCost);
        }

        [Then(@"Verify the order summary on the Quote page")]
        public void ThenVerifyTheOrderSummaryonQuotePage()
        {
            _context.VerifyPage<QuotePage>();
            _cartContext.CartDetails = AdobeCommerce.GetCartSubtotalDetails(_testData.User, _storeContext.Store);
            _context.VerifyPage<QuotePage>()
                .VerifySummary(ref _cartContext, _storeContext, _testData.User, GetPromoDiscountPercent());
        }

        [Then(@"Verify membership option is not present")]
        public void VerifyEnsureMembershipOptionIsNotPresent()
        {
            _context.InitPage<CartPage>()
                .WaitUntilMembershipOptionNotPresent();
        }

        [Then(@"Ensure membership notification for China user in payment")]
        public void EnsureMembershipNotificationForChinaUserInPayment()
        {
            _context.VerifyPage<CheckoutPage>()
                .GetComponent<MembershipNotification>(MembershipNotification.RootElement)
                .VerifyMembershipNotification();
        }

        [Then(@"Ensure cart is empty")]
        public void ThenEnsureCartIsEmpty()
        {
            _context.VerifyPage<CartPage>()
                .WaitUntilEmptyCart();
        }

        [Then(@"Ensure user should not be allowed to add the chapter product to the cart")]
        public void ThenVerifyTheValidationErrorInCart()
        {
            var newCartPage = _context.VerifyPage<CartPage>();

            newCartPage.VerifyChapterErrorNotification();

            newCartPage.IsCartEmpty().Should().BeTrue();
        }

        [Then(@"Verify user should not be allowed to add membership product to the cart")]
        public void VerifyTheValidationError()
        {
            var cartPage = _context.VerifyPage<CartPage>();
            cartPage.VerifyErrorNotification();

        }

        [Then(@"Remove chapter membership from the cart list")]
        public void ThenRemoveFromTheCartList()
        {
            var product = _cartContext.ProductList.First(p => p.Title.Contains(Title.Chapter));
            _cartContext.ProductList.Remove(product);
        }

        [When(@"Refresh the page")]
        public void WhenRefreshThePage()
        {
            _context.WebDriver().Navigate().Refresh();
        }

        [When(@"Add online course to the cart")]
        public void WhenAddOnlineCourseToTheCart()
        {
            var product = _context.GoTo(_userSettings.BaseUrl, "/learning/online-courses")
                .VerifyPage<OnlineCoursePage>()
                .AddOnlineCourseToCart(_productBuilder);

            _cartContext.ProductList.Add(product);

            _context.VerifyPage<ProductDetailsPage>()
                .AddToCart()
                .VerifyPage<CartPage>();
        }

        [When(@"Add student membership to the cart")]
        public void WhenAddStudentMembershipToTheCart()
        {
            _testData.User.Country = _storeContext.Country;
            _context.InitPage<CartPage>()
                  .AddMembership(MembershipType.Student, _testData.User)
                  .VerifyPage<CartPage>();

            var product = _productBuilder.GetMembershipProduct(MembershipType.Student);
            _cartContext.ProductList.Add(product);
            AddChapterMembershipOnCartToProductList(_cartContext.ProductList);
        }

        private CertificationTypes GetCertName()
        {
            foreach(CertificationTypes cert in Enum.GetValues(typeof(CertificationTypes)))
            {
                if(_context.ScenarioInfo.Title.Contains(cert.ToString()))
                {
                    return cert;
                }
            }
            throw new Exception("Test Case doesn't have certification type in the scenario title");
        }

        public void GetEligibleToPayCertificationUsers(CertificationTypes certificationType)
        {
            switch(certificationType)
            {
                case CertificationTypes.PMP:
                {
                    _testData.User = DataScaffolding.PullPMPExamUser(_userSettings.Environment, _testData.Country);
                    Console.WriteLine($"User:{_testData.User.Username}/{_testData.User.Password}");
                    break;
                }
                case CertificationTypes.CAPM:
                {
                    _testData.User = DataScaffolding.PullCAPMExamUser(_userSettings.Environment, _testData.Country);
                    Console.WriteLine($"User:{_testData.User.Username}/{_testData.User.Password}");
                    break;
                }
                case CertificationTypes.PgMP:
                {
                    _testData.User = DataScaffolding.PullPgMPExamUser(_userSettings.Environment, _testData.Country);
                    Console.WriteLine($"User:{_testData.User.Username}/{_testData.User.Password}");
                    break;
                }
                case CertificationTypes.ACP:
                {
                    _testData.User = DataScaffolding.PullACPExamUser(_userSettings.Environment, _testData.Country);
                    Console.WriteLine($"User:{_testData.User.Username}/{_testData.User.Password}");
                    break;
                }
                case CertificationTypes.PBA:
                {
                    _testData.User = DataScaffolding.PullPBAExamUser(_userSettings.Environment, _testData.Country);
                    Console.WriteLine($"User:{_testData.User.Username}/{_testData.User.Password}");
                    break;
                }
                case CertificationTypes.RMP:
                {
                    _testData.User = DataScaffolding.PullRMPExamUser(_userSettings.Environment, _testData.Country);
                    Console.WriteLine($"User:{_testData.User.Username}/{_testData.User.Password}");
                    break;
                }
                case CertificationTypes.PfMP:
                {
                    _testData.User = DataScaffolding.PullPfMPExamUser(_userSettings.Environment, _testData.Country);
                    Console.WriteLine($"User:{_testData.User.Username}/{_testData.User.Password}");
                    break;
                }
                case CertificationTypes.SP:
                {
                    _testData.User = DataScaffolding.PullSPExamUser(_userSettings.Environment, _testData.Country);
                    Console.WriteLine($"User:{_testData.User.Username}/{_testData.User.Password}");
                    break;
                }
            }
        }

        [Given(@"an eligible to pay certification user from data scaffolding is logged in")]
        public void GivenAnEligibleToPayCertificationUserFromDataScaffolding()
        {
            Random random = new Random();

            if(_userSettings.Environment.Equals("PROD"))
                return;
            var v = Enum.GetValues(typeof(CertificationTypes));
            var certType = v.GetValue(random.Next(v.Length));
            isUserCreated = true;
            GetEligibleToPayCertificationUsers((CertificationTypes)certType);

            LoginWithUser();
        }

        private void GetNewlyRegisteredUserFromDataScaffolding()
        {
            if(_userSettings.Environment.Equals("PROD"))
                return;
            try
            {
                _testData.User = DataScaffolding.PullRegisteredUser(_userSettings.Environment, _testData.Country);
                Console.WriteLine($"User:{_testData.User.Username}/{_testData.User.Password} Email:{_testData.User.Email}");
            }
            catch
            {
                try
                {
                    _testData.User = DataScaffolding.PullPMPCertSubmittedUser(_userSettings.Environment, _testData.Country);
                    Console.WriteLine($"User:{_testData.User.Username}/{_testData.User.Password}");
                }
                catch
                {
                    _testData.User = DataScaffolding.PullPMPCertifiedUser(_userSettings.Environment, _testData.Country);
                    Console.WriteLine($"User:{_testData.User.Username}/{_testData.User.Password}");
                }
                
            }
        }

        [Given(@"a new member user from data scaffolding is logged in")]
        public void GivenANewMemberUserFromDataScaffolding()
        {
            if(_userSettings.Environment.Equals("PROD"))
                return;
            _testData.User = DataScaffolding.PullMemberUser(_userSettings.Environment, _testData.Country);
            Console.WriteLine($"User:{_testData.User.Username}/{_testData.User.Password}");
            _testData.PricingType = PricingType.Member;
            
            LoginWithUser();
        }

        [Given(@"Get eligible to pay PMP certification user")]
        public void GetEligibleToPayPMPUsers()
        {
            if(_userSettings.Environment.Equals("PROD"))
                return;
            GetEligibleToPayCertificationUsers(CertificationTypes.PMP);
        }

        /// <summary>
        /// 
        /// </summary>
        [Then(@"Validate order details on MyPMI page")]
        public void ValidatePrintInvoiceButton()
        {
            /*_context.VerifyPage<ConfirmationPage>()
                .ValidateMyPMIOrderDetailsPage(_cartContext,_testData)
                .ValidateMyPMIPage()
                .ValidateCustomerCarePage();*/
        }

        [Then(@"Verify order details on MyPMI page")]
        public void ThenVerifyOrderDetailsOnMyPMIPage()
        {
            _context.VerifyPage<OrderConfirmationPage>()
                .ValidateMyPMIOrderDetailsPage(_cartContext, _testData);
        }

        [When(@"Add Single membership to the cart")]
        public void WhenAddSingleMembershipToTheCart()
        {
            AddSkuToTheCart(_testData.MembershipSku);
            var product = _productBuilder.GetMembershipProduct(MembershipType.Individual);
            _context.VerifyPage<CartPage>().ValidateSingleMembershipProduct();
            _cartContext.ProductList.Add(product);
        }

        [When(@"Add Single membership to the cart and change chapter on US store")]
        public void WhenAddSingleMembershipToTheCartAndChangeChapter()
        {
            AddSkuToTheCart(_testData.MembershipSku);
            var product = _productBuilder.GetMembershipProduct(MembershipType.Individual);
            _context.VerifyPage<CartPage>().ValidateSingleMembershipProduct();
            _cartContext.ProductList.Add(product);
            AddChapterFromPresumptiveChaptersList();
        }

        [When(@"Add membership to the cart via SKU's")]
        public void WhenAddMembershipViaSkuToTheCart()
        {
            AddSkuToTheCart(_testData.MembershipSku);
            var product = _productBuilder.GetMembershipProduct(MembershipType.Individual);
            _cartContext.ProductList.Add(product);
            AddChapterMembershipOnCartToProductList(_cartContext.ProductList);
        }

        [When(@"Add subscription product to the cart via SKUs")]
        [When(@"Add digital product to the cart via SKU's")]
        public void WhenAddDigitalToTheCart()
        {
            var product = _productBuilder.GetDigitalProduct();
            AddSkuToTheCart(product.Sku);
            _cartContext.ProductList.Add(product);
        }

        [When(@"Add Single membership to the cart and change the chapter")]
        public void WhenAddSingleMembershipToTheCartWithNewChapter()
        {
            _context.InitPage<CartPage>()
                .AddMembership(MembershipType.Individual)
                .VerifyPage<CartPage>();

            var product = _productBuilder.GetMembershipProduct(MembershipType.Individual);
            _cartContext.ProductList.Add(product);

            AddChapterFromPresumptiveChaptersList();
        }

        [Then(@"Ensure user should not be allowed to add donation product from detail page")]
        public void ThenEnsureUserShouldNotBeAllowedToAddDonationProductFromDetailPage()
        {
            NavigateToDonationDetailsPage();

            _context.InitPage<DonationPage>().VerifyDonationButtonDisabled();

            GoToTheCart();
        }

        [Then(@"Verify donation component is not available")]
        public void VerifyDonationProductIsNotPresent()
        {
            _context.InitPage<CartPage>()
               .VerifyDonationFieldNotPresent();
        }

        [When(@"Click on request a quote link")]
        public void WhenRequestAQuote()
        {
            _context.VerifyPage<CartPage>().ClickGenerateAQuote();
            _context.InitPage<CheckoutPage>().VerifyQuotePageLoaded();
        }

        [Then(@"Validate single membership on the cart")]
        public void ThenValidateSingleMembership()
        {
            _context.VerifyPage<CartPage>().ClickGenerateAQuote();
            _context.VerifyPage<CheckoutPage>();
        }

        [Then(@"Verify order summary on checkout page")]
        [Then(@"Verify products on order summary in checkout page")]
        public void VerifyOrderSummaryOnCheckoutpage()
        {
            _context.VerifyPage<CheckoutPage>().ValidateProductsInOrderSummary(_cartContext);
        }

        [When(@"Fill out contact information")]
        public void FillOutContactInformation()
        {
            var email = _context.Get<string>("EmailComponent");
            _context.VerifyPage<CheckoutPage>().EnterContactInformation(email, _storeContext, _context, _testData);
        }

        [When(@"(.*) billing address and (.*) for future use")]
        public void FillOutBillingInformation(string addressType, string saveForFutureUse = "not save")
        {
            _context.VerifyPage<CheckoutPage>()
                .EnterBillingAddress(_testData.GetAddressForCountry(), IsSaveForFutureUse(saveForFutureUse), _cartContext, GetAddressType(addressType));
        }

        [When(@"Select existing billing address on checkout page")]
        public void ThenSelectExistingBillingAddressOnCheckoutPage()
        {
            _context.VerifyPage<CheckoutPage>()
                .SelectExistingBillingAddress();
        }

        [When(@"(.*) invalid address and (.*) for future use")]
        public void AddInvalidBillingAddress(string addressType, string saveForFutureUse = "not save")
        {
            var scenarioAddress = _testData.GetAddressForCountry();
            var invalidAddress = _testData.GetRandomCountryAddress();

            invalidAddress.Country = scenarioAddress.Country;
            invalidAddress.AddressLine1 = scenarioAddress.AddressLine1;
            invalidAddress.State = scenarioAddress.State;

            _context.VerifyPage<CheckoutPage>()
                .EnterBillingAddress(invalidAddress, IsSaveForFutureUse(saveForFutureUse), _cartContext, GetAddressType(addressType));
        }

        [Then(@"Validate the error message on billing address")]
        public void ThenValidateTheErrorMessageOnBillingAddress()
        {
            _context.VerifyPage<CheckoutPage>()
                .ValidateErrorMessageOnBillingAddress();
        }

        [Then(@"Verify invalid tax error message on billing address")]
        public void ThenVerifyInvalidTaxErrorMessageOnBillingAddress()
        {
            _context.VerifyPage<CheckoutPage>()
                .ValidateInvalidTaxErrorMessageOnBillingAddress();
        }

        private bool IsSaveForFutureUse(string saveForFuture) => !saveForFuture.Contains("not");

        [When(@"Fill out contact information on quote page")]
        public void UpdateContactInformationOnQuotePage()
        {
            var email = _context.Get<string>("EmailComponent");
            _context.VerifyPage<QuotePage>()
                .EnterContactInformation(email, _storeContext);
        }

        private AddressType GetAddressType(string typeText)
        {
            if(typeText.Contains("Add"))
                return AddressType.Add;

            if(typeText.Contains("Edit"))
                return AddressType.Edit;

            if(typeText.Contains("Fill out"))
                return AddressType.First;

            throw new Exception($"Unknown address type for text '{typeText}'");
        }

        [When(@"(.*) billing address on quote page and (.*) for future use")]
        public void UpdateBillingAddressOnQuotePage(string addressType, string saveForFuture)
        {
            _context.VerifyPage<QuotePage>()
                .EnterBillingAddress(_testData.GetAddressForCountry(), IsSaveForFutureUse(saveForFuture), _cartContext, GetAddressType(addressType));
        }

        [Then(@"Verify legal statement block display with payment")]
        [Then(@"Verify the payment methods")]
        public void ThenVerifyLegalStatementBlockDisplayWithPayment()
        {
            _context.InitPage<CheckoutPage>()
                .VerifyPageLoaded()
                .VerifyPaymentMethods(_cartContext);
        }

        [When(@"Place Adyen Google Pay order")]
        public void WhenPlaceAdyenGooglePayOrder()
        {
            _context.InitPage<CheckoutPage>()
                .VerifyPageLoaded()
                .AdyenGooglePay();

            _context.InitPage<AdyenGooglePay>()
                .PlaceAdyenGooglePayOrder(_testData);
        }

        [When(@"Place Adyen Klarna order")]
        public void WhenPlaceAdyenKlarnaOrder()
        {
            _context.InitPage<CheckoutPage>()
                .VerifyPageLoaded()
                .PlaceKlarnaOrder(KlarnaPhoneNumbers.Dispute.GetEnumDisplayName());
        }

        [When(@"Place Invalid Adyen Klarna order")]
        public void WhenPlaceInvalidAdyenKlarnaOrder()
        {
            _context.InitPage<CheckoutPage>()
                .VerifyPageLoaded()
                .PlaceInvalidKlarnaOrder(KlarnaPhoneNumbers.Denied.GetEnumDisplayName());
        }

        [When(@"Place Adyen AfterPay order")]
        public void WhenPlaceAdyenAfterPayOrder()
        {
            _context.InitPage<CheckoutPage>()
                .VerifyPageLoaded()
                .PlaceAfterPayOrder(_testData.User);
        }

        [When(@"Place Adyen CashApp Pay order")]
        public void WhenPlaceAdyenCashAppPayOrder()
        {
            _context.InitPage<CheckoutPage>()
                .VerifyPageLoaded()
                .PlaceCashAppPayOrder();             
        }

        [When(@"Place Adyen WeChat Pay order")]
        public void WhenPlaceAdyenWeChatPayOrder()
        {
            _context.InitPage<CheckoutPage>()
                .VerifyPageLoaded()
                .PlaceWeChatOrder();
        }

        [When(@"Place Tokenized Adyen CashApp Pay order")]
        public void WhenPlaceTokenizedAdyenCashAppPayOrder()
        {
            _context.InitPage<CheckoutPage>()
                .VerifyPageLoaded()
                .PayTokenizedCashAppPayPayment();
        }

        [When(@"Update creditcard payment details and (.*) for future use")]
        public void UpdateCreditCardPayment(string saveForFutureUse)
        {
            _context.VerifyPage<CheckoutPage>().EnterCreditCardPaymentDetails(_testData.PaymentInfo, IsSaveForFutureUse(saveForFutureUse), _cartContext);
            HandleStoreSpecificPaymentSteps();
        }

        [When(@"Edit creditcard payment details and (.*) for future use")]
        public void EditCreditCardPayment(string saveForFutureUse)
        {

            _context.VerifyPage<CheckoutPage>().EditCreditCardPaymentDetails(_testData.PaymentInfo, IsSaveForFutureUse(saveForFutureUse), _cartContext);
            HandleStoreSpecificPaymentSteps();
        }

        [When(@"Edit creditcard payment details with new creditcard and (.*) for future use")]
        public void EditCreditCardPaymentWithNewCreditCard(string saveForFutureUse)
        {
            var newCreditCard = _testData.GetRandomCreditCard(_testData);
            var paymentName = newCreditCard.CardName;
            _testData.SetPaymentInfoDetails(paymentName);
            _context.VerifyPage<CheckoutPage>().EditCreditCardPaymentDetailsWithNewCreditCard(_testData.PaymentInfo, IsSaveForFutureUse(saveForFutureUse), _cartContext);
            HandleStoreSpecificPaymentSteps();
        }

        [When(@"Update Alipay payment details")]
        public void UpdateAliPayPayment()
        {
            _context.VerifyPage<CheckoutPage>().EnterAliPayPaymentDetails();
        }

        [When(@"Input Alipay payment details")]
        public void UpdateAliPayPaymentDetail()
        {
            _context.VerifyPage<CheckoutPage>().OpenAdyenAliPaySimulator();
        }

        [When(@"Select saved payment details")]
        public void SavedPayments()
        {
            _context.VerifyPage<CheckoutPage>().SavedWorldpayPayment();
            HandleStoreSpecificPaymentSteps();
        }

        [When(@"Place Paypal order")]
        public void PlacePayPalOrder()
        {
            _context.VerifyPage<CheckoutPage>().PlacePayPalOrder();
        }

        [When(@"Cancel Paypal order")]
        public void CancelPayPalOrder()
        {
            _context.VerifyPage<CheckoutPage>().CancelPayPalOrder();
        }

        [When(@"Place AliPay order")]
        public void PlaceAliPayOrder()
        {
            _context.VerifyPage<CheckoutPage>().PlaceAliPayOrder();
        }

        [When(@"Cancel AliPay order")]
        public void CancelAliPayOrder()
        {
            _context.VerifyPage<CheckoutPage>().CancelAliPayOrder();
        }

        [When(@"Cancel Adyen AliPay order")]
        public void CancelAdyenAliPayOrder()
        {
            _context.VerifyPage<AdyenAliPayPage>().RefuseAdyenAliPayPayment();
        }

        [When(@"Place Pending Open AliPay order")]
        public void PlacePendingOpenAliPayOrder()
        {
            _context.VerifyPage<CheckoutPage>().PlacePendingOpenAliPayOrder();
        }

        [When(@"Place worldpay creditcard order")]
        public void PlaceWorldPayCreditCardOrder()
        {
            _context.VerifyPage<CheckoutPage>().PlaceOrder();
        }

        [Then(@"Verify Place Order button is disabled")]
        public void DisableWorldPayCreditCardOrder()
        {
            _context.VerifyPage<CheckoutPage>().PlaceOrderIsDisabled();
        }

        [Then(@"Verify Adyen Pay button is disabled")]
        public void VerifyAdyenPayButtonIsDisabled()
        {
            _context.VerifyPage<CheckoutPage>().AdyenCreditCardPayButtonIsDisabled();
        }

        [When(@"Place quote order")]
        public void PlaceQuoteOrder()
        {
            _context.VerifyPage<QuotePage>().PlaceQuoteOrder();
        }

        [Then(@"Verify order confirmation page")]
        public void VerifyOrderConfirmationPage()
        {
            _context.VerifyPage<OrderConfirmationPage>()
                .VerifyOrderReceipt(_cartContext, _storeContext, GetPromoDiscountPercent())
                .VerifyEmailAddress(_testData.User.Email)
                .VerifyPaymentMethod(_testData.PaymentInfo)
                .VerifyViewInvoiceLink(_testData.PaymentInfo.PaymentType)
                .VerifyProductConfirmationBlock(_cartContext, _storeContext);
        }

        [Then(@"Verify pending order confirmation page")]
        public void VerifyPendingOrderConfirmationPage()
        {
            _context.VerifyPage<OrderConfirmationPage>()
                .VerifyOrderReceipt(_cartContext, _storeContext, GetPromoDiscountPercent())
                .VerifyPendingOrderStatus()
                .VerifyPaymentMethod(_testData.PaymentInfo)
                .VerifyProductConfirmationBlock(_cartContext, _storeContext);
        }

        [When(@"Apply voucher on the cart page")]
        public void ApplyVoucherOnCartPage()
        {
            _context.VerifyPage<CheckoutPage>()
                .AddVoucher(_testData.VoucherCode);
            _cartContext.VoucherCode = _testData.VoucherCode;
        }

        private void AddChapterMembershipOnCartToProductList(List<Product> productsInCart)
        {
            var product = _context.InitPage<CartPage>()
                .AddChapterMembershipManuallyToCart(productsInCart);
            if(product.Title != null)
                _cartContext.ProductList.Add(product);
        }

        [When(@"Click edit cart button on checkout page")]
        public void ClickEditCartButtonOnCheckoutPage()
        {
            _context.VerifyPage<CheckoutPage>().ClickEditCartButton();
        }

        [When(@"Add chapter from presumptive chapter list")]
        public void AddChapterFromPresumptiveChaptersList()
        {
            _context.InitPage<CartPage>().AddChapterFromPresumptiveChaptersList(_storeContext);
            var chaptersCount = _context.InitPage<CartPage>().AllChapterProductsInCart.Count;
            if(!_userSettings.Environment.Equals("PROD"))
            {
                var cartDetails = AdobeCommerce.GetCartDetails(_testData.User, _storeContext.Store);
                for(var count = 1; count <= chaptersCount; count++)
                {
                    var chapterSku = cartDetails.Items.FindAll(i => i.Name.Contains("Chapter"));
                    var chapterProduct = _productBuilder.GetChapterProduct(chapterSku[count - 1].Sku);
                    if(chapterProduct.Title != null)
                    {
                        if(!_cartContext.ProductList.Any(i => i.Title.Equals(chapterProduct.Title)))
                            _cartContext.ProductList.Add(chapterProduct);
                    }
                }
            }
            else
            {
                AddChapterMembershipOnCartToProductList(_cartContext.ProductList);
            }
        }

        [When(@"Add event product to cart")]
        public void WhenAddEventProductToCart()
        {
            var product = _productBuilder.GetDigitalProduct();

            _context.GoTo(_userSettings.BaseUrl, $"/shop/p-/{product.Sku}")
                .VerifyPage<ProductDetailsPage>()
                .AddToCart()
                .VerifyPage<CartPage>();

            _cartContext.ProductList.Add(product);
        }

        [Then(@"Verify order summary on the cart page")]
        public void ThenVerifyOrderSummaryOnTheCartPage()
        {
            if(_context.WebDriver().Url.Contains(_context.InitPage<CartPage>().RelativePath))
                _context.InitPage<CartPage>();
        }

        [Then(@"Verify View Dashboard and View Course Library")]
        public void ThenVerifyViewDashboardAndViewCourseLibrary()
        {
            _context.VerifyPage<OrderConfirmationPage>()
               .VerifyPageLoaded()
               .VerifyAccessItems();
        }

        [When(@"Fill out payment page for Zero price product")]
        public void WhenFillOutPaymentPageForZeroPriceProduct()
        {
            FillOutContactInformation();
            FillOutBillingInformation("Add new");
        }

        [When(@"Place zero dollar order")]
        [When(@"Place order without payment method")]
        public void WhenPlaceOrderWithoutPaymentMethod()
        {
            _context.InitPage<CheckoutPage>()
               .PlaceOrderForZeroDollar()
               .VerifyPage<OrderConfirmationPage>();
        }

        [When(@"Place order on checkout page")]
        public void ThenPlaceYourOrderOnNewCheckoutPage()
        {
            _context.InitPage<CheckoutPage>()
               .PlaceOrder()
               .VerifyPage<OrderConfirmationPage>();
        }

        [When(@"Add '([^']*)' credit card details on checkout page and (.*) for future use")]
        public void WhenAddCreditCardDetailsOnCheckoutPage(string cardName, string saveForFutureUse)
        {
            var alternatePaymentInfo = _testData.PaymentInfo;
            alternatePaymentInfo.CreditCard = _testData.GetCreditCard(cardName);
            _context.VerifyPage<CheckoutPage>().EnterCreditCardPaymentDetails(alternatePaymentInfo, IsSaveForFutureUse(saveForFutureUse), _cartContext);
            HandleStoreSpecificPaymentSteps();
        }

        [Then(@"Verify order confirmation page for quote order")]
        public void ThenVerifyOrderConfirmationPageForQuoteOrder()
        {
            _context.VerifyPage<OrderConfirmationPage>()
               .VerifyQuoteOrderConfirmation(_cartContext);
            _context.InitPage<OrderConfirmationPage>()
               .VerifyOrderReceipt(_cartContext, _storeContext, GetPromoDiscountPercent());
        }

        [When(@"Update Adyen creditcard payment details and (.*) for future use")]
        public void UpdateAdyenCreditCardPayment(string saveForFutureUse)
        {
            _context.VerifyPage<CheckoutPage>()
                .EnterAdyenCreditCardDetails(_testData.PaymentInfo, _cartContext, _storeContext,IsSaveForFutureUse(saveForFutureUse));
        }

        [When(@"Place Adyen Credit card order and (.*) for future use")]
        public void WhenPlaceAdyenCreditCardOrder(string saveForFutureUse = "not save")
        {
            _context.VerifyPage<CheckoutPage>()
                .EnterAdyenCreditCardDetails(_testData.PaymentInfo, _cartContext, _storeContext, IsSaveForFutureUse(saveForFutureUse))
                .PlaceAdyenCreditCardOrder(_cartContext);
        }

        [When(@"Place Adyen Credit card order and (.*) for future use and verify 3DS")]
        public void WhenPlaceAdyenCreditCardOrderVerify3ds(string saveForFutureUse = "not save")
        {
            _context.VerifyPage<CheckoutPage>()
                .EnterAdyenCreditCardDetails(_testData.PaymentInfo, _cartContext, _storeContext, IsSaveForFutureUse(saveForFutureUse))
                .PlaceAdyenCreditCardOrder(_cartContext)
                .HandleAdyen3DSPopup();
        }

        [When(@"Place order using Adyen Paypal payment method")]
        public void WhenPlaceOrderUsingAdyenPaypalPaymentMethod()
        {
                _context.VerifyPage<CheckoutPage>()
                    .PlaceAdyenPayPalOrder(_testData, _context);     
        }

        [Then(@"Place Adyen AliPay order")]
        public void WhenPlaceAdyenAliPayOrder()
        {
            _context.VerifyPage<CheckoutPage>()
                .OpenAdyenAliPaySimulator();
            _context.VerifyPage<AdyenAliPayPage>()
                .MakeAdyenAliPayPayment();
        }

        [Then(@"Open Adyen AliPay Simulator")]
        public void WhenOpenAdyenAliPaySimulator()
        {
            _context.VerifyPage<CheckoutPage>()
                .OpenAdyenAliPaySimulator();
        }

        [When(@"Place order using Adyen ACH payment method")]
        public void WhenPlaceOrderUsingAdyenACHPaymentMethod()
        {
            _context.VerifyPage<CheckoutPage>()
                .EnterAdyenACHPaymentDetails(_testData.GetAdyenCredential(), _testData.PaymentInfo, _cartContext, _testData.GetAddressForCountry());
        }

        [Given(@"go to gifting membership page")]
        public void GivenGoToGiftingMembershipPage()
        {
            _context.GoTo(_userSettings.BaseUrl, "/testpages/gift-membership")
            .VerifyPage<GiftingMembershipPage>();
        }

        [Then(@"Fill out recipient detials")]
        public void ThenFillOutRecipientDetials()
        {
            _context.VerifyPage<GiftingMembershipPage>()
                .EnterRecipientDetails(_testData.GetAddressForCountry());
        }

        [When(@"Proceed to checkout from gifting membership page")]
        public void WhenProceedToCheckoutFromGiftingMembershipPage()
        {
            _context.VerifyPage<GiftingMembershipPage>()
                .ContinueToPayment();
            var product = _productBuilder.GetMembershipProduct(MembershipType.Individual);
            _cartContext.ProductList.Add(product);
            _context.VerifyPage<CheckoutPage>();
        }

        [Then(@"Verify cart count on adaptive navigation")]
        public void ThenVerifyCartCountOnAdaptiveNavigation()
        {
            _context.VerifyPage<CartPage>().VerifyANCartTotal();
        }

        [When(@"Remove digital product from cart")]
        public void WhenRemoveDigitalProductFromCart()
        {
            _context.VerifyPage<CartPage>()
                .RemoveDigitalProduct(_cartContext);
        }

        [Then(@"Verify saved billing address and payment type on checkout page")]
        public void VerifySavedBillingAddressAndPaymentTypeOnCheckoutPage()
        {
            _context.VerifyPage<CheckoutPage>()
                .VerifySavedAddressDetails(_testData)
                .VerifySavedPaymentType(_testData.PaymentInfo);
        }

        [When(@"Place with Adyen Credit Card Order for Saved Payment")]
        public void ThenPlaceWithAdyenCreditCardOrderForSavedPayment()
        {
            _context.VerifyPage<CheckoutPage>()
                .PlaceOrderForTokenizedAdyenCreditCard(_testData.PaymentInfo);
        }

        [Given(@"Navigate to eLearning PDP free for Members")]
        [When(@"Navigate to free product PDP")]
        public void WhenNavigateToFreeProductPDP()
        {
            var product = _productBuilder.GetDigitalProduct();
            var productsPage = _context.GoTo(_userSettings.BaseUrl, "/store/products#sort=relevancy")
                .VerifyPage<ProductsPage>()
                .Search(product.Title);
                productsPage.LearnMore();
                _context.VerifyPage<ElearningPdpPage>();
            _cartContext.ProductList.Add(product);
        }

        [When(@"Initate free product activation flow")]
        public void WhenInitateTheFreeProductActivationFlow()
        {
            _context.VerifyPage<ElearningPdpPage>()
                .InitiateFreeProductActivation();
        }

        [Then(@"Verify free product activation is successful")]
        public void ThenVerifyFreeProductActivationIsSuccessful()
        {
            var product = _productBuilder.GetDigitalProduct();
            _context.InitPage<ElearningPdpPage>()
                .VerifyLaunchCourseDialog(_testData.User, product.Title)
                .LaunchCourse();
        }

        [Then(@"Verify Continue Course on PDP")]
        public void ThenVerifyContinueCourseOnPDP()
        {
            _context.VerifyPage<ElearningPdpPage>()
                .VerifyContinueCourse();
        }

        [Then(@"Logout and Login with same user")]
        public void ThenLogoutAndLoginWithSameUser()
        {
            _context.VerifyPage<OrderConfirmationPage>()
                .LoggedOut();

            _context.VerifyPage<LoginPage>()
                .VerifyPageLoaded();

            LoginWithUser();
        }

        [When(@"I navigate to PDP Membership")]
        public void WhenINavigateToPDPMembership()
        {
            var productTitle = _productBuilder.GetMembershipProductTitle();
            _context.GoTo(_userSettings.BaseUrl, "/store/products#sort=relevancy")
                .VerifyPage<ProductsPage>()
                    .Search(productTitle)
                    .ClickOnProductTitle()
                    .VerifyPage<MembershipDetailPage>();
        }

        [When(@"Add PMI Membership from PDP")]
        public void WhenNavigateToMembershipPDP()
        {
            _context.GoTo(_userSettings.BaseUrl, "/store/products#sort=relevancy");
            _context.InitPage<MembershipDetailPage>()
                .NavigateToMembershipPDPAndAddToCart();
        }

        [Then(@"Verify membership status")]
        public void ThenVerifyMembershipStatus()
        {
            _context.VerifyPage<MembershipDetailPage>()
                .ValidateAlreadyMembershipStatus(MembershipStatus.Membership, _productBuilder);
        }

        [Then(@"Verify membership status renewal")]
        public void ThenVerifyMembershipStatusRenewal()
        {
            _context.VerifyPage<MembershipDetailPage>()
                .ValidateAlreadyMembershipStatus(MembershipStatus.Renewal, _productBuilder);
        }

        [Then(@"Verify membership status expired")]
        public void ThenVerifyMembershipStatusExpired()
        {
            _context.VerifyPage<MembershipDetailPage>()
                .ValidateAlreadyMembershipStatus(MembershipStatus.Expired, _productBuilder);
        }

        [When(@"Place Adyen Amazonpay order")]
        public void WhenPlaceAdyenAmazonpayOrder()
        {
            _context.InitPage<CheckoutPage>()
                .VerifyPageLoaded()
                .CheckoutWithAdyenAmazonPay();

            _context.InitPage<AdyenAmazonPayPage>()
                .PlaceAdyenAmazonPayOrder(_testData);
        }

        [Then(@"Verify Skip to main content")]
        public void ThenVerifySkipToMainContentElement()
        {
            _context.VerifyPage<ProductDetailsPage>().VerifyScrollPositionAfterSkipToMainContent();
        }

        [Then(@"Verify order history for free product activation")]
        public void ThenVerifyOrderHistory()
        {
            var product = _productBuilder.GetDigitalProduct();
            _context.VerifyPage<MyPmiOrderHistoryPage>()
                .VerifyProductTitle(product.Title);
        }

        [When(@"Add Membership Renewal from MyPmi")]
        public void AddMembershipRenewalFromMyPmi()
        {
            _context.InitPage<MyPmiPage>().RenewMembershipFromMyPMI();
            var product = _productBuilder.GetMembershipProduct(MembershipType.Retiree);
            _cartContext.ProductList.Add(product);
        }

        [When(@"Place RazorPay Credit card order for Subscription product")]
        public void WhenPlaceRazorPayCreditCardOrder()
        {
            _context.VerifyPage<CheckoutPage>()
                .PlaceRazorPayCreditCardOrderForSubscription(_testData.PaymentInfo);
        }

        [When(@"Place RazorPay Credit card order for Non-Subscription product")]
        public void PlaceRazorPayCreditCardOrder()
        {
            _context.VerifyPage<CheckoutPage>()
                .PlaceRazorPayCreditCardOrderForNonSubscription(_testData.PaymentInfo);
        }

        [When(@"Place RazorPay UPI for Subscription product")]
        public void WhenPlaceRazorUPIOrder()
        {
            _context.VerifyPage<CheckoutPage>()
                .PlaceRazorPayUPIOrderForSubscription();
        }

        [When(@"Place RazorPay UPI for Non-Subscription product")]
        public void PlaceRazorUPIOrder()
        {
            _context.VerifyPage<CheckoutPage>()
                .PlaceRazorPayUPIOrderForNonSubscription();
        }

        [When(@"Place RazorPay NetBanking Order for Non-Subscription product")]
        public void PlaceRazorNetBankingOrder()
        {
            _context.VerifyPage<CheckoutPage>()
                .PlaceRazorPayNetBankingOrderForNonSubscription();
        }

    }
}
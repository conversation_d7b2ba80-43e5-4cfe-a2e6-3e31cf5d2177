﻿using Newtonsoft.Json;
using System.Collections.Generic;

namespace Pmi.Spx.Foundation.MagentoConnector.MagentoApi.Models.Product.Price
{
	public class ABPrice
	{
        [JsonProperty("group_type")]
        public string GroupType { get; set; }

        [JsonProperty("tier_prices")]
        public IEnumerable<TierPrice> TierPrices { get; set; }

        [JsonProperty("zone_prices")]
        public IEnumerable<ProductZonePriceData> ZonePrices { get; set; }

        [JsonProperty("country_prices")]
        public IEnumerable<ProductCountryPriceData> CountryPrices { get; set; }
    }
}
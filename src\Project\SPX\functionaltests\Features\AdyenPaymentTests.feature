﻿@local @browser:Chrome-Headless @desktop
Feature: AdyenPaymentTests
    Ensure user purchases different products using adyen payment methods
    Background:  
	Given an empty cart

@Qa @Can @Germany @MainStore @Adyen
Scenario: Purchase subscription product using Adyen GooglePay 
	
	Given a newly registered user from data scaffolding is logged in
	When Add subscription product to the cart via SKUs
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Proceed to checkout from cart
	 And Fill out billing address and save for future use
	Then Verify legal statement block display with payment
	 And Verify the payment methods
	 And Verify the order summary on the checkout page
	
	When Place Adyen Google Pay order
	Then Verify order confirmation page

@Qa @Can @Germany @MainStore @Adyen
Scenario: Purchase student renewal with Adyen
	
	Given the user is logged in
	When Add student membership renewal to the cart from PDP
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Proceed to checkout from cart
	 And Edit billing address and save for future use
	Then Verify legal statement block display with payment
	 And Verify the payment methods
	 And Verify the order summary on the checkout page 

@Qa @Can @Germany @MainStore @Adyen
Scenario: Purchase student membership with adyen payment
	
    Given a newly registered user from data scaffolding is logged in
	When Add digital product to the cart via SKU's
	 And Add student membership to the cart
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Proceed to checkout from cart
	 And Fill out billing address and save for future use
	Then Verify legal statement block display with payment
	 And Verify the payment methods
	 And Verify the order summary on the checkout page
	
	When Place Adyen Google Pay order
	Then Verify order confirmation page

@Ignore @Qa @Can @Germany @MainStore @NewEmail
Scenario: Buy zero dollar subscription product with Adyen and verify billing and payment details are tokenized
	
	Given a newly registered user from data scaffolding is logged in
	When Add subscription product to the cart
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Proceed to checkout from cart
	 And Fill out billing address and save for future use
	Then Verify the order summary on the checkout page
	
	When Place Adyen Credit card order and not save for future use
	Then Verify order confirmation page

	When Add membership to the cart via SKU's
	 And Proceed to checkout from cart
	Then Verify saved billing address and payment type on checkout page

@Qa @Can @Germany @MainStore
Scenario: Purchase multiple products with promo using adyen payment method

	Given a newly registered user from data scaffolding is logged in
	 When Add membership to the cart via SKU's
	  And Add chapter membership to the cart
	  And Add donation to the cart
	  And Add event product to cart
	  And Apply promo code on the cart page
	 Then Verify products on the cart page
	  And Verify the order summary on the cart page
	 
	 When Proceed to checkout from cart
	  And Fill out billing address and save for future use
	 Then Verify the order summary on the checkout page
	 
	 When Place Adyen Credit card order and not save for future use
	 Then Verify order confirmation page

@Qa @Can @Germany @MainStore
Scenario: Verify order summary for multiple products with voucher

	Given a newly registered user from data scaffolding is logged in
	When Add digital product to the cart via SKU's
	 And Add donation to the cart
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Proceed to checkout from cart
	 And Fill out billing address and not save for future use
	 And Apply voucher on the cart page
	Then Verify the order summary on the checkout page
	 And Verify Adyen Price After Voucher Discount on the checkout page
	 
@MainStore @Qa @Can @Germany
Scenario: Buy course using Adyen Alipay payment method
   
	Given a newly registered user from data scaffolding is logged in
	When Add digital product to the cart via SKU's
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Proceed to checkout from cart
	 And Fill out contact information
	 And Fill out billing address and not save for future use
	Then Verify the order summary on the checkout page
	
	 And Place Adyen AliPay order
	Then Verify order confirmation page 

@MainStore @Qa @Can @Germany
Scenario: Buy course using Adyen Paypal payment method

	Given a newly registered user from data scaffolding is logged in
	When Add digital product to the cart via SKU's
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Proceed to checkout from cart
	 And Fill out contact information
	 And Fill out billing address and not save for future use
	Then Verify the order summary on the checkout page
	
	When Place order using Adyen Paypal payment method
	Then Verify order confirmation page

@MainStore @Qa @Can @Germany
Scenario: Buy individual membership with Adyen and verify billing and payment details are tokenized
   
   Given a newly registered user from data scaffolding is logged in
	When Add membership to the cart via SKU's 
	 And Add chapter membership to the cart
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Proceed to checkout from cart
	 And Fill out billing address and save for future use
	Then Verify the order summary on the checkout page
	
	When Place Adyen Credit card order and not save for future use
	Then Verify order confirmation page
	When Add digital product to the cart via SKU's
     And Proceed to checkout from cart
    Then Verify saved billing address and payment type on checkout page

@MainStore @Qa @Can @Germany
Scenario: Ensure second purchase made using an Adyen credit card that has been tokenized
	
	Given a newly registered user from data scaffolding is logged in
	When Add digital product to the cart via SKU's
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Proceed to checkout from cart
	 And Add new billing address and save for future use
	Then Verify the order summary on the checkout page
	
	When Place Adyen Credit card order and save for future use
	Then Verify order confirmation page
	
	When Empty the cart
	 And Add digital product to the cart via SKU's
	Then Verify products on the cart page
	
	When Proceed to checkout from cart
    Then Verify saved billing address and payment type on checkout page
	When Place with Adyen Credit Card Order for Saved Payment
	Then Verify order confirmation page

@MainStore @Qa @Can @Germany
Scenario: Ensure that Adyen credit card 3DS is functioning properly
   
	Given a newly registered user from data scaffolding is logged in
	When Add digital product to the cart via SKU's
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Proceed to checkout from cart
	 And Fill out billing address and not save for future use
	Then Verify the payment methods
	Then Verify the order summary on the checkout page
	
	When Place Adyen Credit card order and  not save for future use and verify 3DS
	Then Verify order confirmation page

@MainStore @Qa @Can @Adyen
Scenario: Purchase and Verify Subscription Product on PDP Page

	Given a newly registered user from data scaffolding is logged in
	 When Add digital product to the cart via SKU's
	 Then Verify products on the cart page
	 Then Verify the order summary on the cart page
	 When Proceed to checkout from cart
	 And Add new billing address and save for future use

	 When Place Adyen Credit card order and save for future use
	 Then Verify order confirmation page
	  And Logout and Login with same user

	 When Navigate to Subscription PDP
	 Then Verify Subscription status on the Subscription Detail Page
	 When Navigate to MyPmi Subscription page
	 Then Verify Subscription product on MyPmi Subscription page

@USAStore @Qa @Can
Scenario: Buy course using Adyen Klarna payment method
	
   Given a newly registered user from data scaffolding is logged in
	When Add digital product to the cart via SKU's
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Proceed to checkout from cart
	 And Fill out billing address and save for future use
	Then Verify legal statement block display with payment
	 And Verify the payment methods
	 And Verify the order summary on the checkout page
	When Place Adyen Klarna order
	Then Verify order confirmation page

@USAStore @Qa @Can
Scenario: Buy course using Adyen AfterPay payment method
	
   Given the user is logged in
	When Add digital product to the cart via SKU's
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Proceed to checkout from cart
	 And Fill out billing address and save for future use
	Then Verify legal statement block display with payment
	 And Verify the payment methods
	 And Verify the order summary on the checkout page
	
	When Place Adyen AfterPay order
	Then Verify order confirmation page

@USAStore @Qa
Scenario: Buy course using Adyen CashApp Pay payment method
	
   Given a newly registered user from data scaffolding is logged in
	When Add digital product to the cart via SKU's
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Proceed to checkout from cart
	 And Fill out billing address and save for future use
	Then Verify legal statement block display with payment
	 And Verify the payment methods
	 And Verify the order summary on the checkout page
	
	When Place Adyen CashApp Pay order
	Then Verify order confirmation page

	Then Clear The Product List

	When Add digital product to the cart via SKU's
	Then Verify products on the cart page

	When Proceed to checkout from cart
	 And Fill out billing address and save for future use
	Then Verify legal statement block display with payment
	 And Verify the payment methods
	 And Verify the order summary on the checkout page

	 When Place Tokenized Adyen CashApp Pay order
	 Then Verify order confirmation page

@USAStore @Qa @Can
Scenario: Buy course using Adyen ACH payment method
	
    Given a newly registered user from data scaffolding is logged in
	 When Add digital product to the cart via SKU's
	 Then Verify products on the cart page
	  And Verify the order summary on the cart page
	
	 When Proceed to checkout from cart
	  And Fill out billing address and save for future use
	 Then Verify legal statement block display with payment
	  And Verify the payment methods
	  And Verify the order summary on the checkout page
	
	 When Place order using Adyen ACH payment method
	 Then Verify order confirmation page

@USAStore @Qa 
Scenario: Buy course using Adyen AmazonPay payment method
	
   Given a newly registered user from data scaffolding is logged in
	When Add digital product to the cart via SKU's
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Proceed to checkout from cart
	 And Fill out billing address and save for future use
	Then Verify legal statement block display with payment
	 And Verify the payment methods
	 And Verify the order summary on the checkout page
	
	When Place Adyen Amazonpay order
	Then Verify order confirmation page

@MainStore @Qa @Can 
Scenario: Buy course using Adyen Paypal PayLater payment method

	Given a newly registered user from data scaffolding is logged in
	When Add digital product to the cart via SKU's
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Proceed to checkout from cart
	 And Fill out contact information
	 And Fill out billing address and not save for future use
	Then Verify the order summary on the checkout page
	
	When Place order using Adyen Paypal payment method
	Then Verify order confirmation page

	@ChinaStore @Qa
Scenario: Buy course using Adyen WeChat Pay payment method
Given a newly registered user from data scaffolding is logged in
	When Add digital product to the cart via SKU's
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	When Proceed to checkout from cart
	 And Fill out billing address and save for future use
	Then Verify the payment methods
	 And Verify the order summary on the checkout page
	When Place Adyen WeChat Pay order
	Then Verify order confirmation page
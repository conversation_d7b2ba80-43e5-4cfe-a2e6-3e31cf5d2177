﻿namespace Pmi.Spx.Foundation.Commerce.CommerceRepositories
{
    using Connect.Constants;
    using Connect.Models.Search;
    using Connect.Services;
    using Connect.Utils;
    using Framework.Services.GlassMapper;
    using Glass.Mapper.Sc;
    using ModelMappers;
    using Pmi.Spx.Foundation.Connect.Configuration;
    using Pmi.Spx.Foundation.Framework.Services;
    using Pmi.Spx.Foundation.Framework.Services.SitecoreAbstractions;
    using Repositories;
    using Services;
    using Sitecore;
    using Sitecore.Commerce.Data.Products;
    using Sitecore.Commerce.Entities.Products;
    using Sitecore.Configuration;
    using Sitecore.Data.Fields;
    using Sitecore.Data.Items;
    using Sitecore.DependencyInjection;
    using Sitecore.Diagnostics;
    using Sitecore.Mvc.Extensions;
    using System;
    using System.Collections.Generic;
    using System.Collections.Specialized;
    using System.Linq;
    using System.Web;

    public class ProductRepository : Sitecore.Commerce.Data.Products.DelayedSyncProductRepository, IProductRepository
    {
        private readonly IConnectEntityMapper entityMapper;
        private readonly ISitecoreServiceFactory sitecoreServiceFactory;
        private readonly IProductKeyService productKeyService;
        private readonly IProductStoreRepository productStoreRepository;
        private readonly ICommerceSearchService commerceSearchService;
        private readonly IProductMatchingService productMatchingService;
        private readonly TokenReplacementService tokenReplacementService;
        private readonly IExtSitecoreItemManager extSitecoreItemManager;
        private readonly ISitecoreCacheManager sitecoreCacheManager;
        private readonly IConnectConfiguration connectConfiguration;

        private static readonly Lazy<string> IdentificationPath = new Lazy<string>(() => Factory.GetString("/sitecore/paths/identification", true));

        public IIdGenerator SubstituteIdGenerator { get; set; }
        public override IIdGenerator IdGenerator { get => this.SubstituteIdGenerator ?? base.IdGenerator; protected set => this.SubstituteIdGenerator = value; }
        public ProductAttributeValuesRepository ProductAttributeValuesRepository { get; set; }
        public IProductRelatedEntityRepository<Models.Product.CustomerPrice> ProductCustomerPricesRepository { get; set; }

        public ProductRepository()
            : this(
                ServiceLocator.ServiceProvider.GetService(typeof(IConnectEntityMapper)) as IConnectEntityMapper,
                ServiceLocator.ServiceProvider.GetService(typeof(ISitecoreServiceFactory)) as ISitecoreServiceFactory,
                ServiceLocator.ServiceProvider.GetService(typeof(IProductKeyService)) as IProductKeyService,
                ServiceLocator.ServiceProvider.GetService(typeof(IProductStoreRepository)) as IProductStoreRepository,
                ServiceLocator.ServiceProvider.GetService(typeof(ICommerceSearchService)) as ICommerceSearchService,
                ServiceLocator.ServiceProvider.GetService(typeof(IProductMatchingService)) as IProductMatchingService,
                ServiceLocator.ServiceProvider.GetService(typeof(IExtSitecoreItemManager)) as IExtSitecoreItemManager,
                ServiceLocator.ServiceProvider.GetService(typeof(TokenReplacementService)) as TokenReplacementService,
                ServiceLocator.ServiceProvider.GetService(typeof(ISitecoreCacheManager)) as SitecoreCacheManager,
                ServiceLocator.ServiceProvider.GetService(typeof(IConnectConfiguration)) as IConnectConfiguration)
        {
        }

        public ProductRepository(IConnectEntityMapper entityMapper, ISitecoreServiceFactory sitecoreServiceFactory,
            IProductKeyService productKeyService, IProductStoreRepository productStoreRepository,
            ICommerceSearchService commerceSearchService, IProductMatchingService productMatchingService,
            IExtSitecoreItemManager extSitecoreItemManager, TokenReplacementService tokenReplacementService,
            ISitecoreCacheManager sitecoreCacheManager, IConnectConfiguration connectConfiguration)
        {
            Assert.IsNotNull(productStoreRepository, nameof(productStoreRepository));
            Assert.IsNotNull(entityMapper, nameof(entityMapper));
            Assert.IsNotNull(sitecoreServiceFactory, nameof(sitecoreServiceFactory));
            Assert.IsNotNull(productKeyService, nameof(productKeyService));
            Assert.IsNotNull(commerceSearchService, nameof(commerceSearchService));
            Assert.IsNotNull(productMatchingService, nameof(productMatchingService));
            Assert.IsNotNull(extSitecoreItemManager, nameof(extSitecoreItemManager));
            Assert.IsNotNull(tokenReplacementService, nameof(tokenReplacementService));
            Assert.IsNotNull(sitecoreCacheManager, nameof(sitecoreCacheManager));
            Assert.IsNotNull(connectConfiguration, nameof(connectConfiguration));

            this.productStoreRepository = productStoreRepository;
            this.entityMapper = entityMapper;
            this.sitecoreServiceFactory = sitecoreServiceFactory;
            this.productKeyService = productKeyService;
            this.commerceSearchService = commerceSearchService;
            this.productMatchingService = productMatchingService;
            this.extSitecoreItemManager = extSitecoreItemManager;
            this.tokenReplacementService = tokenReplacementService;
            this.sitecoreCacheManager = sitecoreCacheManager;
            this.connectConfiguration = connectConfiguration;
        }

        public void SaveCustomerPrices(string externalProductId, IEnumerable<Models.Product.CustomerPrice> customerPrices)
        {
            this.ProductCustomerPricesRepository.SaveProductEntities(externalProductId, customerPrices);
        }

        public Sitecore.Data.ID GetProductID(string sku, string storeCode)
        {
            var productKey = productKeyService.Stringify(sku, storeCode);
            var rootItem = this.GetRepositoryRootItem();
            var id = this.GetEntityItemId(rootItem, productKey);
            return id;
        }

        protected override Item CreateEntityItem(Item root, Item entityItemImmediateRoot, Product entity, bool moveToBucket)
        {
            var product = entity as Models.Product.Product;
            var masterStore = this.productStoreRepository.GetMasterProductStore();
            if(masterStore.ExternalId != product.StoreCode)
            {
                var masterProduct = this.GetEntityItem(root, this.productKeyService.Stringify(product.ExternalId, masterStore.ExternalId));
                var clonedProductId = this.GetEntityItemId(root, this.productKeyService.Derive(product));
                var clonedProductName = this.GetEntityItemName(root, product);
                return this.extSitecoreItemManager.CloneTo(masterProduct, root, clonedProductId, clonedProductName, true);
            }
            var entityItem = base.CreateEntityItem(root, entityItemImmediateRoot, entity, moveToBucket);
            //Cannot override GetEntityItemTemplateId because we create a product off the branch template! Hence changing the template here.
            var templateId = this.GetEntityItemTemplateIdInner(entity);
            entityItem.ChangeTemplate(templateId);
            //Needs to have the GetItem in other functions return the item instead of null (possible reason: events are disabled)
            this.sitecoreCacheManager.ClearAllCaches();
            return entityItem;
        }

        protected override void UpdateEntityItem(Item entityItem, Product entity)
        {
            var product = entity as Models.Product.Product;
            var sitecoreContext = this.sitecoreServiceFactory.Create(entityItem.Database.Name);
            var productStore = this.productStoreRepository.GetProductStoreByCode(product.StoreCode);
            EnsureWorkflowState(entityItem);                       
            var sitecoreProduct = sitecoreContext.GetItem<Connect.TemplateModels.ICommerceProductBase>(entityItem);
            if(sitecoreProduct == null)
            {
                return;
            }
            this.entityMapper.MapProduct(product, sitecoreProduct);
            sitecoreProduct.ProductStore = sitecoreContext.GetItem<Connect.TemplateModels.IProductStore>(productStore.SitecoreItemId);
            sitecoreProduct.IsDeleted = false;
            var sitecoreService = this.sitecoreServiceFactory.Create(this.Database.Name);
            var productKind = sitecoreService.GetItems<Item>(Query.New(string.Format(ProductConstants.ProductKindsQueryByExternalID, product.ProductTypeId))).FirstOrDefault();
            sitecoreProduct.ProductKind = productKind?.ID.Guid;
            sitecoreContext.SaveItem(sitecoreProduct);

            //Needs to have the GetItem in other functions return the item instead of null (possible reason: events are disabled)
            //this.sitecoreCacheManager.ClearAllCaches(); // Disabling due to performance issue
            this.UpdateEntityItemInternal(entityItem, entity, productStore.StoreName);
            this.UpdateProductField(entityItem, product, entityItem);
        }

        protected void UpdateEntityItemInternal(Item entityItem, Product entity, string storeName)
        {
            entity.SitecoreItemId = entityItem.ID.ToGuid();
            using(new EditContext(entityItem))
            {
                var processedTitle = this.tokenReplacementService.Process(entity.Name);
                entityItem["ExternalID"] = entity.ExternalId;
                if(!this.TrySetItemName(entityItem, this.GetEntityItemName(entityItem.Parent, entity)))
                {
                    entityItem.Name = entityItem.ID.Guid.ToString("N");
                }

                entityItem.Appearance.DisplayName = $"[{storeName}] {entity.Name}";
                entityItem["Title"] = processedTitle;
                entityItem["HtmlTitle"] = processedTitle;
                entityItem["Short Title"] = processedTitle;
                entityItem["Name"] = entity.Name;
                entityItem["Short Description"] = entity.ShortDescription;
                entityItem["Full Description"] = entity.FullDescription;
                entityItem["BrandName"] = entity.BrandName;
                entityItem["ModelName"] = entity.ModelName;
                var field = entityItem.Fields["Identification"];
                if(field == null)
                {
                    return;
                }

                var obj = entityItem.Database.GetItem(ProductRepository.IdentificationPath.Value);
                var nameValueCollection = new NameValueCollection();
                foreach(var keyValuePair in entity.Identification)
                {
                    var upperInvariant = keyValuePair.Value;
                    var child = obj.Children[keyValuePair.Value];
                    if(child != null)
                    {
                        upperInvariant = HttpUtility.UrlEncode(child.ID.ToString()).ToUpperInvariant();
                    }

                    nameValueCollection.Add(keyValuePair.Key, upperInvariant);
                }
                ((NameValueListField)field).NameValues = nameValueCollection;
            }
        }

        private bool TrySetItemName(Item item, string name)
        {
            try
            {
                item.Name = name;
                return true;
            }
            catch(Exception e)
            {
                Sitecore.Diagnostics.Log.Error($"Cannot set item name: {item.ID}", e, this);
                return false;
            }
        }
        private void UpdateProductField(Item sitecoreProduct, Models.Product.Product product, Item entityItem)
        {
            if((connectConfiguration.TemplatesPath + ProductConstants.ProductBaseTemplateLocation).Contains(entityItem.Template.FullName) ||
               (connectConfiguration.TemplatesHeadlessPath + ProductConstants.ProductBaseTemplateLocation).Contains(entityItem.Template.FullName))
            {
                return;
            }
            using(new EditContext(sitecoreProduct))
            {
                foreach(var field in entityItem.Template.Fields)
                {
                    var sitecoreProductField = sitecoreProduct.Fields[field.Name];
                    if(sitecoreProductField != null && product.CustomFields.TryGetValue(field.Name, out var value))
                    {
                        var stringValue = ValueConverter.GetStringValueByType(sitecoreProductField.Type, value);
                        if(sitecoreProductField.Type.ToLower() == CommonConstants.DroplinkFieldType)
                        {
                            var attributeItemId = this.ProductAttributeValuesRepository.GetItemIdByCodeAndValue(field.Name, stringValue);
                            sitecoreProductField.Value = attributeItemId.ToString();
                        }
                        else if(sitecoreProductField.Type.ToLower() == CommonConstants.MultilistFieldType)
                        {
                            var attributeItemIds = new List<string>();
                            foreach(var arrayValue in stringValue.Split(','))
                            {
                                attributeItemIds.Add(this.ProductAttributeValuesRepository.GetItemIdByCodeAndValue(field.Name, arrayValue).ToString());
                            }
                            sitecoreProductField.Value = string.Join("|", attributeItemIds);
                        }
                        else
                        {
                            sitecoreProductField.Value = stringValue;
                        }
                    }
                    if(sitecoreProductField.Name == "BundleProductOptions" && !string.IsNullOrWhiteSpace(product.ProductBundleOptions))
                    {
                        sitecoreProductField.Value = product.ProductBundleOptions;
                    }
                    if(sitecoreProductField.Name == "ProductCustomOptions" && !string.IsNullOrWhiteSpace(product.ProductCustomOptions))
                    {
                        sitecoreProductField.Value = product.ProductCustomOptions;
                    }
                    if(sitecoreProductField.Name == "Country Prices" && !string.IsNullOrWhiteSpace(product.CountryPrices))
                    {
                        sitecoreProductField.Value = product.CountryPrices;
                    }
                    if(sitecoreProductField.Name == "Zone Prices" && !string.IsNullOrWhiteSpace(product.ZonePrices))
                    {
                        sitecoreProductField.Value = product.ZonePrices;
                    }
                    if(sitecoreProductField.Name == "AB Prices" && !string.IsNullOrWhiteSpace(product.ABPrices))
                    {
                        sitecoreProductField.Value = product.ABPrices;
                    }
                }
            }
        }

        protected override void PopulateEntity(Item entityItem, Product entity)
        {
            base.PopulateEntity(entityItem, entity);
            var sitecoreProduct = this.sitecoreServiceFactory.Create(entityItem.Database.Name).GetItem<Connect.TemplateModels.ICommerceProductBase>(entityItem);
            if(sitecoreProduct == null)
            {
                return;
            }

            var product = entity as Models.Product.Product;
            this.entityMapper.MapProduct(sitecoreProduct, product);
            product.StoreCode = sitecoreProduct.ProductStore == null ? null : this.productStoreRepository.GetProductStoreById(sitecoreProduct.ProductStore.Id)?.ExternalId;
        }

        public override bool DeleteProduct(string externalId)
        {
            var entityItem = this.GetEntityItem(this.GetRepositoryRootItem(), externalId);
            if(entityItem == null)
            {
                return false;
            }
            var sitecoreContext = this.sitecoreServiceFactory.Create(entityItem.Database.Name);
            var sitecoreProduct = sitecoreContext.GetItem<Connect.TemplateModels.ICommerceProductBase>(entityItem);
            sitecoreProduct.IsDeleted = true;
            sitecoreContext.SaveItem(sitecoreProduct);
            return true;
        }

        public bool DeleteProduct(string sku, string storeCode)
        {
            var productKey = productKeyService.Stringify(sku, storeCode);
            return DeleteProduct(productKey);
        }

        protected override string GetEntityKey(Item root, Product entity)
        {
            var product = entity as Models.Product.Product;
            return this.productKeyService.Derive(product);
        }

        protected override string GetEntityItemName(Item root, Product entity)
        {
            Assert.IsNotNull(entity, nameof(entity));
            Assert.IsNotNull(entity.Name, "entity.Name");
            var product = entity as Models.Product.Product;
            var name = $"{entity.ExternalId}-{product.StoreCode}-{entity.Name}";
            return ItemUtil.ProposeValidItemName(name);
        }

        protected override IEnumerable<Item> GetEntityItems(Item repositoryItem)
        {
            var products = this.commerceSearchService.GetProducts(new CommerceSearchOptions { IncludeDeleted = true }).SearchResultItems;
            return products.Select(_ => _.GetItem()).ToList();
        }

        /// <summary>
        ///     Ensures the state of the workflow if workflow state is empty and a default workflow is set.
        /// </summary>
        /// <param name="item">The Sitecore product item.</param>
        private static void EnsureWorkflowState(Item item)
        {
            if(item == null)
            {
                throw new ArgumentNullException(nameof(item));
            }

            if(!string.IsNullOrEmpty(item[FieldIDs.WorkflowState].ValueOrEmpty()) ||
                string.IsNullOrEmpty(item[FieldIDs.DefaultWorkflow].ValueOrEmpty()))
            {
                return;
            }

            var workflow = item.Database.WorkflowProvider.GetWorkflow(item[FieldIDs.DefaultWorkflow]);
            workflow?.Start(item);
        }

        private TemplateItem GetEntityItemTemplateIdInner(Product entity)
        {
            var product = entity as Models.Product.Product;
            var sitecoreService = this.sitecoreServiceFactory.Create(this.Database.Name);
            var productType = sitecoreService.GetItems<Item>(Query.New(string.Format(ProductConstants.ProductTypesQueryByExternalID, product.ProductAttributeTypeId))).FirstOrDefault();
            var productKind = sitecoreService.GetItems<Item>(Query.New(string.Format(ProductConstants.ProductKindsQueryByExternalID, product.ProductTypeId))).FirstOrDefault();
            var productTemplateMappings = sitecoreService.GetItems<Connect.TemplateModels.IProductTemplateMapping>(Query.New(connectConfiguration.TemplatesMappingPath + "/*"));
            var bestProductTemplateMapping = this.productMatchingService.GetBestMatch(productTemplateMappings, productType?.ID.Guid, productKind?.ID.Guid);
            if(bestProductTemplateMapping != null)
            {
                var template = bestProductTemplateMapping.ProductTemplateHeadless ?? bestProductTemplateMapping.ProductTemplate;
                if(template != null)
                {
                    return sitecoreService.GetItem<Item>(template.Value);
                }
            }
            return sitecoreService.GetItems<Item>(Query.New(connectConfiguration.TemplatesPath + ProductConstants.ProductBaseTemplateLocation)).FirstOrDefault();
        }
    }
}
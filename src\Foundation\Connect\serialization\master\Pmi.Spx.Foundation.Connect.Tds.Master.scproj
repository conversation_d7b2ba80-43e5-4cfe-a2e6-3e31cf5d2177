﻿<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.21022</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>b575ced3-e127-4124-b66b-f78acb8e6b76</ProjectGuid>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <CompactSitecoreItemsInProjectFile>True</CompactSitecoreItemsInProjectFile>
    <AssemblyName>Pmi.Spx.Foundation.Connect.Tds.Master</AssemblyName>
    <Name>Pmi.Spx.Foundation.Connect.Tds.Master</Name>
    <RootNamespace>Pmi.Spx.Foundation.Connect.Tds.Master</RootNamespace>
    <ManageRoles>False</ManageRoles>
    <SitecoreDatabase>master</SitecoreDatabase>
    <AssemblyStatus>Exclude</AssemblyStatus>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <SourceWebVirtualPath>/Pmi.Spx.Foundation.Connect.csproj</SourceWebVirtualPath>
    <SourceWebProject>{60cd1765-857b-4bc8-831e-7526264f1ec5}|Foundation\Connect\code\Pmi.Spx.Foundation.Connect.csproj</SourceWebProject>
    <SourceWebPhysicalPath>..\..\code</SourceWebPhysicalPath>
    <CodeGenTargetProject>Pmi.Spx.Foundation.Connect</CodeGenTargetProject>
    <BaseTransformFile>GlassV5Item.tt</BaseTransformFile>
    <HeaderTransformFile>GlassV5Header.tt</HeaderTransformFile>
    <CodeGenFile>SitecoreModels.Generated.cs</CodeGenFile>
    <BaseNamespace>TemplateModels</BaseNamespace>
    <EnableCodeGeneration>True</EnableCodeGeneration>
    <FieldsForCodeGen>Title,Blob,Shared,Unversioned,Default value,Validation,ValidationText,__Long description,__Short description,__Display name,__Hidden,__Read Only,__Sortorder</FieldsForCodeGen>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>.\bin\Debug\</OutputPath>
    <RecursiveDeployAction>SitecoreRecycle</RecursiveDeployAction>
    <InstallSitecoreConnector>True</InstallSitecoreConnector>
    <DisableFileDeployment>False</DisableFileDeployment>
    <LightningDeployMode>True</LightningDeployMode>
    <ConnectorTimeoutSeconds>120</ConnectorTimeoutSeconds>
    <EnableValidations>False</EnableValidations>
    <ValidationSettingsFilePath>.\Pmi.Spx.Foundation.Connect.Tds.Master_Debug.xml</ValidationSettingsFilePath>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
    <DebugSymbols>false</DebugSymbols>
    <OutputPath>.\bin\Release\</OutputPath>
    <RecursiveDeployAction>Ignore</RecursiveDeployAction>
  </PropertyGroup>
  <ItemGroup>
    <AssemblyAttributes Include="AssemblyFileVersion">
      <Value>$(AssemblyFileVersion)</Value>
    </AssemblyAttributes>
    <AssemblyAttributes Include="AssemblyVersion">
      <Value>$(AssemblyVersion)</Value>
    </AssemblyAttributes>
  </ItemGroup>
  <ItemGroup>
    <SitecoreItem Include="sitecore\content.item"><ItemDeployment>NeverDeploy</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX.item"><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization><ExcludeItemFrom></ExcludeItemFrom><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Global Settings.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Global Settings\Global Connect Settings.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Global Settings\Global Connect Settings\Card Types.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Global Settings\Global Connect Settings\Card Types\AmericanExpress.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Global Settings\Global Connect Settings\Card Types\DinerClub.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Global Settings\Global Connect Settings\Card Types\Discover.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Global Settings\Global Connect Settings\Card Types\Jcb.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Global Settings\Global Connect Settings\Card Types\MasterCard.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Global Settings\Global Connect Settings\Card Types\Visa.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Global Settings\Global Connect Settings\Countries Regions.item"><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Global Settings\Global Connect Settings\Currencies.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Global Settings\Global Connect Settings\Currencies\China CNY.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Global Settings\Global Connect Settings\Currencies\Indian Rupee.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Global Settings\Global Connect Settings\Currencies\US Dollar.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Global Settings\Global Connect Settings\Product Search Settings.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Product Repository.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Product Repository\Customer Groups.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Product Repository\Divisions.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Product Repository\Lookups.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Product Repository\Lookups\Global Product Specification Lookups.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Product Repository\Lookups\Identification.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Product Repository\Lookups\Identification\EAN.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Product Repository\Lookups\Identification\ISBN.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Product Repository\Lookups\Identification\ProductCode.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Product Repository\Lookups\Identification\SKU.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Product Repository\Lookups\Product Relation Type.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Product Repository\Lookups\Product Relation Type\Accessories.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Product Repository\Lookups\Product Relation Type\Bundle Items.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Product Repository\Lookups\Product Relation Type\Cross-sell.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Product Repository\Lookups\Product Relation Type\Recommended Products.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Product Repository\Lookups\Product Relation Type\Related products.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Product Repository\Lookups\Product Relation Type\Variants.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Product Repository\Lookups\Resource Types.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Product Repository\Lookups\Resource Types\Alt image.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Product Repository\Lookups\Resource Types\Download.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Product Repository\Lookups\Resource Types\Image.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Product Repository\Lookups\Resource Types\Spec sheet.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Product Repository\Lookups\Resource Types\User manual.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Product Repository\Lookups\Resource Types\Video demo.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Product Repository\Manufacturers.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Product Repository\Product Attributes.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Product Repository\Product Classifications.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Product Repository\Product Kinds.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Product Repository\Product Kinds\Bundle Product.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Product Repository\Product Kinds\Configurable Product.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Product Repository\Product Kinds\Donation Product.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Product Repository\Product Kinds\Downloadable Product.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Product Repository\Product Kinds\Gift Card.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Product Repository\Product Kinds\Grouped Product.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Product Repository\Product Kinds\Simple Product.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Product Repository\Product Kinds\Virtual Product.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Product Repository\Product Stores.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Product Repository\Product Types.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Product Repository\Products.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\layout.item"><ItemDeployment>NeverDeploy</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Layouts.item"><ItemDeployment>NeverDeploy</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Layouts\SPX.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Layouts\SPX\Foundation.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Layouts\SPX\Foundation\Connect.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Models.item"><ItemDeployment>NeverDeploy</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Models\SPX.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Models\SPX\Foundation.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Models\SPX\Foundation\Connect.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings.item"><ItemDeployment>NeverDeploy</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Foundation.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Placeholder Settings\SPX\Foundation\Connect.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings.item"><ItemDeployment>NeverDeploy</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Foundation.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\layout\Renderings\SPX\Foundation\Connect.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\media library.item"><ItemDeployment>NeverDeploy</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\media library\SPX.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\media library\SPX\Foundation.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\media library\SPX\Foundation\Connect.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\media library\SPX\Foundation\Connect\Products.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\media library\SPX\Foundation\Connect\Products\Product Specific Templates.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\media library\SPX\Foundation\Connect\Products\Product Specific Templates\placeholder-book.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\media library\SPX\Foundation\Connect\Products\Product Specific Templates\placeholder-certification.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\media library\SPX\Foundation\Connect\Products\Product Specific Templates\placeholder-chapter.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\media library\SPX\Foundation\Connect\Products\Product Specific Templates\placeholder-elearning.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\media library\SPX\Foundation\Connect\Products\Product Specific Templates\placeholder-membership.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\media library\SPX\Foundation\Connect\Products\Product Specific Templates\placeholder-miscellaneous.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system.item"><ItemDeployment>NeverDeploy</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Modules.item"><ItemDeployment>NeverDeploy</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Modules\PowerShell.item"><ItemDeployment>NeverDeploy</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Modules\PowerShell\Script Library.item"><ItemDeployment>NeverDeploy</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Modules\PowerShell\Script Library\SPX.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Modules\PowerShell\Script Library\SPX\Content Editor.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Modules\PowerShell\Script Library\SPX\Content Editor\Context Menu.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Modules\PowerShell\Script Library\SPX\Content Editor\Context Menu\Store.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Modules\PowerShell\Script Library\SPX\Content Editor\Context Menu\Store\Set Countries for regional stores.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Settings.item"><ItemDeployment>NeverDeploy</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Settings\Buckets.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Settings\Buckets\Facets.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Settings\Buckets\Facets\SPX.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Settings\Buckets\Facets\SPX\Foundation.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Settings\Buckets\Facets\SPX\Foundation\Connect.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Settings\Buckets\Facets\SPX\Foundation\Connect\Global Price.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Settings\Buckets\Facets\SPX\Foundation\Connect\PMI Segment.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Settings\Rules.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Settings\Rules\Definitions.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Settings\Rules\Definitions\Elements.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Settings\Rules\Definitions\Elements\Bucketing.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Settings\Rules\Definitions\Elements\Bucketing\Resolve Classification Path.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Settings\SPX.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Settings\SPX\Foundation.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Settings\SPX\Foundation\Connect.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Settings\Validation Rules.item"><ItemDeployment>NeverDeploy</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Settings\Validation Rules\Field Rules.item"><ItemDeployment>NeverDeploy</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Settings\Validation Rules\Field Rules\SPX.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Settings\Validation Rules\Field Rules\SPX\Foundation.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Settings\Validation Rules\Field Rules\SPX\Foundation\Connect.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Settings\Validation Rules\Field Rules\SPX\Foundation\Connect\Store Country Validation.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Workflows.item"><ItemDeployment>NeverDeploy</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Workflows\PMI SPX Content Workflow.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Workflows\PMI SPX Content Workflow\Approved.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Workflows\PMI SPX Content Workflow\Draft.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Workflows\PMI SPX Content Workflow\Draft\__OnSave.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Workflows\PMI SPX Content Workflow\Draft\__OnSave\Auto Submit Action.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Workflows\PMI SPX Content Workflow\Draft\Submit for Preview.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Workflows\PMI SPX Content Workflow\Draft\Submit for Preview\Auto Publish to Preview.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Workflows\PMI SPX Content Workflow\Pending.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Workflows\PMI SPX Content Workflow\Pending\Approve.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Workflows\PMI SPX Content Workflow\Pending\Approve\Validation Action.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Workflows\PMI SPX Content Workflow\Pending\Back to Draft.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Workflows\PMI SPX Content Workflow\Review.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Workflows\PMI SPX Content Workflow\Review\Back to Draft.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Workflows\PMI SPX Content Workflow\Review\Submit for Approval.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Workflows\PMI Store Workflow.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Workflows\PMI Store Workflow\Approved.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Workflows\PMI Store Workflow\Draft.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Workflows\PMI Store Workflow\Draft\__OnSave.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Workflows\PMI Store Workflow\Draft\__OnSave\Auto Submit Action.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Workflows\PMI Store Workflow\Draft\Bypass.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Workflows\PMI Store Workflow\Draft\Submit for Preview.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Workflows\PMI Store Workflow\Draft\Submit for Preview\Auto Publish to Preview.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Workflows\PMI Store Workflow\Pending.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Workflows\PMI Store Workflow\Pending\Approve.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Workflows\PMI Store Workflow\Pending\Approve\Validation Action.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Workflows\PMI Store Workflow\Pending\Back to Draft.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Workflows\PMI Store Workflow\Review.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Workflows\PMI Store Workflow\Review\Back to Draft.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Workflows\PMI Store Workflow\Review\Submit for Approval.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates.item"><ItemDeployment>NeverDeploy</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Branches.item"><ItemDeployment>NeverDeploy</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Branches\SPX.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Branches\SPX\Foundation.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Branches\SPX\Foundation\Connect.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Branches\SPX\Foundation\Connect\Synchronization.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Branches\SPX\Foundation\Connect\Synchronization\Commerce Product Base.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ExcludeItemFrom></ExcludeItemFrom><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Branches\SPX\Foundation\Connect\Synchronization\Commerce Product Base\%2524name.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Branches\SPX\Foundation\Connect\Synchronization\Commerce Product Base\%2524name\Relations.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Branches\SPX\Foundation\Connect\Synchronization\Product Repository.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Branches\SPX\Foundation\Connect\Synchronization\Product Repository\%2524name.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Branches\SPX\Foundation\Connect\Synchronization\Product Repository\%2524name\Customer Groups.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Branches\SPX\Foundation\Connect\Synchronization\Product Repository\%2524name\Divisions.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Branches\SPX\Foundation\Connect\Synchronization\Product Repository\%2524name\Lookups.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Branches\SPX\Foundation\Connect\Synchronization\Product Repository\%2524name\Lookups\Global Product Specification Lookups.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Branches\SPX\Foundation\Connect\Synchronization\Product Repository\%2524name\Lookups\Identification.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Branches\SPX\Foundation\Connect\Synchronization\Product Repository\%2524name\Lookups\Identification\EAN.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Branches\SPX\Foundation\Connect\Synchronization\Product Repository\%2524name\Lookups\Identification\ISBN.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Branches\SPX\Foundation\Connect\Synchronization\Product Repository\%2524name\Lookups\Identification\ProductCode.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Branches\SPX\Foundation\Connect\Synchronization\Product Repository\%2524name\Lookups\Identification\SKU.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Branches\SPX\Foundation\Connect\Synchronization\Product Repository\%2524name\Lookups\Product Relation Type.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Branches\SPX\Foundation\Connect\Synchronization\Product Repository\%2524name\Lookups\Product Relation Type\Accessories.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Branches\SPX\Foundation\Connect\Synchronization\Product Repository\%2524name\Lookups\Product Relation Type\Cross-sell.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Branches\SPX\Foundation\Connect\Synchronization\Product Repository\%2524name\Lookups\Product Relation Type\Related products.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Branches\SPX\Foundation\Connect\Synchronization\Product Repository\%2524name\Lookups\Product Relation Type\Variants.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Branches\SPX\Foundation\Connect\Synchronization\Product Repository\%2524name\Lookups\Resource Types.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Branches\SPX\Foundation\Connect\Synchronization\Product Repository\%2524name\Lookups\Resource Types\Alt image.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Branches\SPX\Foundation\Connect\Synchronization\Product Repository\%2524name\Lookups\Resource Types\Download.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Branches\SPX\Foundation\Connect\Synchronization\Product Repository\%2524name\Lookups\Resource Types\Image.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Branches\SPX\Foundation\Connect\Synchronization\Product Repository\%2524name\Lookups\Resource Types\Spec sheet.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Branches\SPX\Foundation\Connect\Synchronization\Product Repository\%2524name\Lookups\Resource Types\User manual.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Branches\SPX\Foundation\Connect\Synchronization\Product Repository\%2524name\Lookups\Resource Types\Video demo.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Branches\SPX\Foundation\Connect\Synchronization\Product Repository\%2524name\Manufacturers.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Branches\SPX\Foundation\Connect\Synchronization\Product Repository\%2524name\Product Attributes.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Branches\SPX\Foundation\Connect\Synchronization\Product Repository\%2524name\Product Classifications.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Branches\SPX\Foundation\Connect\Synchronization\Product Repository\%2524name\Product Kinds.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Branches\SPX\Foundation\Connect\Synchronization\Product Repository\%2524name\Product Kinds\Bundle Product.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Branches\SPX\Foundation\Connect\Synchronization\Product Repository\%2524name\Product Kinds\Configurable Product.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Branches\SPX\Foundation\Connect\Synchronization\Product Repository\%2524name\Product Kinds\Donation Product.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Branches\SPX\Foundation\Connect\Synchronization\Product Repository\%2524name\Product Kinds\Downloadable Product.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Branches\SPX\Foundation\Connect\Synchronization\Product Repository\%2524name\Product Kinds\Gift Card.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Branches\SPX\Foundation\Connect\Synchronization\Product Repository\%2524name\Product Kinds\Grouped Product.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Branches\SPX\Foundation\Connect\Synchronization\Product Repository\%2524name\Product Kinds\Simple Product.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Branches\SPX\Foundation\Connect\Synchronization\Product Repository\%2524name\Product Kinds\Virtual Product.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Branches\SPX\Foundation\Connect\Synchronization\Product Repository\%2524name\Product Stores.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Branches\SPX\Foundation\Connect\Synchronization\Product Repository\%2524name\Product Types.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Branches\SPX\Foundation\Connect\Synchronization\Product Repository\%2524name\Products.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Division.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Division\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Division\Main.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Division\Main\Description.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Division\Main\Name.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Divisions.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Divisions\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Identification Type.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Identification Type\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Identification Type\Type.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Identification Type\Type\Description.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Identification Type\Type\Name.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Identification Types.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Identification Types\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Lookup Value.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Lookup Value\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Lookup Value\Main.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Lookup Value\Main\Description.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Lookup Value\Main\Short.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Lookup Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Lookup Values\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Lookup Values\Main.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Lookup Values\Main\Description.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Manufacturer.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Manufacturer\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Manufacturer\Main.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Manufacturer\Main\Description.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Manufacturer\Main\Name.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Manufacturer\Main\ProductURLMacro.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Manufacturer\Main\WebsiteUrl.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Manufacturers.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Manufacturers\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Classification.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Classification\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Classification\IDs.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Classification\IDs\ExternalParentID.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Classification\Main.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Classification\Main\Description.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Classification\Main\Name.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product ClassificationGroup.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product ClassificationGroup\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product ClassificationGroup\Main.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product ClassificationGroup\Main\Description.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product ClassificationGroup\Main\Name.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Classifications Specification.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Classifications Specification\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Classifications.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Classifications\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Classifications\Main.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Classifications\Main\Description.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Classifications\Main\Name.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Global Specification.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Global Specification\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Relation.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Relation\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Relation\Main.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Relation\Main\Description.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Relation\Main\Name.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Relation\Main\RelatedProducts.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Relation\Main\Type.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Relations.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Relations\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Relations\Main.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Relations\Main\Description.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Repository.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Repository\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Resource.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Resource\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Resource\Main.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Resource\Main\Resource.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Resource\Main\Type.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Resource\Main\URI.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Resources.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Resources\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Resources\Main.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Resources\Main\Main image.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Specification.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Specification\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Specification\Main.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Specification\Main\Group.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Specification\Main\Key.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Specification\Main\LookupValue.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Specification\Main\Value.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Specifications.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Specifications\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Specifications\Main.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Specifications\Main\Description.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Specifications\Main\VariantSpecifications.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Type Specification.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Type Specification\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Type.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Type\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Type\IDs.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Type\IDs\ExternalParentID.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Type\Main.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Type\Main\Description.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Type\Main\Name.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Types.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product Types\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product\General product information.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product\General product information\BrandName.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product\General product information\Divisions.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product\General product information\Full Description.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product\General product information\Identification.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product\General product information\Manufacturer.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product\General product information\ModelName.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product\General product information\Name.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product\General product information\ProductClasses.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product\General product information\ProductType.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment><CodeGenData>type=IProductType</CodeGenData></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Product\General product information\Short Description.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Products.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Products\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Resource Folder.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Resource Folder\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Specification Lookup.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Specification Lookup\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Specification Lookup\Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Specification Lookup\Data\Name.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Specification Lookups.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Specification Lookups\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Specification.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Specification\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Specification\Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Specification\Data\Name.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Synchronization.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Synchronization\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Synchronization\IDs.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\CommerceConnect\Products\Synchronization\IDs\ExternalID.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>DeployOnce</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Foundation.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Foundation\BaseModels.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Foundation\BaseModels\Content Page.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Foundation\BaseModels\Content Page\Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\Foundation\BaseModels\Content Page\Data\Short Title.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\PMI.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\PMI\Base.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\PMI\Base\_BaseIcon.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\PMI\Base\_BaseIcon\__Standard Values.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\PMI\Base\_BaseIcon\Icon.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\PMI\Base\_BaseIcon\Icon\Icon.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\PMI\Base\_BaseIcon\Icon\IconClass.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\PMI\Base\BaseContent.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\PMI\Base\BaseContent\_BaseTitle.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\PMI\Base\BaseContent\_BaseTitle\__Standard Values.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\PMI\Base\BaseContent\_BaseTitle\Data.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\PMI\Base\BaseContent\_BaseTitle\Data\Title.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepDirectDescendantsSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Categories.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Categories\Commerce Category Base.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Categories\Commerce Category Base\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Categories\Commerce Category Base\Category Additional Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Categories\Commerce Category Base\Category Additional Data\Display Mode.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Categories\Commerce Category Base\Category Additional Data\Include In Menu.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Categories\Commerce Category Base\Category Additional Data\Is Active.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Categories\Commerce Category Base\Category Additional Data\Position.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Categories\Commerce Category Base\Category Metadata.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Categories\Commerce Category Base\Category Metadata\Is Deleted.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Customer Groups.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Customer Groups\Customer Group.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Customer Groups\Customer Group\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Customer Groups\Customer Group\Main.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Customer Groups\Customer Group\Main\Code.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Customer Groups\Customer Groups.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Customer Groups\Customer Groups\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Global Settings.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Global Settings\CardType.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Global Settings\CardType\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Global Settings\CardType\Main.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Global Settings\CardType\Main\Card Description.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Global Settings\CardType\Main\Card ID.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Global Settings\CardTypes.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Global Settings\CardTypes\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Global Settings\Countries.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Global Settings\Countries\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Global Settings\Country.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Global Settings\Country\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Global Settings\Country\CountryRegionModel.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Global Settings\Country\CountryRegionModel\Code.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Global Settings\Country\CountryRegionModel\Code3.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Global Settings\Country\CountryRegionModel\Name.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Global Settings\Currencies.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Global Settings\Currencies\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Global Settings\Currency.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Global Settings\Currency\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Global Settings\Currency\Main.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Global Settings\Currency\Main\Allowed Cards.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment><CodeGenData>type=Pmi.Spx.Foundation.Connect.TemplateModels.ICardType</CodeGenData></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Global Settings\Currency\Main\Country.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment><CodeGenData>type=Pmi.Spx.Foundation.Connect.TemplateModels.ICountry</CodeGenData></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Global Settings\Currency\Main\Currency Code.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Global Settings\Currency\Main\Currency Name.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Global Settings\Currency\Main\Currency Symbol.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Global Settings\Currency\Main\CurrencyDecimalPlaces.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Global Settings\Global Connect Settings.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Global Settings\Global Connect Settings\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Global Settings\Region.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Global Settings\Region\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Global Settings\Region\RegionModel.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Global Settings\Region\RegionModel\Code.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Global Settings\Region\RegionModel\Name.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Global Settings\Search Settings.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Global Settings\Search Settings\Commerce Search.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Global Settings\Search Settings\Commerce Search\Items Per Page.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Global Settings\Search Settings\Commerce Search\Search Facets.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Global Settings\Search Settings\Commerce Search\Sort Fields.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Lookups.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Product Attributes.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Product Attributes\Product Attribute Value.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Product Attributes\Product Attribute Value\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Product Attributes\Product Attribute Value\Main.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Product Attributes\Product Attribute Value\Main\Label.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Product Attributes\Product Attribute.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Product Attributes\Product Attribute\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Product Attributes\Product Attribute\Main.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Product Attributes\Product Attribute\Main\Code.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Product Attributes\Product Attributes.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Product Attributes\Product Attributes\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Product Kinds.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Product Kinds\Product Kind.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Product Kinds\Product Kind\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Product Kinds\Product Kind\Main.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Product Kinds\Product Kind\Main\Name.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Product Kinds\Product Kinds.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Product Kinds\Product Kinds\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Product Pages.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Product Pages\Product Page.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Product Stores.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Product Stores\Product Store.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Product Stores\Product Store\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Product Stores\Product Store\Main.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Product Stores\Product Store\Main\AvailableAddressCountries.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Product Stores\Product Store\Main\CustomerCareRegionalEmailAddress.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Product Stores\Product Store\Main\CustomerCareRegionalPhoneNumber.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Product Stores\Product Store\Main\DefaultCartLink.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Product Stores\Product Store\Main\EnableRemoveChapterLink.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Product Stores\Product Store\Main\Is Default Store.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Product Stores\Product Store\Main\Is Master Store.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Product Stores\Product Store\Main\Is Store Enabled.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Product Stores\Product Store\Main\Is Tiered Store.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Product Stores\Product Store\Main\LocalCurrencies.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment><CodeGenData>type=Pmi.Spx.Foundation.Connect.TemplateModels.ICurrency</CodeGenData></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Product Stores\Product Store\Main\Store Countries.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment><CodeGenData>type=Pmi.Spx.Foundation.Connect.TemplateModels.ICountry</CodeGenData></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Product Stores\Product Store\Main\Store Currency.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment><CodeGenData>type=Pmi.Spx.Foundation.Connect.TemplateModels.ICurrency</CodeGenData></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Product Stores\Product Store\Main\Store Id.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Product Stores\Product Store\Main\Store Identifier.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Product Stores\Product Store\Main\Store Name.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Product Stores\Product Store\Main\StoreLanguage.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment><CodeGenData></CodeGenData></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Product Stores\Product Store\Main\SyncCountries.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Product Stores\Product Store\Main\Tier.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment><CodeGenData>nullable=true</CodeGenData></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Product Stores\Product Store\Main\WebsiteId.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Product Stores\Product Stores.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Product Stores\Product Stores\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Product Types.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Product Types\Commerce Product Type Base.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products.item"><ChildItemSynchronization>KeepDirectDescendantsSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base.item"><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\__Standard Values.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\B2b general product information.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\B2b general product information\B2b Full Description.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\B2b general product information\B2b image.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\B2b general product information\B2b Short Description.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\B2b general product information\B2b Title.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\CommerceOptions.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\CommerceOptions\BundleProductOptions.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\CommerceOptions\ProductCustomOptions.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\General product information.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\General product information\Backorders.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\General product information\course_kit_type.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\General product information\course_type.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\General product information\disabled_Product_URL.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\General product information\fulfillment_provider.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\General product information\included_in_membership.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\General product information\Is In Stock.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\General product information\Is Returnable.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\General product information\is_membership.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\General product information\is_subscription_product.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\General product information\language.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\General product information\OverviewDescription.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\General product information\pmi_catalog.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\General product information\pmi_product_category.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\General product information\pmi_product_family.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\General product information\pmi_product_format.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\General product information\pmi_product_type.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\General product information\Product Kind.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment><CodeGenData>nullable=true</CodeGenData></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\General product information\Product Store.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment><CodeGenData>nullable=true&amp;type=IProductStore</CodeGenData></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\General product information\Quantity.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\General product information\single_membership.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\General product information\sold_as_bundle.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\General product information\subscription_frequency.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment><CodeGenData>nullable=true&amp;type=IProductAttributeValue</CodeGenData></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\General product information\Weight.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\IDs.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\IDs\pmi_product_id.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\Internal Attributes.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\Internal Attributes\membershipTextHighlightColor.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\Internal Attributes\RecommendedAltText.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\Internal Attributes\showCTA.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\Internal Attributes\showPrices.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\Product Custom Attributes.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\Product Custom Attributes\custom_cart.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\Product Image Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\Product Image Data\HeroBackgroundImage.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\Product Image Data\HeroBackgroundImageMobile.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\Product Image Data\HeroImage.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\Product Image Data\Image.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\Product Image Data\Small Image.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\Product Image Data\Thumbnail.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\Product Image Data\UseDarkThemeForHero.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\Product Marketing Data.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\Product Marketing Data\Is Featured.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\Product Marketing Data\PromoBadges.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\Product Marketing Data\SeoFriendlyTitle.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\Product Marketing Data\Wordmark.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\Product Metadata.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\Product Metadata\Is Deleted.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\Product Personalization.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\Product Personalization\PMI Membership.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\Product Personalization\PMI Segment.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\Product Pricing.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\Product Pricing\AB Prices.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\Product Pricing\Country Prices.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\Product Pricing\Global Price.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\Product Pricing\Group Prices.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment><CodeGenData>type=IDictionary&lt;ICustomerGroup, float&gt;</CodeGenData></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\Product Pricing\Renewal Price.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Commerce Product Base\Product Pricing\Zone Prices.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Digital Product Base.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Digital Product Base\Custom Attributes.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Digital Product Base\Custom Attributes\ceu_credit_value.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Digital Product Base\Custom Attributes\language_format.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment><CodeGenData>type=Pmi.Spx.Foundation.Connect.TemplateModels.IProductAttributeValue</CodeGenData></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Digital Product Base\Custom Attributes\leadership_pdus.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Digital Product Base\Custom Attributes\length_of_course.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Digital Product Base\Custom Attributes\level_of_course.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment><CodeGenData>type=Pmi.Spx.Foundation.Connect.TemplateModels.IProductAttributeValue&amp;name=CourseLevel</CodeGenData></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Digital Product Base\Custom Attributes\pdus.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Digital Product Base\Custom Attributes\strategic_business_pdus.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Digital Product Base\Custom Attributes\technical_pdus.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Digital Product Base\General product information.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Digital Product Base\General product information\AssociatedCertifications.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Digital Product Base\General product information\Byline.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Hierarchy Base.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Hierarchy Base\Hierarchy Levels.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Hierarchy Base\Hierarchy Levels\level_2_hierarchy.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization><CodeGenData>type=Pmi.Spx.Foundation.Connect.TemplateModels.IProductAttributeValue</CodeGenData></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Hierarchy Base\Hierarchy Levels\level_3_hierarchy.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization><CodeGenData>type=Pmi.Spx.Foundation.Connect.TemplateModels.IProductAttributeValue</CodeGenData></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Hierarchy Base\Hierarchy Levels\level_4_hierarchy.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization><CodeGenData>type=Pmi.Spx.Foundation.Connect.TemplateModels.IProductAttributeValue</CodeGenData></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Matching.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Matching\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Matching\Matching Settings.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Matching\Matching Settings\Product Kind.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment><CodeGenData>name=ProductKind</CodeGenData></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Matching\Matching Settings\Product Type.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment><CodeGenData>name=ProductType</CodeGenData></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates.item"><ChildItemSynchronization>KeepDirectDescendantsSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Book.item"><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Book\Book Custom Attributes.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Book\Book Custom Attributes\author_name.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Book\Book Custom Attributes\book_format.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Book\Book Custom Attributes\date_published.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Book\Book Custom Attributes\isbn13_number.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Book\Book Custom Attributes\language_format.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment><CodeGenData>type=IProductAttribute</CodeGenData></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Book\Book Custom Attributes\number_pages.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Book\Book Custom Attributes\preorderable.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Book\Book Custom Attributes\publisher_name.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Book\Book Custom Attributes\subject.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Book\Book Custom Attributes\tax_class_id.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Certification.item"><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Certification\Certification Custom Attributes.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Certification\Certification Custom Attributes\credential.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Certification\Certification Custom Attributes\examtype.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Certification\Certification Custom Attributes\ordertype.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Certification\Certification Custom Attributes\retake.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Certification\Certification Custom Attributes\studentbundle.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Chapter.item"><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Chapter\Chapter Custom Attributes.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Chapter\Chapter Custom Attributes\chapter_city.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Chapter\Chapter Custom Attributes\chapter_code.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Chapter\Chapter Custom Attributes\chapter_email.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Chapter\Chapter Custom Attributes\chapter_phone.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Chapter\Chapter Custom Attributes\chapter_state.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Chapter\Chapter Custom Attributes\charter_status.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Chapter\Chapter Custom Attributes\charter_year.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Chapter\Chapter Custom Attributes\country.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Chapter\Chapter Custom Attributes\region.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Chapter\Chapter Custom Attributes\url.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Digital Product.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Digital Product\Product Custom Attributes.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Digital Product\Product Custom Attributes\Details.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Donation.item"><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Donation\Donation Custom Attributes.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Donation\Donation Custom Attributes\donation_fixed_amount.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Donation\Donation Custom Attributes\donation_max_amount.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Donation\Donation Custom Attributes\experius_donation_min_amount.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\eBook.item"><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\eBook\eBook Custom Attributes.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\eBook\eBook Custom Attributes\author_name.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\eBook\eBook Custom Attributes\book_format.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\eBook\eBook Custom Attributes\date_published.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\eBook\eBook Custom Attributes\eisbn13_number.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\eBook\eBook Custom Attributes\language_format.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment><CodeGenData>type=IProductAttribute</CodeGenData></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\eBook\eBook Custom Attributes\preorderable.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\eBook\eBook Custom Attributes\publisher_name.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\eBook\eBook Custom Attributes\subject.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\eBook\eBook Custom Attributes\tax_class_id.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\eLearning.item"><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\eLearning\eLearning Custom Attributes.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\eLearning\eLearning Custom Attributes\format.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\eLearning\eLearning Custom Attributes\lms_code.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\eLearning\eLearning Custom Attributes\pmi_elearning_provider.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment><CodeGenData>type=Pmi.Spx.Foundation.Connect.TemplateModels.IProductAttribute</CodeGenData></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\eLearning\eLearning Custom Attributes\who_should_attend.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\eLearning\General product information.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\eLearning\General product information\OverviewLanguages.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Event.item"><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Event\Event Custom Attributes.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Event\Event Custom Attributes\event_dates.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Event\Event Custom Attributes\event_email.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Event\Event Custom Attributes\event_location.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Event\Event Custom Attributes\event_name.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Event\Event Custom Attributes\event_policy.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Event\Event Custom Attributes\event_tickets.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Event\Event Custom Attributes\target_audience.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Membership.item"><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Membership\Membership Custom Attributes.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Membership\Membership Custom Attributes\is_studentmembership.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Membership\Membership Custom Attributes\opt_in_for_auto_renew.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Membership\Membership Custom Attributes\validation.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\REP Course.item"><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\REP Course\REP Course Custom Attributes.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\REP Course\REP Course Custom Attributes\course_level.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\REP Course\REP Course Custom Attributes\format.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\REP Course\REP Course Custom Attributes\pdus.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\REP Course\REP Course Custom Attributes\rep_provider_id.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\REP Course\REP Course Custom Attributes\rep_provider_name.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\REP Member.item"><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Webinar.item"><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Webinar\Webinar Course Custom Attributes.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Webinar\Webinar Course Custom Attributes\course_level.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Webinar\Webinar Course Custom Attributes\format.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Products\Product Specific Templates\Webinar\Webinar Course Custom Attributes\pdus.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Settings.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Settings\Connect Settings.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Settings\Connect Settings\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Settings\Product Template Mapping.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Settings\Product Template Mapping\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Settings\Product Template Mapping\Mapping Settings.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Settings\Product Template Mapping\Mapping Settings\Product Template Headless.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment><CodeGenData>nullable=true</CodeGenData></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Settings\Product Template Mapping\Mapping Settings\Product Template.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment><CodeGenData>nullable=true</CodeGenData></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Settings\Product Template Mappings.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Settings\Product Template Mappings\__Standard Values.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Tokens.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Tokens\_With Category.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Foundation\Connect\Tokens\_With Product.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\System.item"><ItemDeployment>NeverDeploy</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\System\Item Buckets.item"><ItemDeployment>NeverDeploy</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\System\Item Buckets\Facet.item"><ItemDeployment>NeverDeploy</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\System\Item Buckets\Facet\Facet.item"><ItemDeployment>NeverDeploy</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\System\Item Buckets\Facet\Facet\Client Side Handle.item"><ItemDeployment>NeverDeploy</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\System\Item Buckets\Facet\Facet\DisplayName.item"><ItemDeployment>NeverDeploy</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\System\Item Buckets\Facet\Facet\Enabled.item"><ItemDeployment>NeverDeploy</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\System\Item Buckets\Facet\Facet\Global.item"><ItemDeployment>NeverDeploy</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\System\Item Buckets\Facet\Facet\Minimum Number of Items.item"><ItemDeployment>NeverDeploy</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\System\Item Buckets\Facet\Facet\Name.item"><ItemDeployment>NeverDeploy</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\System\Item Buckets\Facet\Facet\Parameters.item"><ItemDeployment>NeverDeploy</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\System\Item Buckets\Facet\Facet\Type.item"><ItemDeployment>NeverDeploy</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\code\Pmi.Spx.Foundation.Connect.csproj">
      <Project>{60cd1765-857b-4bc8-831e-7526264f1ec5}</Project>
      <Name>Pmi.Spx.Foundation.Connect</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <CodeGenTemplate Include="..\..\..\..\t4templates\glass\GeneralExtensions.tt"><Link>Code Generation Templates\GeneralExtensions.tt</Link>
    </CodeGenTemplate><CodeGenTemplate Include="..\..\..\..\t4templates\glass\GlassV5Header.tt"><Link>Code Generation Templates\GlassV5Header.tt</Link>
    </CodeGenTemplate><CodeGenTemplate Include="..\..\..\..\t4templates\glass\GlassV5Item.tt"><Link>Code Generation Templates\GlassV5Item.tt</Link>
    </CodeGenTemplate><CodeGenTemplate Include="..\..\..\..\t4templates\glass\Helpers.tt"><Link>Code Generation Templates\Helpers.tt</Link>
    </CodeGenTemplate><CodeGenTemplate Include="..\..\..\..\t4templates\glass\Inflector.tt"><Link>Code Generation Templates\Inflector.tt</Link>
    </CodeGenTemplate><CodeGenTemplate Include="..\..\..\..\t4templates\glass\StringExtensions.tt"><Link>Code Generation Templates\StringExtensions.tt</Link>
    </CodeGenTemplate></ItemGroup>
  <ItemGroup>
    <Content Include="packages.config" />
  </ItemGroup>
  <Import Project="..\..\..\..\packages\HedgehogDevelopment.TDS.6.0.0.34\build\HedgehogDevelopment.TDS.targets" Condition="Exists('..\..\..\..\packages\HedgehogDevelopment.TDS.6.0.0.34\build\HedgehogDevelopment.TDS.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\..\..\..\packages\HedgehogDevelopment.TDS.6.0.0.34\build\HedgehogDevelopment.TDS.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\..\..\packages\HedgehogDevelopment.TDS.6.0.0.34\build\HedgehogDevelopment.TDS.targets'))" />
  </Target>
</Project>
﻿@using Pmi.Spx.Foundation.Framework.Services
@using Sitecore.Mvc
@using System.Web.Optimization
@using Pmi.Spx.Foundation.Analytics.Constants
@using Pmi.Spx.Foundation.Security.Extensions
@using Pmi.Spx.Foundation.Security.Models.LoginInfoModels
@using Pmi.Spx.Foundation.Security.Services
@using Microsoft.Extensions.DependencyInjection;
@using SpxHelpers = Pmi.Spx.Foundation.Framework.Helpers
@model Pmi.Spx.Foundation.Connect.TemplateModels.IContent_Page
@{
    ViewBag.Title = Model.Short_Title;
    Layout = "~/Views/Shared/_Layout.cshtml";
    var isExperienceEditor = Sitecore.Context.PageMode.IsExperienceEditor;
}
@if (isExperienceEditor)
{
    <style type="text/css">

        div.scLooseFrameZone {
            overflow-y: unset;
        }
    </style>
}

@Html.Partial("~/Views/SPX/Shared/StylesInitializer.cshtml")
<link href="@AssetMap.Instance.MapCssAssetName("main.min.css")" rel="stylesheet" type="text/css" />
<link href="/css/products.css?v=@SpxHelpers.AssemblyHelpers.BuildTime" rel="stylesheet" type="text/css" />
<link href="@AssetMap.Instance.MapSpxAssetName("base-client.css")" rel="stylesheet" type="text/css" />
@Styles.Render("~/assets/storestylesheetbundle")
@Html.Partial(LogRocketConstants.LogRocketInitializerPartial)
<script src="@AssetMap.Instance.MapSpxAssetName("runtime.js")"></script>
<script src="@AssetMap.Instance.MapSpxAssetName("vendors.js")"></script>
<script src="@AssetMap.Instance.MapSpxAssetName("dsm-react.js")"></script>
<script src="@AssetMap.Instance.MapSpxAssetName("base-client.js")"></script>
<script defer src="@AssetMap.Instance.MapSpxAssetName("mvc-client.js")"></script>

@if (Sitecore.Security.Accounts.User.Current.IsAuthenticated)
{
    var fullName = Sitecore.Security.Accounts.User.Current.Profile.FullName;
    var localName = Sitecore.Security.Accounts.User.Current.LocalName;
    var userName = Sitecore.Security.Accounts.User.Current.Name.Replace(@"\", @"\\");
    var email = localName;

    var user = Sitecore.DependencyInjection.ServiceLocator.ServiceProvider.GetService<IUserService>();
    var membershipType = "";
    var customerGroup = "";
    var currentMembership = default(MembershipInformation);
    var isLoggedIn = user != null && user.IsUserAuthenticated();
    if (isLoggedIn)
    {
        var loginInfo = user.GetLoginInfo();
        if (loginInfo != null)
        {
            currentMembership = loginInfo.GetCurrentMembershipInfo();
        }
    }
    if (currentMembership != null)
    {
        membershipType = currentMembership.MembershipType;
        customerGroup = currentMembership.GetCustomerGroupName();
    }

    <script>
        //TODO: refactor this:
        window["spx_user"] = { "email": "@email", "fullName": "@fullName", "localName": "@localName", "userName": "@userName", "membership": { "membershipType": "@membershipType", "customerGroup": "@customerGroup" } };
    </script>
}
<div class="dsm">
    <div class="container-fluid">
        <!-- Breadcrumbs -->
        <div class="breadcrumbs">
            @Html.Sitecore().Placeholder("breadcrumb")
        </div>
    </div>
</div>
<div id="main-content" class="dsm">
    <div class="container-fluid" style="background: var(--spx-store-homepage-background);">
        <div class="row relative">
            <div class="col-xs-12 col-sm-8">
                @Html.Sitecore().Placeholder("title")
                <script type="text/javascript">
                    var lazy = document.getElementsByClassName('lazy-ad');

                    for (var i = 0; i < lazy.length; i++) {
                        lazy[i].src = lazy[i].getAttribute('data-src');
                    }
                </script>
            </div>
            <!-- Social share -->
            <div class="col-sm-4 social-row">
                <div class="social-wrapper">
                    @Html.Sitecore().Placeholder("social")
                </div>
            </div>
        </div>
        <div class="row relative">
            @Html.Sitecore().Placeholder("main")
        </div>
    </div>
</div>
<div class="dsm">
    <div class="container-fluid" style="background: var(--spx-store-homepage-background);">
        @Html.Sitecore().Placeholder("spx-footer")
    </div>
</div>
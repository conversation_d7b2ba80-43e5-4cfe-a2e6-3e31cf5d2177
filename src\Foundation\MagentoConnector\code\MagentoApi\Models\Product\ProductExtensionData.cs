﻿using Newtonsoft.Json;
using System.Collections.Generic;
using Pmi.Spx.Foundation.MagentoConnector.MagentoApi.Models.Product.Price;

namespace Pmi.Spx.Foundation.MagentoConnector.MagentoApi.Models.Product
{
    public class ProductExtensionData
    {
        [JsonProperty("ab_prices")]
        public IList<ABPrice> ABPrices { get; set; } = new List<ABPrice>();

        [JsonProperty("category_links")]
        public IList<CategoryLink> CategoryLinks { get; set; } = new List<CategoryLink>();

        [JsonProperty("stock_item")]
        public ProductStockItem ProductStockItem { get; set; }

        [JsonProperty("bundle_product_options")]
        public IList<BundleProductOption> BundleProductOptions { get; set; } = new List<BundleProductOption>();

        [JsonProperty("custom_product_options")]
        public IList<BundleProductOption> CustomProductOptions { get; set; } = new List<BundleProductOption>();

        [JsonProperty("website_ids")]
        public IList<int> WebsiteIds { get; set; } = new List<int>();

        [JsonProperty("zone_prices")]
        public IList<ProductZonePriceData> ZonePrices { get; set; } = new List<ProductZonePriceData>();

        [JsonProperty("country_prices")]
        public IList<ProductCountryPriceData> CountryPrices { get; set; } = new List<ProductCountryPriceData>();

        [JsonProperty("renewal_price")]
        public float RenewalPrice { get; set; }
    }
}
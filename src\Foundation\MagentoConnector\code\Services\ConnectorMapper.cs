namespace Pmi.Spx.Foundation.MagentoConnector.Services
{
    using Newtonsoft.Json;
    using Pmi.Spx.Foundation.Commerce.Models.Cart;
    using Pmi.Spx.Foundation.Connect.Constants;
    using Pmi.Spx.Foundation.Framework.Services;
    using Pmi.Spx.Foundation.Framework.Services.Logging;
    using Pmi.Spx.Foundation.MagentoConnector.Configuration;
    using Pmi.Spx.Foundation.MagentoConnector.Constants;
    using Pmi.Spx.Foundation.MagentoConnector.MagentoApi.Models;
    using Pmi.Spx.Foundation.MagentoConnector.MagentoApi.Models.Product;
    using Pmi.Spx.Foundation.MagentoConnector.MagentoApi.Models.ProductAttributes;
    using Pmi.Spx.Foundation.Payment.Constants;
    using Pmi.Spx.Foundation.Payment.Extensions;
    using Pmi.Spx.Foundation.Payment.JsModels;
    using Sitecore.Commerce.Entities.Carts;
    using Sitecore.Commerce.Entities.Prices;
    using Sitecore.Commerce.Entities.Products;
    using Sitecore.Diagnostics;
    using System;
    using System.Collections.Generic;
    using System.Globalization;
    using System.Linq;
    using BundleOption = Pmi.Spx.Foundation.MagentoConnector.MagentoApi.Models.BundleOption;
    using FastOrderData = Commerce.Models.Order.FastOrderData;

    public class ConnectorMapper : IConnectorMapper
    {
        private const string BundleProductTypeLabel = "bundle";

        private readonly IMagentoConfiguration magentoConfiguration;
        private readonly IUrlUtility urlUtility;
        private readonly ILogger logger;

        public ConnectorMapper(IMagentoConfiguration magentoConfiguration, IUrlUtility urlUtility, ILoggerFactory loggerFactory)
        {
            Assert.IsNotNull(magentoConfiguration, nameof(magentoConfiguration));
            Assert.IsNotNull(urlUtility, nameof(urlUtility));
            Assert.IsNotNull(loggerFactory, nameof(loggerFactory));
            this.magentoConfiguration = magentoConfiguration;
            this.urlUtility = urlUtility;
            this.logger = loggerFactory.GetLogger(this);
        }

        public Commerce.Models.Product.Product MapProduct(MagentoApi.Models.Product.Product product)
        {
            var customData = product.CustomData;
            var extensionData = product.ExtensionData;

            return new Commerce.Models.Product.Product
            {
                Status = (Commerce.Models.Product.ProductStatus)product.Status,
                ExternalId = product.SKU,
                Name = product.Name,
                DisplayName = product.Name,
                ProductAttributeTypeId = product.AttributeSetId,
                CustomFields = customData?.CustomAttributes,
                ProductTypeId = product.TypeId,
                Created = product.CreatedAt,
                Updated = product.UpdatedAt,
                GlobalPrice = product.GlobalPrice,
                FullDescription = customData?.ShortDescription,
                IsInStock = customData?.CustomAttributes.ContainsKey("is_in_stock") == true && Convert.ToBoolean(customData.CustomAttributes["is_in_stock"]),
                Quantity = customData?.CustomAttributes.ContainsKey("qty") == true ? Convert.ToInt64(customData.CustomAttributes["qty"]) : 0L,
                ClassificationGroups = new List<ClassificationGroup>()
                {
                    new ClassificationGroup()
                    {
                        Classifications = extensionData?.CategoryLinks.Select(category =>
                            new Classification()
                            {
                                ExternalId = category.CategoryId
                            }
                        ).ToList()
                    }
                },
                RelationTypes = MapRelationTypes(extensionData?.BundleProductOptions),
                CustomerPrices = product.CustomerPrices?.Select(_ => this.Map(_)).ToList(),
                Image = this.GetProductImageUrl(customData?.Image),
                SmallImage = this.GetProductImageUrl(customData?.SmallImage),
                Thumbnail = this.GetProductImageUrl(customData?.Thumbnail),
                StoreIds = extensionData?.WebsiteIds.ToList(),
                ProductBundleOptions = JsonConvert.SerializeObject(extensionData?.BundleProductOptions),
                ProductCustomOptions = JsonConvert.SerializeObject(product.Options),
                ZonePrices = JsonConvert.SerializeObject(extensionData?.ZonePrices),
                CountryPrices = JsonConvert.SerializeObject(extensionData?.CountryPrices),
                RenewalPrice = extensionData.RenewalPrice,
                ABPrices = JsonConvert.SerializeObject(extensionData?.ABPrices),
            };
        }

        public Commerce.Models.Product.CustomerPrice Map(MagentoApi.Models.Customer.CustomerPrice source, Commerce.Models.Product.CustomerPrice destination = null)
        {
            destination = destination ?? new Commerce.Models.Product.CustomerPrice();
            destination.CustomerGroupId = source.CustomerGroupId;
            destination.Price = source.Price;
            destination.Quantity = source.Quantity;
            return destination;
        }

        public Commerce.Models.Product.Classification MapClassification(MagentoApi.Models.Category category)
        {
            return new Commerce.Models.Product.Classification()
            {
                ExternalId = category.Id.ToString(),
                ExternalParentId = category.ParentId.ToString(),
                Name = category.Name,
                Created = category.CreatedAt,
                Updated = category.UpdatedAt,
                IncludeInMenu = category.IncludeInMenu,
                IsActive = category.IsActive,
                Position = category.Position,
                DisplayMode = category.CustomData.DisplayMode,
            };
        }

        private static List<RelationType> MapRelationTypes(IList<BundleProductOption> bundleProductOptions)
        {
            if(bundleProductOptions == null) return new List<RelationType>();

            var relations = bundleProductOptions.SelectMany(opt => opt.ProductLinks, (_, link) => link.Sku)
                                                .Distinct()
                                                .Select(sku => new Relation { ExternalId = sku })
                                                .ToList();

            var relationType = new RelationType
            {
                Name = ProductConstants.ProductBundleItemsItemName, 
                Relations = relations
            };
            
            var relationTypes = new List<RelationType> { relationType };

            return relationTypes;
        }

        private string GetProductImageUrl(string relativeImageUrl)
        {
            return string.IsNullOrEmpty(relativeImageUrl)
                ? null
                : this.urlUtility.JoinUrlParts(this.magentoConfiguration.BaseUrl, this.magentoConfiguration.ProductImageUrl, relativeImageUrl);
        }

        public Commerce.Models.Cart.Cart MapCart(MagentoApi.Models.Cart cart)
        {
            var commerceCart = new Commerce.Models.Cart.Cart()
            {
                Id = cart.Id,
                CreatedAt = cart.CreatedAt,
                UpdatedAt = cart.UpdatedAt,
                IsActive = cart.IsActive,
                IsVirtual = cart.IsVirtual,
                ItemsCount = cart.ItemsCount,
                ItemsQuantity = cart.ItemsQuantity,
                StoreId = cart.StoreId,
                CurrencyCode = string.IsNullOrWhiteSpace(cart.Currency.StoreCurrencyCode) ? cart.Currency.GlobalCurrencyCode : cart.Currency.StoreCurrencyCode,
                LocalCurrencyCode = !string.IsNullOrWhiteSpace(cart.LocalCurrencyCode) ? cart.LocalCurrencyCode : string.Empty,
                StoreCurrencyCode = cart.Currency.StoreCurrencyCode,
                CouponCode = cart.CouponCode,
                VoucherCode = cart.CartExtensionAttributes?.TotalsAttributes?.VoucherCode,
                Total = new Commerce.Models.Cart.CartTotal()
                {
                    GrandTotal = cart.GrandTotal,
                    BaseGrandTotal = cart.BaseGrandTotal,
                    SubTotal = cart.SubTotal,
                    BaseSubtotal = cart.BaseSubtotal,
                    DiscountAmount = cart.DiscountAmount,
                    BaseDiscountAmount = cart.BaseDiscountAmount,
                    SubtotalWithDiscount = cart.SubtotalWithDiscount,
                    BaseSubtotalWithDiscount = cart.BaseSubtotalWithDiscount,
                    ShippingAmount = cart.ShippingAmount,
                    BaseShippingAmount = cart.BaseShippingAmount,
                    ShippingDiscountAmount = cart.ShippingDiscountAmount,
                    BaseShippingDiscountAmount = cart.BaseShippingDiscountAmount,
                    BaseCurrencyCode = cart.BaseCurrencyCode,
                    QuoteCurrencyCode = cart.QuoteCurrencyCode,
                    TotalCartAmount = cart.CartExtensionAttributes?.TotalsAttributes?.TotalCartAmount ?? 0,
                    Amount = cart.CartExtensionAttributes?.TotalsAttributes?.TotalCartAmount ?? 0,
                    TotalCartDiscountAmount = cart.CartExtensionAttributes?.TotalsAttributes?.TotalCartDiscountAmount ?? 0,
                    VoucherDiscountAmount = cart.VoucherDiscountAmount,
                    TotalGrossLocalCurrencyAmount = cart.TotalGrossLocalCurrencyAmount,
                    TaxTotal = new Commerce.Models.Cart.CartTaxTotal
                    {
                        TaxAmount = cart.TaxAmount,
                        BaseTaxAmount = cart.BaseTaxAmount,
                        WeeeTaxAppliedAmount = cart.WeeeTaxAppliedAmount,
                        ShippingTaxAmount = cart.ShippingTaxAmount,
                        BaseShippingTaxAmount = cart.BaseShippingTaxAmount,
                        SubtotalInclTax = cart.SubtotalInclTax,
                        ShippingInclTax = cart.ShippingInclTax,
                        BaseShippingInclTax = cart.BaseShippingInclTax,
                        InvalidTax = cart.InvalidTax,
                    }
                },
                CustomData = this.MapCustomData(cart.CartExtensionAttributes?.CartAttributes?.CustomData),
                BillingAddress = this.MapShippingOrBillingAddress(cart.BillingAddress),
                PaymentStatus = cart.CartExtensionAttributes?.CartAttributes?.PaymentStatus,
                BillToCompanyId = cart.CartExtensionAttributes?.CartAttributes?.BillToCompanyId,
                GiftRecipientAddress = this.MapGiftRecipientAddress(cart.CartExtensionAttributes?.CartAttributes?.GiftRecipientInformation),
                GiftRecipientEmail = cart.CartExtensionAttributes?.CartAttributes?.GiftRecipientInformation?.EmailAddress,
                GiftRecipientNote = cart.CartExtensionAttributes?.CartAttributes?.GiftRecipientInformation?.Note,
                ReservedOrderId = cart.ReservedOrderId,
                LockStatus = cart.CartExtensionAttributes?.CartAttributes?.LockStatus ?? 0
            };

            var shippingAssignment = cart.CartExtensionAttributes?.CartAttributes?.ShippingAssignments?.FirstOrDefault();
            var shippingAddress = shippingAssignment?.ShippingAddress?.AddressData;
            commerceCart.ShippingAddress = shippingAddress != null ? this.MapShippingOrBillingAddress(shippingAssignment.ShippingAddress.AddressData) 
                                                                   : new Commerce.Models.Order.Address();
            foreach(var cartItem in cart.Items)
            {
                commerceCart.AddCartLine(this.MapCartLine(cartItem, commerceCart.CurrencyCode));
            }

            this.MapCartTotals(commerceCart, cart);

            return commerceCart;
        }

        private IEnumerable<Commerce.Models.Cart.ProductOption> MapCustomData(string data)
        {
            if(string.IsNullOrEmpty(data))
            {
                return Enumerable.Empty<Commerce.Models.Cart.ProductOption>();
            }

            return JsonConvert
                .DeserializeObject<IEnumerable<KeyValuePair<string, string>>>(data)
                .Select(pair => new Commerce.Models.Cart.ProductOption { Title = pair.Key, Value = pair.Value })
                .ToArray();
        }

        private void MapCartTotals(Commerce.Models.Cart.Cart commerceCart, MagentoApi.Models.Cart cartTotals)
        {
            var giftCardTotalSegment = cartTotals.TotalSegments?.FirstOrDefault(x => x.IsGiftCardAccount);
            if(giftCardTotalSegment != null)
            {
                var giftCardExtensionAttributes = Newtonsoft.Json.JsonConvert.DeserializeObject<IList<MagentoApi.Models.CartGiftCardExtensionAttributesItem>>(giftCardTotalSegment.ExtensionAttributes.GiftCards);
                commerceCart.GiftCardTotal = new Commerce.Models.Cart.CartGiftCardTotal()
                {
                    Title = giftCardTotalSegment.Title,
                    TotalValue = giftCardTotalSegment.Value.Value,
                    GiftCardItems = giftCardExtensionAttributes.Select(x =>
                    new Commerce.Models.Cart.CartGiftCardItem()
                    {
                        GiftCardCode = x.GiftCardCode,
                        GiftCardId = x.GiftCardId,
                        GiftCardValue = x.GiftCardValue
                    }).ToList()
                };
            }
        }

        public Commerce.Models.Cart.CartLine MapCartLine(MagentoApi.Models.CartLineItem cartLineItem, string currencyCode = "USD")
        {
            return new Commerce.Models.Cart.CartLine()
            {
                ItemId = cartLineItem.ItemId,
                Sku = cartLineItem.Sku,
                Quantity = cartLineItem.Quantity,
                QuantityOrdered = cartLineItem.QuantityOrdered,
                Name = cartLineItem.Name,
                SelectedBundleOptions = cartLineItem.CartLineItemExtensionAttributes?.SelectedBundleOptions?.Select(o => MapBundleOption(o)).ToList(),
                AddedProductLineOptions = MapProductLineBundleOptionsFromExtensions(cartLineItem?.ProductLineOption?.ExtensionAttributes),
                GiftRecipient = MapGiftRecipient(cartLineItem?.ProductLineOption?.ExtensionAttributes?.GiftRecipient),
                Total = new Commerce.Models.Cart.CartLineTotal()
                {
                    Price = cartLineItem.Price,
                    BasePrice = cartLineItem.BasePrice,
                    RowTotal = cartLineItem.RowTotal,
                    BaseRowTotal = cartLineItem.BaseRowTotal,
                    RowTotalWithDiscount = cartLineItem.RowTotalWithDiscount,
                    DiscountAmount = cartLineItem.ProductType == BundleProductTypeLabel ? 
                        SetDiscountAmountForBundleProduct(cartLineItem.DiscountAmount, cartLineItem.CartLineItemExtensionAttributes?.BundleDiscountAmount ?? 0) : 
                        cartLineItem.DiscountAmount,
                    BaseDiscountAmount = cartLineItem.ProductType == BundleProductTypeLabel ? 
                        SetDiscountAmountForBundleProduct(cartLineItem.BaseDiscountAmount, cartLineItem.CartLineItemExtensionAttributes?.BundleBaseDiscountAmount ?? 0) :
                        cartLineItem.BaseDiscountAmount,
                    DiscountPercent = cartLineItem.DiscountPercent,
                    ProductBasePrice = cartLineItem.CartLineItemExtensionAttributes?.ProductBasePrice ?? 0,
                    TotalRowDiscountAmount = cartLineItem.CartLineItemExtensionAttributes?.TotalRowDiscountAmount ?? 0,
                    DiscountPercentage = cartLineItem.CartLineItemExtensionAttributes?.DiscountPercentage ?? 0,
                    TotalProductDiscountAmount = cartLineItem.CartLineItemExtensionAttributes?.TotalProductDiscountAmount ?? 0,
                    OrderRowTotal = cartLineItem.CartLineItemExtensionAttributes?.OrderRowTotal ?? 0,
                    TaxTotal = new Commerce.Models.Cart.CartLineTaxTotal()
                    {
                        TaxAmount = cartLineItem.TaxAmount,
                        BaseTaxAmount = cartLineItem.BaseTaxAmount,
                        TaxPercent = cartLineItem.TaxPercent,
                        PriceInclTax = cartLineItem.PriceInclTax,
                        BasePriceInclTax = cartLineItem.BasePriceInclTax,
                        RowTotalInclTax = cartLineItem.RowTotalInclTax,
                        BaseRowTotalInclTax = cartLineItem.BaseRowTotalInclTax,
                        WeeeTaxAppliedAmount = cartLineItem.WeeeTaxAppliedAmount,
                        WeeeTaxApplied = cartLineItem.WeeeTaxApplied,
                    }
                },
                QuoteId = cartLineItem.QuoteId,
                Product = new CartProduct
                {
                    ProductId = cartLineItem.Sku,
                    Price = new Price(cartLineItem.Price, currencyCode),
                    ProductName = cartLineItem.Name,
                },
                FulfillmentProvider = cartLineItem.CartLineItemExtensionAttributes?.FulfillmentProvider
            };
        }

        public MagentoApi.Models.AddCartItem MapAddCartLine(Commerce.Models.Cart.CartLine cartLine)
        {
            return new MagentoApi.Models.AddCartItem()
            {
                CartItem = this.MapCartLineItem(cartLine)
            };
        }

        public AddCartItems MapAddCartLines(IEnumerable<Commerce.Models.Cart.CartLine> cartLines)
        {
            return new AddCartItems()
            {
                QuoteId = cartLines.FirstOrDefault().QuoteId,
                CartItems = cartLines.Select(this.MapCartLineItem).ToList()
            };
        }

        private AddCartLineItem MapCartLineItem(Commerce.Models.Cart.CartLine cartLine)
        {
            return new MagentoApi.Models.AddCartLineItem()
            {
                Sku = cartLine.Sku.ToUpper(CultureInfo.InvariantCulture),
                Quantity = Convert.ToInt32(cartLine.Quantity),
                QuoteId = cartLine.QuoteId,
                ProductOption = new AddCartLineItemProductOption
                {
                    ExtensionAttributes = new AddCartLineItemExtensionAttributes
                    {
                        BundleOptions = new List<MagentoApi.Models.AddCartLineItemBundleOption>(),
                        CustomOptions = new List<MagentoApi.Models.AddCartLineItemOption>()
                    }
                }, 
                ExtensionAttributes = new AddCartLineExtensionAttributes()
            };
        }

        public AddDonationCartItem MapAddDonationCartLine(Commerce.Models.Cart.DonationCartLine cartLine)
        {
            return new MagentoApi.Models.AddDonationCartItem()
            {
                CartItem = new MagentoApi.Models.AddDonationCartLineItem()
                {
                    Sku = cartLine.Sku,
                    Quantity = 1,
                    QuoteId = cartLine.QuoteId,
                    ProductOption = new MagentoApi.Models.AddDonationCartItemProductOption()
                    {
                        ExtensionAttributes = new MagentoApi.Models.AddDonationCartItemExtensionAttributes()
                        {
                            DonationOptions = new MagentoApi.Models.AddDonationCartItemDonationOptions()
                            {
                                Amount = cartLine.DonationAmount
                            }
                        }
                    }
                }
            };
        }

        public MagentoApi.Models.UpdateCartItem MapUpdateCartLine(Commerce.Models.Cart.CartLine cartLine)
        {
            return new MagentoApi.Models.UpdateCartItem()
            {
                CartItem = new MagentoApi.Models.UpdateCartLineItem()
                {
                    Quantity = Convert.ToInt32(cartLine.Quantity),
                    QuoteId = cartLine.QuoteId,
                    Sku = cartLine.Sku,
                    ProductOption = new AddCartLineItemProductOption
                    {
                        ExtensionAttributes = new AddCartLineItemExtensionAttributes
                        {
                            BundleOptions = new List<MagentoApi.Models.AddCartLineItemBundleOption>()
                        }
                    }
                }
            };
        }

        public Commerce.Models.Product.ProductPrice MapProductPrice(MagentoApi.Models.ProductPrice productPrice)
        {
            return new Commerce.Models.Product.ProductPrice()
            {
                Sku = productPrice.Sku,
                Id = productPrice.Id,
                BasePrice = productPrice.BasePrice,
                CustomerGroupPrice = productPrice.CustomerGroupPrice,
                FinalPrice = productPrice.FinalPrice,
            };
        }

        public Commerce.Models.Order.PaymentMethod MapPaymentMethod(MagentoApi.Models.PaymentMethod paymentMethod)
        {
            return new Commerce.Models.Order.PaymentMethod()
            {
                Code = paymentMethod.Code,
                Title = paymentMethod.Title,
            };
        }

        public SetAddressesRequest MapSetShippingAddressRequest(Address shippingAddress, ShippingMethod shippingMethod, Address billingAddress, string customerIpAddress)
        {
            return new SetAddressesRequest
            {
                AddressesInformation = new AddressesInformation
                {
                    ShippingAddress = shippingAddress,
                    ShippingMethodCode = shippingMethod.MethodCode,
                    ShippingCarrierCode = shippingMethod.CarrierCode,
                    BillingAddress = billingAddress,
                    ExtensionAttributes = new AddressesInformationExtensionAttributes
                    {
                        CustomerIpAddress = customerIpAddress
                    }
                }
            };
        }

        public AddressRequest MapAddressRequest(Address address)
        {
            return new AddressRequest
            {
                Address = address,
            };
        }

        public Commerce.Models.Cart.BundleOption MapBundleOption(BundleOption source, Commerce.Models.Cart.BundleOption destination = null)
        {
            destination = destination ?? new Commerce.Models.Cart.BundleOption();
            destination.OptionId = source.OptionId;
            destination.SelectionId = source.SelectionId;
            destination.SelectionSku = source.SelectionSku;
            destination.SelectionName = source.SelectionName;
            return destination;
        }

        public IEnumerable<Commerce.Models.Cart.BundleOption> MapProductLineBundleOptionsFromExtensions(AddCartLineItemExtensionAttributes source)
        {
            if(source?.BundleOptions == null)
                return Enumerable.Empty<Commerce.Models.Cart.BundleOption>();

            var listoptions = new List<Commerce.Models.Cart.BundleOption>();
            foreach(var item in source.BundleOptions)
            {
                foreach(var optionselection in item.OptionSelections)
                {
                    listoptions.Add(new Commerce.Models.Cart.BundleOption { OptionId = item.OptionId, SelectionId = optionselection });
                }
            }
            return listoptions;
        }

        public Commerce.Models.Order.ShippingMethod Map(ShippingMethod source, Commerce.Models.Order.ShippingMethod destination = null)
        {
            destination = destination ?? new Commerce.Models.Order.ShippingMethod();
            destination.MethodCode = source.MethodCode;
            destination.MethodTitle = source.MethodTitle;
            destination.CarrierCode = source.CarrierCode;
            destination.CarrierTitle = source.CarrierTitle;
            destination.Amount = source.Amount;
            destination.BaseAmount = source.BaseAmount;
            destination.Available = source.Available;
            destination.PriceWithTaxExcluded = source.PriceWithTaxExcluded;
            destination.PriceWithTaxIncluded = source.PriceWithTaxIncluded;
            destination.ErrorMessage = source.ErrorMessage;
            return destination;
        }

        public ShippingMethod MapShippingMethod(Commerce.Models.Order.ShippingMethod shippingMethod)
        {
            return new ShippingMethod
            {
                MethodTitle = shippingMethod.MethodTitle,
                MethodCode = shippingMethod.MethodCode,
                Amount = shippingMethod.Amount,
                Available = shippingMethod.Available,
                BaseAmount = shippingMethod.BaseAmount,
                CarrierCode = shippingMethod.CarrierCode,
                CarrierTitle = shippingMethod.CarrierTitle,
                ErrorMessage = shippingMethod.ErrorMessage,
                PriceWithTaxExcluded = shippingMethod.PriceWithTaxExcluded,
                PriceWithTaxIncluded = shippingMethod.PriceWithTaxIncluded,
            };
        }

        public MagentoApi.Models.SubmitOrderPaymentMethod Map(Payment.Models.TransactionResultModel paymentResult, MagentoApi.Models.SubmitOrderPaymentMethod destination)
        {
            switch(paymentResult.PaymentMethodCode)
            {
                case PaymentMethodCodes.Worldpay:
                    var worldpay = destination as SubmitOrderWorldpay;
                    worldpay.AdditionalData = new SubmitOrderWorldpayData
                    {
                        WorldpayData = paymentResult.PaymentProperties.GetPaymentProperty(PaymentProperties.RawResponse),
                        Token = paymentResult.OrderCode,
                        CardLast4Digits = paymentResult.PaymentProperties.GetPaymentProperty(PaymentProperties.LastFourDigits),
                        CardType = paymentResult.PaymentProperties.GetPaymentProperty(PaymentProperties.CardType),
                        MerchantCode = paymentResult.PaymentProperties.GetPaymentProperty(MagentoPaymentProperties.AuthorizationMerchantCode),
                        AuthorizationStatus = paymentResult.PaymentProperties.GetPaymentProperty(MagentoPaymentProperties.PaymentProviderStatus),
                        AuthorizedAmount = paymentResult.PaymentProperties.GetPaymentProperty(MagentoPaymentProperties.AuthorizedAmount),
                        AuthorizedCurrency = paymentResult.PaymentProperties.GetPaymentProperty(MagentoPaymentProperties.AuthorizedCurrencyCode)
                    };
                    break;
                case PaymentMethodCodes.AlipayCheckout:
                    var alipay = destination as SubmitOrderAlipay;
                    alipay.AdditionalData.Token = paymentResult.OrderCode;
                    alipay.AdditionalData.AuthorizationStatus = paymentResult.PaymentProperties.GetPaymentProperty(MagentoPaymentProperties.PaymentProviderStatus);
                    alipay.AdditionalData.MerchantCode = paymentResult.PaymentProperties.GetPaymentProperty(MagentoPaymentProperties.AuthorizationMerchantCode);
                    alipay.AdditionalData.AuthorizedAmount = paymentResult.PaymentProperties.GetPaymentProperty(MagentoPaymentProperties.AuthorizedAmount);
                    alipay.AdditionalData.AuthorizedCurrency = paymentResult.PaymentProperties.GetPaymentProperty(MagentoPaymentProperties.AuthorizedCurrencyCode);
                    break;
                case PaymentMethodCodes.PaypalCheckout:
                    var paypal = destination as SubmitOrderPaypal; 
                    paypal.AdditionalData.Token = paymentResult.OrderCode;
                    paypal.AdditionalData.AuthorizationStatus = paymentResult.PaymentProperties.GetPaymentProperty(MagentoPaymentProperties.PaymentProviderStatus);
                    paypal.AdditionalData.MerchantCode = paymentResult.PaymentProperties.GetPaymentProperty(MagentoPaymentProperties.AuthorizationMerchantCode);
                    paypal.AdditionalData.AuthorizedAmount = paymentResult.PaymentProperties.GetPaymentProperty(MagentoPaymentProperties.AuthorizedAmount);
                    paypal.AdditionalData.AuthorizedCurrency = paymentResult.PaymentProperties.GetPaymentProperty(MagentoPaymentProperties.AuthorizedCurrencyCode);
                    break;

                case PaymentMethodCodes.PurchaseOrder:
                    var po = destination as SubmitOrderPO;
                    po.PONumber = paymentResult.PaymentProperties.GetPaymentProperty(PaymentProperties.PONumber);
                    break;
            }

            return destination;
        }

        public MagentoApi.Models.SubmitOrderPaymentMethod Map(Commerce.Models.Order.PaymentData paymentData)
        {
            var mappedPaymentData = default(MagentoApi.Models.SubmitOrderPaymentMethod);
            switch(paymentData.Transaction.PaymentMethodCode)
            {
                case PaymentMethodCodes.AlipayCheckout:
                    mappedPaymentData = new SubmitOrderAlipay
                    {
                        AdditionalData = new SubmitOrderAdditionalData()
                    };
                    break;
                case PaymentMethodCodes.PaypalCheckout:
                    mappedPaymentData = new SubmitOrderPaypal
                    {
                        AdditionalData = new SubmitOrderPaypalData
                        {
                            PaypalEmail = paymentData.Transaction.PaymentProperties.GetPaymentProperty("WorldpayPaymentProperties_PaypalEmail", out var paypalEmail) ? paypalEmail : null
                        }
                    };
                    break;
                case PaymentMethodCodes.Worldpay:
                    mappedPaymentData = new SubmitOrderWorldpay();
                    break;
                case PaymentMethodCodes.PurchaseOrder:
                    mappedPaymentData = new SubmitOrderPO
                    {
                        PONumber = paymentData.Transaction.PaymentProperties.GetPaymentProperty(Constants.PipelineParameters.PONumberPropertyKey, out var poNumber) ? poNumber : null
                    };
                    break;
                default:
                    mappedPaymentData = new SubmitOrderPaymentMethod();
                    break;
            }
            mappedPaymentData.PaymentMethodCode = paymentData.Transaction.PaymentMethodCode;
            mappedPaymentData.PaymentMethodAttributes = new PaymentMethodAttributes()
            {
                OrderType = paymentData.OrderType,
                AccountNumber = paymentData.AccountNumber,
                CountryCode = paymentData.Location.CountryCode,
                IpAddress = paymentData.Location.IpAddress,
                RegionCode = paymentData.Location.RegionCode,
                VerifiedCountryCode = paymentData.Location.VerifiedCountryCode,
                VerifiedRegionCode = paymentData.Location.VerifiedRegionCode,
                CustomerEmail = paymentData.Location.CustomerEmail
            };

            return mappedPaymentData;
        }

        public Commerce.Models.Order.Order MapOrder(MagentoApi.Models.Order order)
        {
            var commerceOrder = new Commerce.Models.Order.Order()
            {
                OrderID = order.IncrementId,
                Email = order.CustomerEmail,
                CreatedAt = order.CreatedAt,
                UpdatedAt = order.UpdatedAt,
                StoreId = order.StoreId,
                Status = order.Status,
                CurrencyCode = order.OrderCurrencyCode,
                LocalCurrencyCode = order.LocalCurrencyCode,
                StoreCurrencyCode = order.StoreCurrencyCode,
                PaidWithLocalCurrency = order.PaidWithLocalCurrency,
                CouponCode = order.CouponCode,
                Total = new Commerce.Models.Cart.CartTotal()
                {
                    TotalGrossLocalCurrencyAmount = order.TotalGrossLocalCurrencyAmount,
                    GrandTotal = order.GrandTotal,
                    BaseGrandTotal = order.BaseGrandTotal,
                    SubTotal = order.SubTotal,
                    BaseSubtotal = order.BaseSubtotal,
                    DiscountAmount = order.DiscountAmount,
                    BaseDiscountAmount = order.BaseDiscountAmount,
                    SubtotalWithDiscount = order.SubtotalWithDiscount,
                    BaseSubtotalWithDiscount = order.BaseSubtotalWithDiscount,
                    ShippingAmount = order.ShippingAmount,
                    BaseShippingAmount = order.BaseShippingAmount,
                    ShippingDiscountAmount = order.ShippingDiscountAmount,
                    BaseShippingDiscountAmount = order.BaseShippingDiscountAmount,
                    BaseCurrencyCode = order.BaseCurrencyCode,
                    QuoteCurrencyCode = order.QuoteCurrencyCode,
                    OrderCurrencyCode = order.OrderCurrencyCode,
                    TotalOrderAmount = order.ExtensionData?.TotalOrderAmount ?? 0,
                    TotalOrderDiscountAmount = order.ExtensionData?.TotalOrderDiscountAmount ?? 0,
                    VoucherDiscountAmount = order.Payment?.ExtensionAttributes?.VoucherDiscountAmount,
                    TaxTotal = new Commerce.Models.Cart.CartTaxTotal()
                    {
                        TaxAmount = order.TaxAmount,
                        BaseTaxAmount = order.BaseTaxAmount,
                        WeeeTaxAppliedAmount = order.WeeeTaxAppliedAmount,
                        ShippingTaxAmount = order.ShippingTaxAmount,
                        BaseShippingTaxAmount = order.BaseShippingTaxAmount,
                        SubtotalInclTax = order.SubtotalInclTax,
                        ShippingInclTax = order.ShippingInclTax,
                        BaseShippingInclTax = order.BaseShippingInclTax,
                    }
                },
                ShippingDescription = order.ShippingDescription,
                BillingAddress = this.MapAddress(order.BillingAddress),
                GiftRecipientAddress = this.MapGiftRecipientAddress(order.ExtensionData?.GiftRecipientOrderInformation),
                GiftRecipientEmail = order.ExtensionData?.GiftRecipientOrderInformation?.EmailAddress,
                GiftRecipientNote = order.ExtensionData?.GiftRecipientOrderInformation?.Note,
                GiftRecipientPersonId = order.ExtensionData?.GiftRecipientOrderInformation?.PersonId,
                PdfLink = order.ExtensionData.PdfLink,
                IsVirtual = order.IsVirtual,
                PaymentMethodCode = order.Payment.Method,
                VoucherCode = order.Payment?.ExtensionAttributes?.AppliedVoucherCode,
                CardLast4Digits = order.ExtensionData?.PaymentAdditionalInfo?.CardLast4Digits ?? order.Payment?.CardLast4Digits,
                CardType = order.ExtensionData?.PaymentAdditionalInfo?.CardType ?? order.Payment?.CardType,
                PersonId = order.ExtensionData?.CustomerAttributes?.PersonId ?? 0,
                PaymentProperties = new List<PaymentPropertyModel>
                {
                    new PaymentPropertyModel {
                        Key = Constants.PipelineParameters.PaypalEmailPropertyKey,
                        Value = order.ExtensionData?.PaymentAdditionalInfo?.PaypalEmail
                    }
                },
                CustomData = order.ExtensionData?.CustomData != null ? JsonConvert.DeserializeObject<IList<Commerce.Models.Order.CustomData>>(order.ExtensionData?.CustomData) : null,
                PONumber = order.Payment?.PONumber,
            };

            var shippingAddress = order.ExtensionData.ShippingAssignments.First().Shipping.Address;
            if(shippingAddress != null)
            {
                commerceOrder.ShippingAddress = this.MapAddress(shippingAddress);
            }

            this.MapGiftCard(commerceOrder, order.ExtensionData);

            foreach(var cartItem in order.Items)
            {
                commerceOrder.AddCartLine(this.MapCartLine(cartItem, commerceOrder.CurrencyCode));
            }

            return commerceOrder;
        }

        private void MapGiftCard(Commerce.Models.Order.Order commerceOrder, MagentoApi.Models.OrderExtensionData orderExtensionData)
        {
            if(orderExtensionData.GiftCards.Any())
            {
                commerceOrder.GiftCardTotal = new Commerce.Models.Cart.CartGiftCardTotal()
                {
                    TotalValue = orderExtensionData.GiftCardsAmount,
                    GiftCardItems = orderExtensionData.GiftCards.Select(this.MapGiftCardItem).ToList()
                };
            }
        }

        private Commerce.Models.Cart.CartGiftCardItem MapGiftCardItem(MagentoApi.Models.OrderGiftCardItem orderGiftCardItem)
        {
            return new Commerce.Models.Cart.CartGiftCardItem()
            {
                GiftCardCode = orderGiftCardItem.Code,
                GiftCardId = orderGiftCardItem.Id,
                GiftCardValue = orderGiftCardItem.Amount
            };
        }

        public Commerce.Models.Order.Address MapShippingOrBillingAddress(MagentoApi.Models.Address address)
        {
            var quoteAddress = this.MapAddress(address);
            quoteAddress.State = address.RegionCode;
            return quoteAddress;
        }

        private Commerce.Models.Order.Address MapGiftRecipientAddress(GiftRecipientDetails giftRecipientDetails)
        {
            if (giftRecipientDetails == null) return null;
            var giftRecipientAddress = this.MapAddress(giftRecipientDetails);
            giftRecipientAddress.State = giftRecipientDetails.RegionCode;
            return giftRecipientAddress;
        }

        public Commerce.Models.Order.Address MapAddress(MagentoApi.Models.Address address)
        {
            return new Commerce.Models.Order.Address()
            {
                AddressId = address.ExtensionAttributes?.PmiAddressId,
                Address1 = address.Street.FirstOrDefault(),
                Address2 = address.Street.Count() > 1 ? string.Join(" ", address.Street.Skip(1)) : "",
                City = address.City,
                CountryCode = address.CountryId,
                FirstName = address.FirstName,
                LastName = address.LastName,
                PhoneNumber = address.Telephone,
                State = address.RegionName,
                ZipPostalCode = address.PostCode,
            };
        }

        public MagentoApi.Models.Address MapAddress(Commerce.Models.Order.Address address)
        {
            return new MagentoApi.Models.Address()
            {
                FirstName = address.FirstName,
                LastName = address.LastName,
                Street = new[] { address.Address1, address.Address2 },
                Telephone = address.PhoneNumber,
                RegionCode = address.State,
                PostCode = address.ZipPostalCode,
                CountryId = address.CountryCode,
                City = address.City,
                ExtensionAttributes = new AddressExtensionAttributes
                {
                    PmiAddressId = address.AddressId == 0 ? null : address.AddressId
                }
            };
        }

        public Commerce.Models.Product.ProductType Map(ProductAttributeSet source, Commerce.Models.Product.ProductType destination = null)
        {
            destination = destination ?? new Commerce.Models.Product.ProductType();
            destination.Name = source.Name;
            destination.Id = source.Id;
            destination.ExternalId = source.Id.ToString();
            return destination;
        }

        public Commerce.Models.Product.ProductPropertyGroup Map(ProductAttributeGroup source, Commerce.Models.Product.ProductPropertyGroup destination = null)
        {
            destination = destination ?? new Commerce.Models.Product.ProductPropertyGroup();
            destination.Name = source.Name;
            destination.Id = source.Id;
            destination.ExternalId = source.Id.ToString();
            return destination;
        }

        public Commerce.Models.Product.ProductProperty Map(ProductAttribute source, Commerce.Models.Product.ProductProperty destination = null)
        {
            destination = destination ?? new Commerce.Models.Product.ProductProperty();
            destination.Name = source.Name;
            destination.Code = source.Code;
            destination.Id = source.Id;
            destination.ExternalId = source.Id.ToString();
            switch(source.FrontendInput)
            {
                case "media_image":
                    destination.Type = Connect.Enums.PropertyTypes.Image;
                    break;
                case "price":
                    destination.Type = Connect.Enums.PropertyTypes.Price;
                    break;
                case "text":
                    destination.Type = Connect.Enums.PropertyTypes.TextLine;
                    break;
                case "textarea":
                    destination.Type = source.IsWYSIWYG ? Connect.Enums.PropertyTypes.RichText : Connect.Enums.PropertyTypes.Text;
                    break;
                case "date":
                    destination.Type = Connect.Enums.PropertyTypes.Date;
                    break;
                case "boolean":
                    destination.Type = Connect.Enums.PropertyTypes.Boolean;
                    break;
                default:
                    this.logger.Warning($"Unable to map {source.FrontendInput} to any of the supported types. Product Attribute Id = {source.Id}");
                    break;
            }
            return destination;
        }

        public ApplyGiftCardData MapGiftCardAccountData(string giftCardCode)
        {
            return new ApplyGiftCardData()
            {
                GiftCardAccountData = new GiftCardAccountData()
                {
                    GiftCardCodes = new List<string>() { giftCardCode }
                }
            };
        }

        public Commerce.Models.Product.ProductStore MapProductStore(StoreGroup storeGroup, StoreConfig storeConfig)
        {
            return new Commerce.Models.Product.ProductStore()
            {
                ExternalId = storeGroup.Code,
                StoreId = storeConfig.Id,
                WebsiteId = storeConfig.WebsiteId,
                StoreName = storeGroup.Name,
                CurrencyCode = storeConfig.BaseCurrencyCode,
            };
        }

        public Commerce.Models.Product.CustomerGroup Map(MagentoApi.Models.Customer.CustomerGroup source, Commerce.Models.Product.CustomerGroup destination = null)
        {
            destination = destination ?? new Commerce.Models.Product.CustomerGroup();
            destination.Code = source.Code;
            destination.ExternalId = source.Id;
            return destination;
        }

        public Commerce.Models.Product.ProductAttribute MapProductAttribute(ProductAttribute source, Commerce.Models.Product.ProductAttribute destination = null)
        {
            destination = destination ?? new Commerce.Models.Product.ProductAttribute();
            if(source.Options != null)
            {
                destination.ProductAttributeValues = source.Options.Select(_ => this.Map(source.Code, _)).ToList();
            }
            destination.Name = source.Name;
            destination.ExternalId = source.Id.ToString();
            destination.Code = source.Code;
            return destination;
        }

        public Commerce.Models.Product.ProductAttributeValue Map(string parentId, ProductAttributeOption source, Commerce.Models.Product.ProductAttributeValue destination = null)
        {
            destination = destination ?? new Commerce.Models.Product.ProductAttributeValue();
            destination.Label = source.Label;
            destination.ExternalId = source.Value;
            destination.ExternalParentId = parentId;
            return destination;
        }

        public CustomDataItem MapCustomDataRequest(IList<Commerce.Models.Order.CustomData> customData)
        {
            return new CustomDataItem
            {
                CustomData = customData != null ? JsonConvert.SerializeObject(customData) : null
            };
        }

        public AddCartLineGiftRecipient MapGiftRecipient(CartLineGiftRecipient source, AddCartLineGiftRecipient destination = null)
        {
            if(source == null) return null;

            destination = destination ?? new AddCartLineGiftRecipient();

            destination.EmailAddress = source.EmailAddress;
            destination.RegionCode = source.RegionCode;
            destination.CountryId = source.CountryCode;
            destination.City = source.City;
            destination.PostCode = source.PostalCode;
            destination.Telephone = source.PhoneNumber;
            destination.FirstName = source.FirstName;
            destination.LastName = source.LastName;
            destination.Street = (new List<string> { source.Street1, source.Street2 })
                .Where(item => !string.IsNullOrEmpty(item))
                .ToArray();
            destination.Note = source.Note;

            return destination;
        }

        public CartLineGiftRecipient  MapGiftRecipient(AddCartLineGiftRecipient source, CartLineGiftRecipient  destination = null)
        {
            if(source == null) return null;

            destination = destination ?? new CartLineGiftRecipient();
            destination.EmailAddress = source.EmailAddress;
            destination.RegionCode = source.RegionCode;
            destination.CountryCode = source.CountryId;
            destination.City = source.City;
            destination.PostalCode = source.PostCode;
            destination.PhoneNumber = source.Telephone;
            destination.FirstName = source.FirstName;
            destination.LastName = source.LastName;
            destination.Street1 = source.Street.ElementAtOrDefault(0);
            destination.Street2 = source.Street.ElementAtOrDefault(1);
            destination.Note = source.Note;

            return destination;
        }

        public SubmitFastOrderModel MapFreeProductActivationData(FastOrderData freeProductActivationData)
        {
            return new SubmitFastOrderModel
            {
                CartItems = new List<FastOrderProductItem>
                {
                    new FastOrderProductItem
                    {
                       Sku = freeProductActivationData.Sku,
                       Qty = freeProductActivationData.Qty
                    }
                }
            };
        }

        private decimal SetDiscountAmountForBundleProduct(decimal discountAmount, decimal bundleDiscountAmount)
        {
            if(discountAmount == default(decimal))
            {
                return bundleDiscountAmount;
            }
            return discountAmount;
        }
    }
}
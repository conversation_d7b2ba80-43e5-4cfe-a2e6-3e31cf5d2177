﻿// ReSharper disable CollectionNeverUpdated.Local

#pragma warning disable 649

namespace Pmi.Spx.Project.Functional.Tests.Pages
{
    using System.Collections.Generic;
    using System.Linq;
    using FluentAssertions;
    using OpenQA.Selenium;
    using OpenQA.Selenium.Support.Extensions;
    using OpenQA.Selenium.Support.UI;
    using Pmi.Spx.Project.Functional.Tests.Models;
    using Pmi.Web.Ui.Framework.Extensions;
    using Pmi.Web.Ui.Framework.Page;
    using Amount = Currency.Amount;

    public class DonationPage : BasePage<DonationPage>
    {
        private IWebElement AddDonationToCartButton => WebDriver.FindElement(By.XPath("//span[contains(text(),'Cart')]/.. | //button[contains(text(),'Cart')]"));

        private IList<IWebElement> DonationList => WebDriver.FindElements(By.XPath("//h2[contains(text(),'Donation Amount')]/following-sibling::div/div[contains(@class, 'cursor-pointer')]/span"));

        private IWebElement OtherDonation => WebDriver.FindElement(By.XPath("//input[@placeholder='Enter donation Amount'] | //input[@placeholder='Enter other amount']"));

        public IList<IWebElement> DonationSection => WebDriver.FindElements(By.XPath("//*[text()='Donation Amount']"));

        public IWebElement DonationBadge => WebDriver.FindElement(By.XPath("//div[contains(@class,'flex-wrap')]//div[text()='Donation']"));

        public IWebElement DonationProduct => WebDriver.FindElement(By.XPath("//a[contains(text(),'PMI Educational Foundation (PMIEF')]"));

        private IWebElement Header => WebDriver.FindElement(By.XPath(".//h1"));

        private IWebElement DefaultDonation => WebDriver.FindElement(By.XPath("//div[@class='product-donation__form-item']//label[contains(@for,'-0')]"));

        private bool OtherDonationDisplayed() => Extensions.CatchUnavailableElement(() => OtherDonation.Displayed, false);

        private bool DonationBadgeDisplayed() => Extensions.CatchUnavailableElement(() => DonationBadge.Displayed, false);

        private bool OtherDonationFieldDisplayed() => Extensions.CatchUnavailableElement(() => OtherDonation.Displayed, false);

        private readonly string DefaultDonationScript = "return window.getComputedStyle(arguments[0],'::after').getPropertyValue(\"background-color\");";

        private readonly string DONATION_TITLE = "PMI Educational Foundation (PMIEF) Donation";

        private readonly WebDriverWait _wait;

        private readonly UserSettings _userSettings;

        public DonationPage(IWebDriver driver) : base(driver)
        {
            _userSettings = new UserSettings();
            _wait = new WebDriverWait(WebDriver, _userSettings.DefaultExplicitWaitTimeout);
        }

        public override string BaseUrl => _userSettings.BaseUrl;

        public override string RelativePath => RelativeUrl;

        public static string RelativeUrl => "/donation/pmi-educational-foundation-(pmief)-donation/101479";

        private bool HeaderDisplayed() => Extensions.CatchUnavailableElement(() => Header.Displayed, false);

        private bool DonationProductDisplayed() => Extensions.CatchUnavailableElement(() => DonationProduct.Displayed, false);

        private bool AddDonationToCartButtonDisplayed() => Extensions.CatchUnavailableElement(() => AddDonationToCartButton.Displayed, false);

        public override DonationPage VerifyPageLoaded()
        {
            WebDriver.WaitForAllLoadersInvisible();
            _wait.Until(_ => HeaderDisplayed());
            return base.VerifyPageLoaded();
        }

        public Product AddDonation(string amount)
        {   
            var donationAmount = Currency.ParseAmount(amount);
            _wait.Until(_=> OtherDonationDisplayed());
            ChooseDonation(donationAmount);
            AddDonationToCartButton.ScrollAndClick(WebDriver);
            WebDriver.WaitForAllLoadersInvisible();

            var product = new Product
            {
                Category = Category.Donation,
                Title = DONATION_TITLE,
                RegularPrice = donationAmount,
                CartPrice = donationAmount
            };

            if(_userSettings.Environment.Equals("QA"))
                product.MemberPrice = donationAmount;

            return product;
        }

        private void ChooseDonation(Amount donationAmount)
        {
            _wait.Until(_ => OtherDonationFieldDisplayed());
            if(DonationList.Any(p => p.Text.Contains($"{donationAmount.Value}")))
            {
                var easyDonationSelect =
                    DonationList.First(p => p.Text.Contains($"{donationAmount.Value}"));
                easyDonationSelect.Click(true);
            }
            else
            {
                OtherDonation.Click();
                OtherDonation.EnterText(WebDriver, $"{donationAmount.Value}", $"{donationAmount.Currency}{donationAmount.Value}");
            }
        }

        public DonationPage VerifyDonationButtonDisabled()
        {
            DonationSection.Count.Should().BeLessThanOrEqualTo(0);
            return this;
        }

        public DonationPage VerifyDefaultDonationSelected()
        {
            var RgbValue = WebDriver.ExecuteJavaScript<string>(DefaultDonationScript, DefaultDonation);
            RgbValue.GetHexColorCode().Equals(PmiColourValues.PdpDonationRadioButton.GetEnumDisplayName()).Should().BeTrue();
            return this;
        }
    }
}
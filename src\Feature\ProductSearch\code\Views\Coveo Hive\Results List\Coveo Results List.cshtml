﻿@using Sitecore.Mvc
@using Coveo.UI.Components
@using Coveo.UI.Components.Extensions
@using Pmi.Spx.Foundation.Framework.Services
@using Pmi.Spx.Foundation.Security.Services
@using Pmi.Spx.Foundation.ReactJss.Mvc.Helpers
@using Microsoft.Extensions.DependencyInjection;
@using Pmi.Spx.Foundation.Commerce.Providers
@using Pmi.Spx.Foundation.Framework.FeatureSettings
@using Pmi.Spx.Foundation.Framework.Services.Tooltips
@using Pmi.Spx.Foundation.Security.Constants
@using Pmi.Spx.Foundation.Security.Extensions
@using Pmi.Spx.Foundation.Security.Models.LoginInfoModels

@model Coveo.UI.Components.Models.ResultsList.IResultsListModel

@Html.Coveo().RenderErrorSummary(Model.ValidateModel())
<script src="@AssetMap.Instance.MapSpxAssetName("runtime.js")"></script>
<script src="@AssetMap.Instance.MapSpxAssetName("vendors.js")"></script>
<script src="@AssetMap.Instance.MapSpxAssetName("dsm-react.js")"></script>
<script src="@AssetMap.Instance.MapSpxAssetName("base-client.js")"></script>
<script defer src="@AssetMap.Instance.MapSpxAssetName("mvc-client.js")"></script>

@if (Sitecore.Security.Accounts.User.Current.IsAuthenticated)
{
    var user = Sitecore.DependencyInjection.ServiceLocator.ServiceProvider.GetService<IUserService>();
    var fullName = (user != null) ? user.GetFullName() : String.Empty;

    var localName = Sitecore.Security.Accounts.User.Current.LocalName;
    var userName = Sitecore.Security.Accounts.User.Current.Name.Replace(@"\", @"\\");
    var email = localName;

    var currentMembership = default(MembershipInformation);
    var isLoggedIn = user != null && user.IsUserAuthenticated();
    if (isLoggedIn)
    {
        var loginInfo = user.GetLoginInfo();
        if (loginInfo != null)
        {
            currentMembership = loginInfo.GetCurrentMembershipInfo();
        }
    }
    var membershipType = "";
    var customerGroup = "";
    if (currentMembership != null)
    {
        membershipType = currentMembership.MembershipType;
        customerGroup = currentMembership.GetCustomerGroupName();
    }

    <script>
        //TODO: refactor this:
        window["spx_user"] = { "email": "@email", "fullName": "@fullName", "localName": "@localName", "userName": "@userName", "membership": { "membershipType": "@membershipType", "customerGroup": "@customerGroup" } };
    </script>
}
@{
    var storeProvider = Sitecore.DependencyInjection.ServiceLocator.ServiceProvider.GetService<IStoreProvider>();
    var currentStore = storeProvider.GetCurrentStore();
    if (currentStore != null) {
        <script>
            //TODO: refactor this:
            window["spx_user_store"] = { 'storeId': '@Html.Raw(currentStore.ExternalID)', 'storeName': '@Html.Raw(currentStore.Name)', 'storeResolvingCountry': '@Html.Raw(currentStore.StoreResolvingCountry)' };
        </script>
    }
}
@{
    var messagesService = Sitecore.DependencyInjection.ServiceLocator.ServiceProvider.GetService<IMemberTooltipMessagesService>();
    var messages = messagesService.GetMessages();

    <script>
        //TODO: refactor this:
        window["spx_membershiptooltip"] = { 'loggedInMember': '@Html.Raw(messages.LoggedInMember)', 'loggedOutOrNonMember': '@Html.Raw(messages.LoggedOutOrNonMember)' };
    </script>
}
<div>
    @if (Model.IsConfigured)
    {
        @Html.Partial(Partials.EDIT_TITLE, Model.VisibleText)
        @Html.Partial(Partials.DEBUG_INFORMATION, Model)

        <div id="@Model.Id"
             class="CoveoResultList"
             @foreach (var property in @Model.RawProperties) { @: data-@(property.Key)='@(property.Value)'
             }>
            @Html.Sitecore().CoveoDynamicPlaceholder("coveo-ui-result-templates")
        </div>
        @Html.EnsureStoreState()
    }
</div>
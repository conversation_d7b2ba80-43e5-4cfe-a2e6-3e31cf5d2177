﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\..\..\Build\sitecore-versions.props.user" Condition="Exists('..\..\..\Build\sitecore-versions.props.user')" />
  <Import Project="..\..\..\Build\sitecore-versions.props" Condition="Exists('..\..\..\Build\sitecore-versions.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{CAAB64B3-3ADB-4C71-8516-025D877A86CB}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Pmi.Spx.Foundation.MagentoConnector</RootNamespace>
    <AssemblyName>Pmi.Spx.Foundation.MagentoConnector</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <UseIISExpress>false</UseIISExpress>
    <Use64BitIISExpress />
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <RestoreProjectStyle>PackageReference</RestoreProjectStyle>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <MvcBuildViews>false</MvcBuildViews>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <MvcBuildViews Condition="!Exists('.\razorgenerator.directives')">false</MvcBuildViews>
    <MvcBuildViews Condition="Exists('.\razorgenerator.directives')">true</MvcBuildViews>
    <DebugSymbols>false</DebugSymbols>
    <DebugType>None</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Abstractions" />
    <Reference Include="System.Web.Routing" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.AspNet.Mvc">
      <Version>$(MvcVersion)</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.AspNet.Web.Optimization">
      <Version>1.1.3</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.Azure.ServiceBus">
      <Version>4.1.2</Version>
    </PackageReference>
    <PackageReference Include="RazorGenerator.MsBuild">
      <Version>2.4.7</Version>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>
      </PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.AspNet.Razor">
      <Version>$(WebPageVersion)</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.AspNet.WebPages">
      <Version>$(WebPageVersion)</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.CodeDom.Providers.DotNetCompilerPlatform">
      <Version>3.6.0</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.Owin.Security.OpenIdConnect">
      <Version>4.0.0</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.Web.Infrastructure">
      <Version>1.0.0</Version>
    </PackageReference>
    <PackageReference Include="Sitecore.Kernel">
      <Version>$(SitecoreVersion)</Version>
    </PackageReference>
    <PackageReference Include="Sitecore.Mvc">
      <Version>$(SitecoreVersion)</Version>
    </PackageReference>
    <PackageReference Include="SPXBuildActions">
      <Version>$(SPXBuildActionsVersion)</Version>
    </PackageReference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Configuration\IMagentoConfiguration.cs" />
    <Compile Include="Configuration\MagentoAccessConfiguration.cs" />
    <Compile Include="Configuration\MagentoConfiguration.cs" />
    <Compile Include="Configuration\MagentoConnectorSettings.cs" />
    <Compile Include="Configuration\MagentoGeneralSettings.cs" />
    <Compile Include="Configuration\MagentoResourcesSettings.cs" />
    <Compile Include="Configuration\MagentoRestApiSettings.cs" />
    <Compile Include="Configuration\MagentoServiceBusConnectionSettings.cs" />
    <Compile Include="Constants\MagentoPaymentProperties.cs" />
    <Compile Include="Constants\MagentoProductSyncEventTypes.cs" />
    <Compile Include="Constants\PipelineParameters.cs" />
    <Compile Include="Enums\CredentialTypes.cs" />
    <Compile Include="Exceptions\MagentoApiException.cs" />
    <Compile Include="MagentoApi\Converters\IMagentoAttribute.cs" />
    <Compile Include="MagentoApi\Converters\MagentoAttributesConverter.cs" />
    <Compile Include="MagentoApi\Models\AddCartItem.cs" />
    <Compile Include="MagentoApi\Models\AddCartItems.cs" />
    <Compile Include="MagentoApi\Models\AddCartLineExtensionAttributes.cs" />
    <Compile Include="MagentoApi\Models\AddCartLineGiftRecipient.cs" />
    <Compile Include="MagentoApi\Models\AddCartLineItem.cs" />
    <Compile Include="MagentoApi\Models\AddCartLineItemBundleOption.cs" />
    <Compile Include="MagentoApi\Models\AddCartLineItemExtensionAttributes.cs" />
    <Compile Include="MagentoApi\Models\AddCartLineItemOption.cs" />
    <Compile Include="MagentoApi\Models\AddCartLineItemProductOption.cs" />
    <Compile Include="MagentoApi\Models\AddDonationCartItem.cs" />
    <Compile Include="MagentoApi\Models\AddDonationCartLineItem.cs" />
    <Compile Include="MagentoApi\Models\AddressesInformationExtensionAttributes.cs" />
    <Compile Include="MagentoApi\Models\AddressExtensionAttributes.cs" />
    <Compile Include="MagentoApi\Models\BillToCompanyIdItem.cs" />
    <Compile Include="MagentoApi\Models\BaseCart.cs" />
    <Compile Include="MagentoApi\Models\BundleOption.cs" />
    <Compile Include="MagentoApi\Models\FastOrderProductItem.cs" />
    <Compile Include="MagentoApi\Models\FastOrderResponseItem.cs" />
    <Compile Include="MagentoApi\Models\PreAuth\PreauthOrderCreateRequest.cs" />
    <Compile Include="MagentoApi\Models\PreAuth\PreAuthPaymentMethod.cs" />
    <Compile Include="MagentoApi\Models\Product\CountryPriceExtensionData.cs" />
    <Compile Include="MagentoApi\Models\Product\ProductCountryPriceData.cs" />
    <Compile Include="MagentoApi\Models\Product\Price\ABPrice.cs" />
    <Compile Include="MagentoApi\Models\Product\Price\TierPriceExtensionData.cs" />
    <Compile Include="MagentoApi\Models\Product\ProductZonePriceData.cs" />
    <Compile Include="MagentoApi\Models\Product\Price\TierPrice.cs" />
    <Compile Include="MagentoApi\Models\Product\ZonePriceExtensionData.cs" />
    <Compile Include="MagentoApi\Models\SubmitFastOrderModel.cs" />
    <Compile Include="MagentoApi\Models\SubmitFastOrderResponse.cs" />
    <Compile Include="MagentoApi\Models\GiftRecipientDetails.cs" />
    <Compile Include="MagentoApi\Models\GiftRecipientOrderDetails.cs" />
    <Compile Include="MagentoApi\Models\Shipping.cs" />
    <Compile Include="MagentoApi\Models\ShippingAssignment.cs" />
    <Compile Include="MagentoApi\Models\SubmitOrderAlipay.cs" />
    <Compile Include="MagentoApi\Models\SubmitOrderAdditionalData.cs" />
    <Compile Include="MagentoApi\Models\Address.cs" />
    <Compile Include="MagentoApi\Models\AddressesInformation.cs" />
    <Compile Include="MagentoApi\Models\AddressRequest.cs" />
    <Compile Include="MagentoApi\Models\ApplyGiftCardData.cs" />
    <Compile Include="MagentoApi\Models\Cart.cs" />
    <Compile Include="MagentoApi\Models\CartAttributes.cs" />
    <Compile Include="MagentoApi\Models\CartExtensionAttributes.cs" />
    <Compile Include="MagentoApi\Models\CartGiftCardExtensionAttributes.cs" />
    <Compile Include="MagentoApi\Models\CartGiftCardExtensionAttributesItem.cs" />
    <Compile Include="MagentoApi\Models\CartLineItem.cs" />
    <Compile Include="MagentoApi\Models\CartLineItemBase.cs" />
    <Compile Include="MagentoApi\Models\CartLineItemExtensionAttributes.cs" />
    <Compile Include="MagentoApi\Models\Carts\GetAppliedVoucher.cs" />
    <Compile Include="MagentoApi\Models\Carts\RemoveVoucher.cs" />
    <Compile Include="MagentoApi\Models\PreferredCurrencies.cs" />
    <Compile Include="MagentoApi\Models\TotalsAttributes.cs" />
    <Compile Include="MagentoApi\Models\CartTotalSegment.cs" />
    <Compile Include="MagentoApi\Models\Category.cs" />
    <Compile Include="MagentoApi\Models\CategoryCustomData.cs" />
    <Compile Include="MagentoApi\Models\CategoryLink.cs" />
    <Compile Include="MagentoApi\Models\Currency.cs" />
    <Compile Include="MagentoApi\Models\CustomDataItem.cs" />
    <Compile Include="MagentoApi\Models\Customer\Customer.cs" />
    <Compile Include="MagentoApi\Models\Customer\CustomerGroup.cs" />
    <Compile Include="MagentoApi\Models\Customer\CustomerPrice.cs" />
    <Compile Include="MagentoApi\Models\Errors\MagentoErrorData.cs" />
    <Compile Include="MagentoApi\Models\Errors\MagentoErrorMessage.cs" />
    <Compile Include="MagentoApi\Models\Errors\MagentoErrorMessage2.cs" />
    <Compile Include="MagentoApi\Models\GetProductPrices.cs" />
    <Compile Include="MagentoApi\Models\GiftCardAccountData.cs" />
    <Compile Include="MagentoApi\Models\ListResult.cs" />
    <Compile Include="MagentoApi\Models\MagentoAdditionalInfo.cs" />
    <Compile Include="MagentoApi\Models\MagentoAttribute.cs" />
    <Compile Include="MagentoApi\Models\MagentoEntity.cs" />
    <Compile Include="MagentoApi\Models\MagentoExtensionAttribute.cs" />
    <Compile Include="MagentoApi\Models\Order.cs" />
    <Compile Include="MagentoApi\Models\OrderAddress.cs" />
    <Compile Include="MagentoApi\Models\OrderCustomerAttributes.cs" />
    <Compile Include="MagentoApi\Models\OrderDetails.cs" />
    <Compile Include="MagentoApi\Models\OrderExtensionData.cs" />
    <Compile Include="MagentoApi\Models\OrderGiftCardItem.cs" />
    <Compile Include="MagentoApi\Models\OrderLineItem.cs" />
    <Compile Include="MagentoApi\Models\OrderPayment.cs" />
    <Compile Include="MagentoApi\Models\OrderPaymentAdditionalInfo.cs" />
    <Compile Include="MagentoApi\Models\OrderPaymentExtensionAttributes.cs" />
    <Compile Include="MagentoApi\Models\OrderShippingAddress.cs" />
    <Compile Include="MagentoApi\Models\OrderShippingAssignment.cs" />
    <Compile Include="MagentoApi\Models\OrderTotal.cs" />
    <Compile Include="MagentoApi\Models\OrderTotalExtensionAttributes.cs" />
    <Compile Include="MagentoApi\Models\PaymentMethod.cs" />
    <Compile Include="MagentoApi\Models\PaymentMethodAttributes.cs" />
    <Compile Include="MagentoApi\Models\ProductAttributes\ProductAttribute.cs" />
    <Compile Include="MagentoApi\Models\ProductAttributes\ProductAttributeGroup.cs" />
    <Compile Include="MagentoApi\Models\ProductAttributes\ProductAttributeOption.cs" />
    <Compile Include="MagentoApi\Models\ProductAttributes\ProductAttributeSet.cs" />
    <Compile Include="MagentoApi\Models\ProductPrice.cs" />
    <Compile Include="MagentoApi\Models\ProductPriceResult.cs" />
    <Compile Include="MagentoApi\Models\Product\BundleProductOption.cs" />
    <Compile Include="MagentoApi\Models\Product\Product.cs" />
    <Compile Include="MagentoApi\Models\Product\ProductCustomData.cs" />
    <Compile Include="MagentoApi\Models\Product\ProductExtensionData.cs" />
    <Compile Include="MagentoApi\Models\Product\ProductLink.cs" />
    <Compile Include="MagentoApi\Models\Product\ProductOption.cs" />
    <Compile Include="MagentoApi\Models\Product\ProductStockItem.cs" />
    <Compile Include="MagentoApi\Models\SessionTokenRequest.cs" />
    <Compile Include="MagentoApi\Models\SetAddressesRequest.cs" />
    <Compile Include="MagentoApi\Models\ShippingMethod.cs" />
    <Compile Include="MagentoApi\Models\StoreConfig.cs" />
    <Compile Include="MagentoApi\Models\StoreGroup.cs" />
    <Compile Include="MagentoApi\Models\SubmitOrder.cs" />
    <Compile Include="MagentoApi\Models\SubmitOrderPaymentMethod.cs" />
    <Compile Include="MagentoApi\Models\SubmitOrderPayPal.cs" />
    <Compile Include="MagentoApi\Models\SubmitOrderPayPalData.cs" />
    <Compile Include="MagentoApi\Models\SubmitOrderPO.cs" />
    <Compile Include="MagentoApi\Models\SubmitOrderWorldpay.cs" />
    <Compile Include="MagentoApi\Models\SubmitOrderWorldpayData.cs" />
    <Compile Include="MagentoApi\Models\TokenRequest.cs" />
    <Compile Include="MagentoApi\Models\TotalSegment.cs" />
    <Compile Include="MagentoApi\Models\UpdateCartItem.cs" />
    <Compile Include="MagentoApi\Models\UpdateCartLineItem.cs" />
    <Compile Include="MagentoApi\Providers\AddressApiProvider.cs" />
    <Compile Include="MagentoApi\Providers\CategoryApiProvider.cs" />
    <Compile Include="MagentoApi\Providers\CustomerApiProvider.cs" />
    <Compile Include="MagentoApi\Providers\CustomerCartApiProvider.cs" />
    <Compile Include="MagentoApi\Providers\CustomerGroupsApiProvider.cs" />
    <Compile Include="MagentoApi\Providers\CustomerOrderApiProvider.cs" />
    <Compile Include="MagentoApi\Providers\CustomerPaymentMethodApiProvider.cs" />
    <Compile Include="MagentoApi\Providers\CustomerProductPriceApiProvider.cs" />
    <Compile Include="MagentoApi\Providers\IAddressesApiProvider.cs" />
    <Compile Include="MagentoApi\Providers\ICategoryApiProvider.cs" />
    <Compile Include="MagentoApi\Providers\ICustomerApiProvider.cs" />
    <Compile Include="MagentoApi\Providers\ICustomerCartApiProvider.cs" />
    <Compile Include="MagentoApi\Providers\ICustomerGroupsApiProvider.cs" />
    <Compile Include="MagentoApi\Providers\ICustomerOrderApiProvider.cs" />
    <Compile Include="MagentoApi\Providers\ICustomerPaymentMethodApiProvider.cs" />
    <Compile Include="MagentoApi\Providers\ICustomerProductPriceApiProvider.cs" />
    <Compile Include="MagentoApi\Providers\IPreferredCurrenciesApiProvider.cs" />
    <Compile Include="MagentoApi\Providers\IProductApiProvider.cs" />
    <Compile Include="MagentoApi\Providers\IProductAttributeSetApiProvider.cs" />
    <Compile Include="MagentoApi\Providers\IProductStoreApiProvider.cs" />
    <Compile Include="MagentoApi\Providers\IShippingMethodsApiProvider.cs" />
    <Compile Include="MagentoApi\Providers\PreferredLocalCurrenciesApiProvider.cs" />
    <Compile Include="MagentoApi\Providers\ProductApiProvider.cs" />
    <Compile Include="MagentoApi\Providers\ProductAttributeSetApiProvider.cs" />
    <Compile Include="MagentoApi\Providers\ProductStoreApiProvider.cs" />
    <Compile Include="MagentoApi\Providers\ShippingMethodsApiProvider.cs" />
    <Compile Include="MagentoApi\Search\AndCondition.cs" />
    <Compile Include="MagentoApi\Search\Condition.cs" />
    <Compile Include="MagentoApi\Search\ConditionBase.cs" />
    <Compile Include="MagentoApi\Search\ConditionTypes.cs" />
    <Compile Include="MagentoApi\Search\OrCondition.cs" />
    <Compile Include="MagentoApi\Search\SearchCriteria.cs" />
    <Compile Include="MagentoApi\Services\ApiErrorsService.cs" />
    <Compile Include="MagentoApi\Services\ApiService.cs" />
    <Compile Include="MagentoApi\Services\CachedTokenService.cs" />
    <Compile Include="MagentoApi\Services\ConditionBuilder.cs" />
    <Compile Include="MagentoApi\Services\IApiErrorsService.cs" />
    <Compile Include="MagentoApi\Services\IApiService.cs" />
    <Compile Include="MagentoApi\Services\ICachedTokenService.cs" />
    <Compile Include="MagentoApi\Services\IConditionBuilder.cs" />
    <Compile Include="MagentoApi\Services\IdentityTokenService.cs" />
    <Compile Include="MagentoApi\Services\IListApiService.cs" />
    <Compile Include="MagentoApi\Services\ISearchCriteriaService.cs" />
    <Compile Include="MagentoApi\Services\ITokenService.cs" />
    <Compile Include="MagentoApi\Services\ITokenServiceFactory.cs" />
    <Compile Include="MagentoApi\Services\ListApiService.cs" />
    <Compile Include="MagentoApi\Models\MagentoIdentityToken.cs" />
    <Compile Include="MagentoApi\Services\MagentoTokenService.cs" />
    <Compile Include="MagentoApi\Services\SearchCriteriaService.cs" />
    <Compile Include="MagentoApi\Services\TokenServiceFactory.cs" />
    <Compile Include="MagentoProviders\CustomerCartProvider.cs" />
    <Compile Include="MagentoProviders\CustomerOrderProvider.cs" />
    <Compile Include="MagentoProviders\CustomerProvider.cs" />
    <Compile Include="MagentoProviders\ICustomerCartProvider.cs" />
    <Compile Include="MagentoProviders\ICustomerOrderProvider.cs" />
    <Compile Include="MagentoProviders\ICustomerProvider.cs" />
    <Compile Include="MagentoProviders\IProductProvider.cs" />
    <Compile Include="MagentoProviders\IProductStoreProvider.cs" />
    <Compile Include="MagentoProviders\IProductTypeProvider.cs" />
    <Compile Include="MagentoProviders\ProductProvider.cs" />
    <Compile Include="MagentoProviders\ProductStoreProvider.cs" />
    <Compile Include="MagentoProviders\ProductTypeProvider.cs" />
    <Compile Include="Models\ServiceBusModels\ProductSyncModel.cs" />
    <Compile Include="Pipelines\Carts\AddCartLines\AddMagentoCartLines.cs" />
    <Compile Include="Pipelines\Carts\AddCustomData\AddMagentoCustomData.cs" />
    <Compile Include="Pipelines\Carts\ApplyCoupon\ApplyMagentoCoupon.cs" />
    <Compile Include="Pipelines\Carts\ApplyDonation\ApplyMagentoDonation.cs" />
    <Compile Include="Pipelines\Carts\ApplyGiftCard\ApplyMagentoGiftCard.cs" />
    <Compile Include="Pipelines\Carts\ApplyVoucher\ApplyMagentoVoucher.cs" />
    <Compile Include="Pipelines\Carts\LoadCart\LoadMagentoCart.cs" />
    <Compile Include="Pipelines\Carts\LookupCarts\LookupMagentoCarts.cs" />
    <Compile Include="Pipelines\Carts\RemoveCartLines\RemoveLinesFromMagentoCart.cs" />
    <Compile Include="Pipelines\Carts\RemoveCoupon\RemoveMagentoCoupon.cs" />
    <Compile Include="Pipelines\Carts\RemoveVoucher\RemoveMagentoVoucher.cs" />
    <Compile Include="Pipelines\Carts\SetBillToCompanyId\SetBillToCompanyIdOnMagentoCart.cs" />
    <Compile Include="Pipelines\Carts\SetShippingAddress\SetMagentoShippingAddress.cs" />
    <Compile Include="Pipelines\Carts\UpdateCartLines\UpdateLinesOnMagentoCart.cs" />
    <Compile Include="Pipelines\Carts\UpdateCurrencyPreference\UpdateMagentoCurrencyPreference.cs" />
    <Compile Include="Pipelines\Customers\LookupCustomers\LookupMagentoCustomers.cs" />
    <Compile Include="Pipelines\Initialize\InitializeServiceBusMessageHandler.cs" />
    <Compile Include="Pipelines\Inventory\GetStockInformation\GetMagentoStockInformation.cs" />
    <Compile Include="Pipelines\Orders\ConvertVisitorOrder\ConvertQuoteToOrder.cs" />
    <Compile Include="Pipelines\Orders\GetVisitorOrder\GetMagentoVisitorOrder.cs" />
    <Compile Include="Pipelines\Orders\SubmitVisitorOrder\SubmitMagentoVisitorOrder.cs" />
    <Compile Include="Pipelines\Payments\GetPaymentMethods\GetMagentoPaymentMethods.cs" />
    <Compile Include="Pipelines\Payments\TransactionIdentifier\UpdateTransactionIdentifier.cs" />
    <Compile Include="Pipelines\Products\GetCustomerGroupProductPrice\GetMagentoCustomerGroupProductPrice.cs" />
    <Compile Include="Pipelines\Products\GetExternalCommerceSystemProductList\AppendMagentoMasterStoreProductList.cs" />
    <Compile Include="Pipelines\Products\GetExternalCommerceSystemProductList\GetMagentoProductList.cs" />
    <Compile Include="Pipelines\Products\GetExternalCommerceSystemProductList\GetMagentoStoreProductList.cs" />
    <Compile Include="Pipelines\Products\QueryProductStore\QueryMagentoProductStore.cs" />
    <Compile Include="Pipelines\Products\SynchronizeClassifications\ReadMagentoClassifications.cs" />
    <Compile Include="Pipelines\Products\SynchronizeCustomerGroups\ReadMagentoCustomerGroups.cs" />
    <Compile Include="Pipelines\Products\SynchronizeProductAttributes\ReadMagentoProductAttributes.cs" />
    <Compile Include="Pipelines\Products\SynchronizeProductEntity\ReadMagentoProduct.cs" />
    <Compile Include="Pipelines\Products\SynchronizeStores\ReadMagentoStores.cs" />
    <Compile Include="Pipelines\Products\SynchronizeTypes\ReadMagentoTypeMetadata.cs" />
    <Compile Include="Pipelines\Products\SynchronizeTypes\ReadMagentoTypes.cs" />
    <Compile Include="Pipelines\Shipping\GetShippingMethods\EnforceZeroShippingMethod.cs" />
    <Compile Include="Pipelines\Shipping\GetShippingMethods\GetMagentoShippingMethods.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="..\..\..\Build\Version.cs">
      <Link>Properties\Version.cs</Link>
    </Compile>
    <Compile Include="RegisterDependencies.cs" />
    <Compile Include="Services\ConnectorMapper.cs" />
    <Compile Include="Services\IConnectorMapper.cs" />
    <Compile Include="Services\IProductBatchReader.cs" />
    <Compile Include="Services\IProductBatchReaderFactory.cs" />
    <Compile Include="Services\ProductBatchReader.cs" />
    <Compile Include="Services\ProductBatchReaderFactory.cs" />
    <Compile Include="SitecoreModels.Extended.cs" />
    <Compile Include="SitecoreModels.Generated.cs" />
    <Compile Include="SitecoreSettings\IMagentoSitecoreSettingsProvider.cs" />
    <Compile Include="SitecoreSettings\MagentoSitecoreSettingsProvider.cs" />
    <Compile Include="SitecoreSettings\Models\QueryParameterModel.cs" />
    <Compile Include="SitecoreSettings\Models\StoreSettingsModel.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="App_Config\Include\Foundation\SPX\Foundation.MagentoConnector.config" />
    <Content Include="App_Config\Include\Foundation\SPX\z-Foundation.MagentoConnector.ENV.config" />
    <None Include="Web.config" />
    <None Include="razorgenerator.directives.disabled" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Commerce\code\Pmi.Spx.Foundation.Commerce.csproj">
      <Project>{4cd01a37-76d8-4a6a-8822-014369c4243d}</Project>
      <Name>Pmi.Spx.Foundation.Commerce</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Connect\code\Pmi.Spx.Foundation.Connect.csproj">
      <Project>{60cd1765-857b-4bc8-831e-7526264f1ec5}</Project>
      <Name>Pmi.Spx.Foundation.Connect</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\CRM\code\Pmi.Spx.Foundation.CRM.csproj">
      <Project>{88184E35-5DBE-4246-A741-54399E87405E}</Project>
      <Name>Pmi.Spx.Foundation.CRM</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Framework\code\Pmi.Spx.Foundation.Framework.csproj">
      <Project>{32c6aa3c-2163-4eaa-965e-b5df329bd05b}</Project>
      <Name>Pmi.Spx.Foundation.Framework</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\GeoIP\code\Pmi.Spx.Foundation.GeoIP.csproj">
      <Project>{40084298-92C3-424D-AA45-F27AA858AB75}</Project>
      <Name>Pmi.Spx.Foundation.GeoIP</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\GlassMapper\code\Pmi.Spx.Foundation.GlassMapper.csproj">
      <Project>{0044b0ca-abe2-4365-b1b2-1e347993b4aa}</Project>
      <Name>Pmi.Spx.Foundation.GlassMapper</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Payment\code\Pmi.Spx.Foundation.Payment.csproj">
      <Project>{e1a74f35-b944-44e7-942d-47f654f4b9fd}</Project>
      <Name>Pmi.Spx.Foundation.Payment</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Profile\code\Pmi.Spx.Foundation.Profile.csproj">
      <Project>{0611ec87-85c8-41ac-8606-d1ee82a059ba}</Project>
      <Name>Pmi.Spx.Foundation.Profile</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Security\code\Pmi.Spx.Foundation.Security.csproj">
      <Project>{f25e8dda-07c1-43c0-aa8c-fd3b6008ac89}</Project>
      <Name>Pmi.Spx.Foundation.Security</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup />
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <Target Name="MvcBuildViews" AfterTargets="AfterBuild" Condition="'$(MvcBuildViews)'=='true'">
    <AspNetCompiler VirtualPath="temp" PhysicalPath="$(WebProjectOutputDir)" />
  </Target>
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>False</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>20924</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:20924/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>True</UseCustomServer>
          <CustomServerUrl>https://sc911.local</CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <!--System.Web.Mvc this line only exists so that razorgenerator picks this up as an mvc project -->
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target> -->
</Project>
﻿ @local @browser:Chrome-Headless @desktop
Feature: MembershipPurchaseTests
	Ensure user is able to add a membership to cart and place an order
	Background: 
	Given an empty cart
	
@IndiaStore @Qa @Can @Adyen
Scenario: Buy student membership in India store
    
	Given a newly registered user from data scaffolding is logged in
	When Add digital product to the cart via SKU's
	 And Add student membership to the cart
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Proceed to checkout from cart
	 And Fill out contact information
	 And Fill out billing address and not save for future use
    Then Verify the order summary on the checkout page
	
	When Place RazorPay UPI for Non-Subscription product
	Then Verify order confirmation page
	 
@IndiaStore @Qa @Can @Adyen
Scenario: Buy individual membership with chapter in India store

	Given a newly registered user from data scaffolding is logged in
	When Add digital product to the cart via SKU's
	 And Add Single membership to the cart and change the chapter
	 And Apply promo code on the cart page
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Proceed to checkout from cart
	 And Fill out contact information
	 And Fill out billing address and not save for future use
	Then Verify the order summary on the checkout page
	
	When Place RazorPay Credit card order for Subscription product
	Then Verify order confirmation page

#@Qa data missing
@IndiaStore @Can @Adyen
Scenario: Verify product summary for certification product with Voucher in India store

	Given the user is logged in
	When Add PMP certification to the cart
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Proceed to checkout from cart
	 And Fill out contact information
	 And Fill out billing address and not save for future use
	 And Apply voucher on the cart page
	Then Verify the order summary on the checkout page

@IndiaStore @Can @Adyen
Scenario: Verify product summary for Student Membership with voucher in India store
   
	Given a newly registered user from data scaffolding is logged in
	When Add digital product to the cart via SKU's
	 And Add student membership to the cart
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Proceed to checkout from cart
	 And Fill out contact information
	 And Fill out billing address and not save for future use
	 And Apply voucher on the cart page
	Then Verify the order summary on the checkout page
		
@IndiaStore @Qa @Can @Adyen
Scenario: Buy course and individual membership with chapter using promo code in India store

	Given a newly registered user from data scaffolding is logged in
	When Add digital product to the cart via SKU's
	 And Add Single membership to the cart
	Then Verify products on the cart page
	 And Verify the order summary on the cart page

	When Proceed to checkout from cart
	 And Fill out contact information
	 And Fill out billing address and not save for future use
	 And Apply promo code on the cart page
	Then Verify the order summary on the checkout page

	When Place RazorPay UPI for Subscription product
	Then Verify order confirmation page
		
@BrazilStore @Qa @Can
Scenario: Verify product summary for individual membership with voucher in Brazil store

   Given a newly registered user from data scaffolding is logged in
	When Add membership to the cart via SKU's
	 And Remove all chapter products from the cart
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Proceed to checkout from cart
	 And Fill out contact information
	 And Fill out billing address and save for future use
	 And Update creditcard payment details and save for future use
	 And Apply voucher on the cart page
	Then Verify the order summary on the checkout page
					
@BrazilStore @Qa @Can
Scenario: Buy course and individual membership with chapter using promo code in Brazil store

	Given a newly registered user from data scaffolding is logged in
	When Add digital product to the cart via SKU's
	 And Add membership to the cart
	 And Add chapter from presumptive chapter list
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Proceed to checkout from cart
	 And Fill out contact information
	 And Fill out billing address and save for future use
	 And Update creditcard payment details and save for future use
	Then Verify products on order summary in checkout page
	
	When Place worldpay creditcard order
	Then Verify order confirmation page

@Ignore @MainStore @Qa @Can
Scenario: Validate the cart for individual membership and subscription product in Main store

	Given a newly registered user is logged in
	When Add membership to the cart from the details page
	 And Add subscription product to the cart
	Then Verify products on the cart page
	 And Verify the order summary on the cart page

@IndiaStore @Can @Qa @Adyen
Scenario: Buy course and single membership with newly updated chapter in India store

	Given a newly registered user from data scaffolding is logged in
	When Add digital product to the cart via SKU's
	 And Add Single membership to the cart and change the chapter
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Proceed to checkout from cart
	 And Fill out contact information
	 And Fill out billing address and not save for future use
	Then Verify the order summary on the checkout page
	
	When Place RazorPay Credit card order for Subscription product
	Then Verify order confirmation page

@BrazilStore @Qa @Can
Scenario: Validate individual membership with chapter on Brazil store
 
	Given a newly registered user from data scaffolding is logged in
	When Add digital product to the cart via SKU's
	 And Add membership to the cart
	 And Add chapter from presumptive chapter list
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Proceed to checkout from cart
	 And Fill out contact information
	 And Fill out billing address and save for future use
	 And Update creditcard payment details and save for future use
	Then Verify products on order summary in checkout page
	
	When Place worldpay creditcard order
	Then Verify order confirmation page

@BrazilStore @Qa @Can
Scenario: Validate student membership purchase in Brazil store
 
	Given a newly registered user from data scaffolding is logged in
	When Add digital product to the cart via SKU's
	 And Add student membership to the cart
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Proceed to checkout from cart
	 And Fill out contact information
	 And Fill out billing address and save for future use
	 And Update creditcard payment details and save for future use
	Then Verify products on order summary in checkout page
	
	When Place worldpay creditcard order
	Then Verify order confirmation page

@IndiaStore @Can @Qa
Scenario: Validate individual membership and course with promo on India store

	Given a newly registered user from data scaffolding is logged in
	When Add digital product to the cart via SKU's
	 And Add Single membership to the cart and change the chapter
	 And Apply promo code on the cart page
	Then Verify products on the cart page
	 And Verify the order summary on the cart page

@IndiaStore @Can @Qa @Adyen
Scenario: Validate promo tooltip message for individual membership and course on India store

	Given a newly registered user from data scaffolding is logged in
	When Add digital product to the cart via SKU's
	 And Add Single membership to the cart and change the chapter
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Proceed to checkout from cart
	 And Fill out contact information
	 And Fill out billing address and not save for future use 
	Then Verify the order summary on the checkout page	 

@IndiaStore @Can @Qa @Adyen
Scenario: Validate course and single membership with newly updated chapter in India store

	Given a newly registered user from data scaffolding is logged in
	When Add digital product to the cart via SKU's
	 And Add Single membership to the cart and change the chapter
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	When Proceed to checkout from cart
	 And Fill out contact information
	 And Fill out billing address and not save for future use 
	Then Verify the order summary on the checkout page

@IndiaStore @Prod @Adyen
Scenario: Validate course and single membership with newly updated chapter in India store in Prod

	Given the user is logged in
	 And the cart is empty
	When Add course to the cart
	 And Add Single membership to the cart and change the chapter
	Then Verify products on the cart page
	 And Verify the order summary on the cart page
	
	When Proceed to checkout from cart
	 And Fill out contact information
	 And Fill out billing address and not save for future use
	 And Update Adyen creditcard payment details and not save for future use 
	Then Verify the order summary on the checkout page

@MainStore @Qa @Can
Scenario: Verify Access to Subscription Products for Member Users

	Given the user is logged in
	 When Navigate to Subscription PDP
	 Then Verify Subscription status on the Subscription Detail Page